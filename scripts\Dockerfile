# Use Node.js 18 LTS as base image
FROM node:18-slim

# Install dependencies for Puppeteer
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    ca-certificates \
    procps \
    libxss1 \
    && wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && sh -c 'echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list' \
    && apt-get update \
    && apt-get install -y google-chrome-stable fonts-ipafont-gothic fonts-wqy-zenhei fonts-thai-tlwg fonts-kacst fonts-freefont-ttf libxss1 \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install Node.js dependencies
RUN npm ci --only=production

# Copy application files
COPY . .

# Make scripts executable
RUN chmod +x filfox-scraper.js scheduler.js test-scraper.js

# Set timezone to Tokyo
ENV TZ=Asia/Tokyo

# Create a non-root user
RUN groupadd -r scraper && useradd -r -g scraper -G audio,video scraper \
    && mkdir -p /home/<USER>/Downloads \
    && chown -R scraper:scraper /home/<USER>
    && chown -R scraper:scraper /app

# Switch to non-root user
USER scraper

# Expose port (if needed for health checks)
EXPOSE 3000

# Default command
CMD ["node", "scheduler.js"]
