{"ast": null, "code": "import React,{useState,useEffect}from'react';import{<PERSON><PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,Spinner,<PERSON><PERSON>,But<PERSON>,Form}from'react-bootstrap';import{FaChevronDown,FaChevronRight,FaUser,FaUsers,FaExpandArrowsAlt,FaCompressArrowsAlt,FaSearch}from'react-icons/fa';import{getSupabase}from'../../supabaseClient';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Recommend=()=>{const{t}=useTranslation();const[loading,setLoading]=useState(true);const[referralTree,setReferralTree]=useState([]);const[filteredTree,setFilteredTree]=useState([]);const[expandedNodes,setExpandedNodes]=useState(new Set());const[error,setError]=useState(null);const[searchTerm,setSearchTerm]=useState('');useEffect(()=>{const fetchReferralData=async()=>{const supabase=getSupabase();if(!supabase)return;setLoading(true);setError(null);let currentUser=null;try{const{data:{user}}=await supabase.auth.getUser();currentUser=user;if(!user){setError(t('user_not_logged_in'));setLoading(false);return;}// First, get the agent's maker_id to determine scope\nconst{data:agentProfile,error:agentError}=await supabase.from('agent_profiles').select('maker_id').eq('user_id',user.id).single();if(agentError){console.error('Error fetching agent profile:',agentError);setError(t('agent_profile_not_found'));setLoading(false);return;}// For agent view, we'll fetch users related to this agent's customers\n// First get all customers under this agent (limit to prevent large queries)\nconst{data:customers,error:customersError}=await supabase.from('customer_profiles').select(`\n                        user_id,\n                        users (\n                            id,\n                            email,\n                            referred_by,\n                            created_at,\n                            role\n                        )\n                    `).eq('agent_id',user.id).limit(100);// Limit to prevent large queries\nif(customersError){console.error('Error fetching customers:',customersError);setError(t('failed_to_load_referral_data'));setLoading(false);return;}// Extract user data from customers\nconst customerUsers=(customers===null||customers===void 0?void 0:customers.map(c=>c.users).filter(Boolean))||[];// Get referrer information recursively (up to 3 levels to avoid infinite loops)\nconst fetchReferrers=async function(userIds){let level=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;let maxLevel=arguments.length>2&&arguments[2]!==undefined?arguments[2]:3;if(level>=maxLevel||userIds.length===0){return[];}try{const{data:referrerData,error:referrerError}=await supabase.from('users').select('id, email, referred_by, created_at, role').in('id',userIds).limit(50);// Limit each level to prevent large queries\nif(referrerError){console.error(`Error fetching referrers at level ${level}:`,referrerError);return[];}const referrers=referrerData||[];// Get next level referrer IDs\nconst nextLevelIds=[...new Set(referrers.map(u=>u.referred_by).filter(Boolean))];// Recursively fetch next level\nconst nextLevelReferrers=await fetchReferrers(nextLevelIds,level+1,maxLevel);return[...referrers,...nextLevelReferrers];}catch(error){console.error(`Error in fetchReferrers at level ${level}:`,error);return[];}};// Get unique referred_by IDs from customers\nconst initialReferrerIds=[...new Set(customerUsers.map(u=>u.referred_by).filter(Boolean))];const referrers=await fetchReferrers(initialReferrerIds);// Combine customer users and their referrers\nconst allUsers=[...customerUsers,...referrers];// Remove duplicates based on user ID\nconst uniqueUsers=allUsers.filter((user,index,self)=>index===self.findIndex(u=>u.id===user.id));// Build the referral tree\nconst tree=buildReferralTree(uniqueUsers);setReferralTree(tree);setFilteredTree(tree);}catch(err){console.error('Error:',err);// Fallback: try to get just the direct customers without referrer data\ntry{var _currentUser;console.log('Attempting fallback query...');// Use currentUser if available, otherwise try to get user again\nconst userIdToUse=(_currentUser=currentUser)===null||_currentUser===void 0?void 0:_currentUser.id;if(!userIdToUse){const{data:{user:fallbackUser}}=await supabase.auth.getUser();if(!fallbackUser){setError(t('user_not_logged_in'));return;}currentUser=fallbackUser;}const{data:simpleCustomers,error:simpleError}=await supabase.from('customer_profiles').select(`\n                            users (\n                                id,\n                                email,\n                                created_at,\n                                role\n                            )\n                        `).eq('agent_id',currentUser.id).limit(50);// Further limit for fallback\nif(!simpleError&&simpleCustomers&&simpleCustomers.length>0){const simpleUsers=simpleCustomers.map(c=>c.users).filter(Boolean).map(u=>({...u,referred_by:null,children:[]}));// Remove referral info for simplicity\nconsole.log('Fallback successful, loaded',simpleUsers.length,'users');setReferralTree(simpleUsers);setFilteredTree(simpleUsers);setError(t('limited_referral_data'));}else{console.log('Fallback failed or no data:',simpleError);setReferralTree([]);setFilteredTree([]);setError(t('no_referral_data'));}}catch(fallbackErr){console.error('Fallback error:',fallbackErr);setReferralTree([]);setFilteredTree([]);setError(t('unexpected_error'));}}finally{setLoading(false);}};fetchReferralData();},[t]);// Filter tree based on search term\nuseEffect(()=>{if(!searchTerm.trim()){setFilteredTree(referralTree);return;}const filterTree=nodes=>{return nodes.filter(node=>{const matchesSearch=node.email.toLowerCase().includes(searchTerm.toLowerCase())||node.id.toLowerCase().includes(searchTerm.toLowerCase());const filteredChildren=node.children?filterTree(node.children):[];return matchesSearch||filteredChildren.length>0;}).map(node=>({...node,children:node.children?filterTree(node.children):[]}));};setFilteredTree(filterTree(referralTree));},[searchTerm,referralTree]);const buildReferralTree=users=>{if(!users||users.length===0){return[];}const userMap=new Map();const rootUsers=[];// Create a map of all users\nusers.forEach(user=>{userMap.set(user.id,{...user,children:[]});});// Build the tree structure\nusers.forEach(user=>{if(user.referred_by){const parent=userMap.get(user.referred_by);if(parent){parent.children.push(userMap.get(user.id));}else{// Parent not found, treat as root\nrootUsers.push(userMap.get(user.id));}}else{// No referrer, this is a root user\nrootUsers.push(userMap.get(user.id));}});return rootUsers;};const toggleNode=userId=>{const newExpanded=new Set(expandedNodes);if(newExpanded.has(userId)){newExpanded.delete(userId);}else{newExpanded.add(userId);}setExpandedNodes(newExpanded);};const expandAll=()=>{const allNodeIds=new Set();const collectNodeIds=nodes=>{nodes.forEach(node=>{if(node.children&&node.children.length>0){allNodeIds.add(node.id);collectNodeIds(node.children);}});};collectNodeIds(filteredTree);setExpandedNodes(allNodeIds);};const collapseAll=()=>{setExpandedNodes(new Set());};const formatUserId=id=>{return id.substring(0,8);};const getRoleIcon=role=>{switch(role){case'maker':return/*#__PURE__*/_jsx(FaUsers,{className:\"text-primary me-1\"});case'agent':return/*#__PURE__*/_jsx(FaUser,{className:\"text-success me-1\"});case'customer':return/*#__PURE__*/_jsx(FaUser,{className:\"text-info me-1\"});default:return/*#__PURE__*/_jsx(FaUser,{className:\"text-secondary me-1\"});}};const renderTreeNode=function(node){let level=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;const hasChildren=node.children&&node.children.length>0;const isExpanded=expandedNodes.has(node.id);const paddingLeft=level*20;return/*#__PURE__*/_jsxs(\"div\",{className:\"mb-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center p-2 border-bottom\",style:{paddingLeft:`${paddingLeft}px`,cursor:hasChildren?'pointer':'default'},onClick:()=>hasChildren&&toggleNode(node.id),children:[hasChildren?isExpanded?/*#__PURE__*/_jsx(FaChevronDown,{className:\"me-2 text-muted\"}):/*#__PURE__*/_jsx(FaChevronRight,{className:\"me-2 text-muted\"}):/*#__PURE__*/_jsx(\"span\",{className:\"me-4\"}),getRoleIcon(node.role),/*#__PURE__*/_jsx(\"span\",{className:\"me-2\",children:node.email}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-muted small\",children:[\"[\",formatUserId(node.id),\"]\"]}),hasChildren&&/*#__PURE__*/_jsx(\"span\",{className:\"ms-auto badge bg-secondary\",children:node.children.length})]}),hasChildren&&isExpanded&&/*#__PURE__*/_jsx(\"div\",{children:node.children.map(child=>renderTreeNode(child,level+1))})]},node.id);};const getTotalUsers=nodes=>{let total=0;const countNodes=nodeList=>{nodeList.forEach(node=>{total++;if(node.children&&node.children.length>0){countNodes(node.children);}});};countNodes(nodes);return total;};const getRootUsersCount=()=>{return filteredTree.length;};if(loading){return/*#__PURE__*/_jsx(Container,{className:\"d-flex justify-content-center align-items-center\",style:{minHeight:'400px'},children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\",role:\"status\",className:\"mb-3\"}),/*#__PURE__*/_jsx(\"div\",{children:t('loading_referral_data')})]})});}if(error){return/*#__PURE__*/_jsx(Container,{children:/*#__PURE__*/_jsx(Alert,{variant:\"danger\",children:error})});}return/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(Row,{className:\"mb-4\",children:/*#__PURE__*/_jsxs(Col,{children:[/*#__PURE__*/_jsx(\"h2\",{children:t('referral_relationships')}),/*#__PURE__*/_jsx(\"p\",{className:\"text-muted\",children:t('referral_tree_description')})]})}),/*#__PURE__*/_jsx(Row,{className:\"mb-3\",children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Row,{className:\"align-items-end\",children:[/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsxs(Form.Group,{children:[/*#__PURE__*/_jsx(Form.Label,{children:t('search_users')}),/*#__PURE__*/_jsx(Form.Control,{type:\"text\",placeholder:t('search_by_email_or_id'),value:searchTerm,onChange:e=>setSearchTerm(e.target.value)})]})}),/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex gap-2\",children:[/*#__PURE__*/_jsxs(Button,{variant:\"outline-primary\",size:\"sm\",onClick:expandAll,children:[/*#__PURE__*/_jsx(FaExpandArrowsAlt,{className:\"me-1\"}),t('expand_all')]}),/*#__PURE__*/_jsxs(Button,{variant:\"outline-secondary\",size:\"sm\",onClick:collapseAll,children:[/*#__PURE__*/_jsx(FaCompressArrowsAlt,{className:\"me-1\"}),t('collapse_all')]})]})})]})})})})}),/*#__PURE__*/_jsxs(Row,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsx(Card,{className:\"bg-primary text-white\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"h5\",{children:t('total_users')}),/*#__PURE__*/_jsx(\"h3\",{children:getTotalUsers(filteredTree)})]})})}),/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsx(Card,{className:\"bg-success text-white\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"h5\",{children:t('root_users')}),/*#__PURE__*/_jsx(\"h3\",{children:getRootUsersCount()})]})})}),/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsx(Card,{className:\"bg-info text-white\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"h5\",{children:t('expanded_nodes')}),/*#__PURE__*/_jsx(\"h3\",{children:expandedNodes.size})]})})})]}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(Card.Header,{children:[/*#__PURE__*/_jsx(\"h5\",{className:\"mb-0\",children:t('referral_tree')}),/*#__PURE__*/_jsx(\"small\",{className:\"text-muted\",children:t('click_to_expand_collapse')})]}),/*#__PURE__*/_jsx(Card.Body,{style:{maxHeight:'600px',overflowY:'auto'},children:filteredTree.length===0?/*#__PURE__*/_jsx(\"div\",{className:\"text-center text-muted py-4\",children:searchTerm?t('no_search_results'):t('no_referral_data')}):/*#__PURE__*/_jsx(\"div\",{children:filteredTree.map(node=>renderTreeNode(node))})})]})})})]});};export default Recommend;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Spinner", "<PERSON><PERSON>", "<PERSON><PERSON>", "Form", "FaChevronDown", "FaChevronRight", "FaUser", "FaUsers", "FaExpandArrowsAlt", "FaCompressArrowsAlt", "FaSearch", "getSupabase", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "Recommend", "t", "loading", "setLoading", "referralTree", "setReferralTree", "filteredTree", "setFilteredTree", "expandedNodes", "setExpandedNodes", "Set", "error", "setError", "searchTerm", "setSearchTerm", "fetchReferralData", "supabase", "currentUser", "data", "user", "auth", "getUser", "agentProfile", "agent<PERSON><PERSON>r", "from", "select", "eq", "id", "single", "console", "customers", "customersError", "limit", "customerUsers", "map", "c", "users", "filter", "Boolean", "fetchReferrers", "userIds", "level", "arguments", "length", "undefined", "maxLevel", "referrerData", "referrerError", "in", "referrers", "nextLevelIds", "u", "referred_by", "nextLevelReferrers", "initialReferrerIds", "allUsers", "uniqueUsers", "index", "self", "findIndex", "tree", "buildReferralTree", "err", "_currentUser", "log", "userIdToUse", "fallbackUser", "simpleCustomers", "simpleError", "simpleUsers", "children", "fallbackErr", "trim", "filterTree", "nodes", "node", "matchesSearch", "email", "toLowerCase", "includes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "userMap", "Map", "rootUsers", "for<PERSON>ach", "set", "parent", "get", "push", "toggleNode", "userId", "newExpanded", "has", "delete", "add", "expandAll", "allNodeIds", "collectNodeIds", "collapseAll", "formatUserId", "substring", "getRoleIcon", "role", "className", "renderTreeNode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isExpanded", "paddingLeft", "style", "cursor", "onClick", "child", "getTotalUsers", "total", "countNodes", "nodeList", "getRootUsersCount", "minHeight", "animation", "variant", "Body", "md", "Group", "Label", "Control", "type", "placeholder", "value", "onChange", "e", "target", "size", "Header", "maxHeight", "overflowY"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/agent/Recommend.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Spinner, <PERSON><PERSON>, But<PERSON>, Form } from 'react-bootstrap';\nimport { FaChevronDown, FaChevronRight, FaUser, FaUsers, FaExpandArrowsAlt, FaCompressArrowsAlt, FaSearch } from 'react-icons/fa';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst Recommend = () => {\n    const { t } = useTranslation();\n    const [loading, setLoading] = useState(true);\n    const [referralTree, setReferralTree] = useState([]);\n    const [filteredTree, setFilteredTree] = useState([]);\n    const [expandedNodes, setExpandedNodes] = useState(new Set());\n    const [error, setError] = useState(null);\n    const [searchTerm, setSearchTerm] = useState('');\n\n    useEffect(() => {\n        const fetchReferralData = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            setError(null);\n\n            let currentUser = null;\n\n            try {\n                const { data: { user } } = await supabase.auth.getUser();\n                currentUser = user;\n\n                if (!user) {\n                    setError(t('user_not_logged_in'));\n                    setLoading(false);\n                    return;\n                }\n\n                // First, get the agent's maker_id to determine scope\n                const { data: agentProfile, error: agentError } = await supabase\n                    .from('agent_profiles')\n                    .select('maker_id')\n                    .eq('user_id', user.id)\n                    .single();\n\n                if (agentError) {\n                    console.error('Error fetching agent profile:', agentError);\n                    setError(t('agent_profile_not_found'));\n                    setLoading(false);\n                    return;\n                }\n\n                // For agent view, we'll fetch users related to this agent's customers\n                // First get all customers under this agent (limit to prevent large queries)\n                const { data: customers, error: customersError } = await supabase\n                    .from('customer_profiles')\n                    .select(`\n                        user_id,\n                        users (\n                            id,\n                            email,\n                            referred_by,\n                            created_at,\n                            role\n                        )\n                    `)\n                    .eq('agent_id', user.id)\n                    .limit(100); // Limit to prevent large queries\n\n                if (customersError) {\n                    console.error('Error fetching customers:', customersError);\n                    setError(t('failed_to_load_referral_data'));\n                    setLoading(false);\n                    return;\n                }\n\n                // Extract user data from customers\n                const customerUsers = customers?.map(c => c.users).filter(Boolean) || [];\n\n                // Get referrer information recursively (up to 3 levels to avoid infinite loops)\n                const fetchReferrers = async (userIds, level = 0, maxLevel = 3) => {\n                    if (level >= maxLevel || userIds.length === 0) {\n                        return [];\n                    }\n\n                    try {\n                        const { data: referrerData, error: referrerError } = await supabase\n                            .from('users')\n                            .select('id, email, referred_by, created_at, role')\n                            .in('id', userIds)\n                            .limit(50); // Limit each level to prevent large queries\n\n                        if (referrerError) {\n                            console.error(`Error fetching referrers at level ${level}:`, referrerError);\n                            return [];\n                        }\n\n                        const referrers = referrerData || [];\n\n                        // Get next level referrer IDs\n                        const nextLevelIds = [...new Set(referrers\n                            .map(u => u.referred_by)\n                            .filter(Boolean))];\n\n                        // Recursively fetch next level\n                        const nextLevelReferrers = await fetchReferrers(nextLevelIds, level + 1, maxLevel);\n\n                        return [...referrers, ...nextLevelReferrers];\n                    } catch (error) {\n                        console.error(`Error in fetchReferrers at level ${level}:`, error);\n                        return [];\n                    }\n                };\n\n                // Get unique referred_by IDs from customers\n                const initialReferrerIds = [...new Set(customerUsers\n                    .map(u => u.referred_by)\n                    .filter(Boolean))];\n\n                const referrers = await fetchReferrers(initialReferrerIds);\n\n                // Combine customer users and their referrers\n                const allUsers = [...customerUsers, ...referrers];\n\n                // Remove duplicates based on user ID\n                const uniqueUsers = allUsers.filter((user, index, self) =>\n                    index === self.findIndex(u => u.id === user.id)\n                );\n\n                // Build the referral tree\n                const tree = buildReferralTree(uniqueUsers);\n                setReferralTree(tree);\n                setFilteredTree(tree);\n\n            } catch (err) {\n                console.error('Error:', err);\n\n                // Fallback: try to get just the direct customers without referrer data\n                try {\n                    console.log('Attempting fallback query...');\n\n                    // Use currentUser if available, otherwise try to get user again\n                    const userIdToUse = currentUser?.id;\n                    if (!userIdToUse) {\n                        const { data: { user: fallbackUser } } = await supabase.auth.getUser();\n                        if (!fallbackUser) {\n                            setError(t('user_not_logged_in'));\n                            return;\n                        }\n                        currentUser = fallbackUser;\n                    }\n\n                    const { data: simpleCustomers, error: simpleError } = await supabase\n                        .from('customer_profiles')\n                        .select(`\n                            users (\n                                id,\n                                email,\n                                created_at,\n                                role\n                            )\n                        `)\n                        .eq('agent_id', currentUser.id)\n                        .limit(50); // Further limit for fallback\n\n                    if (!simpleError && simpleCustomers && simpleCustomers.length > 0) {\n                        const simpleUsers = simpleCustomers\n                            .map(c => c.users)\n                            .filter(Boolean)\n                            .map(u => ({\n                                ...u,\n                                referred_by: null,\n                                children: []\n                            })); // Remove referral info for simplicity\n\n                        console.log('Fallback successful, loaded', simpleUsers.length, 'users');\n                        setReferralTree(simpleUsers);\n                        setFilteredTree(simpleUsers);\n                        setError(t('limited_referral_data'));\n                    } else {\n                        console.log('Fallback failed or no data:', simpleError);\n                        setReferralTree([]);\n                        setFilteredTree([]);\n                        setError(t('no_referral_data'));\n                    }\n                } catch (fallbackErr) {\n                    console.error('Fallback error:', fallbackErr);\n                    setReferralTree([]);\n                    setFilteredTree([]);\n                    setError(t('unexpected_error'));\n                }\n            } finally {\n                setLoading(false);\n            }\n        };\n\n        fetchReferralData();\n    }, [t]);\n\n    // Filter tree based on search term\n    useEffect(() => {\n        if (!searchTerm.trim()) {\n            setFilteredTree(referralTree);\n            return;\n        }\n\n        const filterTree = (nodes) => {\n            return nodes.filter(node => {\n                const matchesSearch = node.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                                    node.id.toLowerCase().includes(searchTerm.toLowerCase());\n\n                const filteredChildren = node.children ? filterTree(node.children) : [];\n\n                return matchesSearch || filteredChildren.length > 0;\n            }).map(node => ({\n                ...node,\n                children: node.children ? filterTree(node.children) : []\n            }));\n        };\n\n        setFilteredTree(filterTree(referralTree));\n    }, [searchTerm, referralTree]);\n\n    const buildReferralTree = (users) => {\n        if (!users || users.length === 0) {\n            return [];\n        }\n\n        const userMap = new Map();\n        const rootUsers = [];\n\n        // Create a map of all users\n        users.forEach(user => {\n            userMap.set(user.id, {\n                ...user,\n                children: []\n            });\n        });\n\n        // Build the tree structure\n        users.forEach(user => {\n            if (user.referred_by) {\n                const parent = userMap.get(user.referred_by);\n                if (parent) {\n                    parent.children.push(userMap.get(user.id));\n                } else {\n                    // Parent not found, treat as root\n                    rootUsers.push(userMap.get(user.id));\n                }\n            } else {\n                // No referrer, this is a root user\n                rootUsers.push(userMap.get(user.id));\n            }\n        });\n\n        return rootUsers;\n    };\n\n    const toggleNode = (userId) => {\n        const newExpanded = new Set(expandedNodes);\n        if (newExpanded.has(userId)) {\n            newExpanded.delete(userId);\n        } else {\n            newExpanded.add(userId);\n        }\n        setExpandedNodes(newExpanded);\n    };\n\n    const expandAll = () => {\n        const allNodeIds = new Set();\n        const collectNodeIds = (nodes) => {\n            nodes.forEach(node => {\n                if (node.children && node.children.length > 0) {\n                    allNodeIds.add(node.id);\n                    collectNodeIds(node.children);\n                }\n            });\n        };\n        collectNodeIds(filteredTree);\n        setExpandedNodes(allNodeIds);\n    };\n\n    const collapseAll = () => {\n        setExpandedNodes(new Set());\n    };\n\n    const formatUserId = (id) => {\n        return id.substring(0, 8);\n    };\n\n    const getRoleIcon = (role) => {\n        switch (role) {\n            case 'maker':\n                return <FaUsers className=\"text-primary me-1\" />;\n            case 'agent':\n                return <FaUser className=\"text-success me-1\" />;\n            case 'customer':\n                return <FaUser className=\"text-info me-1\" />;\n            default:\n                return <FaUser className=\"text-secondary me-1\" />;\n        }\n    };\n\n    const renderTreeNode = (node, level = 0) => {\n        const hasChildren = node.children && node.children.length > 0;\n        const isExpanded = expandedNodes.has(node.id);\n        const paddingLeft = level * 20;\n\n        return (\n            <div key={node.id} className=\"mb-1\">\n                <div \n                    className=\"d-flex align-items-center p-2 border-bottom\"\n                    style={{ paddingLeft: `${paddingLeft}px`, cursor: hasChildren ? 'pointer' : 'default' }}\n                    onClick={() => hasChildren && toggleNode(node.id)}\n                >\n                    {hasChildren ? (\n                        isExpanded ? (\n                            <FaChevronDown className=\"me-2 text-muted\" />\n                        ) : (\n                            <FaChevronRight className=\"me-2 text-muted\" />\n                        )\n                    ) : (\n                        <span className=\"me-4\"></span>\n                    )}\n                    \n                    {getRoleIcon(node.role)}\n                    \n                    <span className=\"me-2\">\n                        {node.email}\n                    </span>\n                    \n                    <span className=\"text-muted small\">\n                        [{formatUserId(node.id)}]\n                    </span>\n                    \n                    {hasChildren && (\n                        <span className=\"ms-auto badge bg-secondary\">\n                            {node.children.length}\n                        </span>\n                    )}\n                </div>\n                \n                {hasChildren && isExpanded && (\n                    <div>\n                        {node.children.map(child => renderTreeNode(child, level + 1))}\n                    </div>\n                )}\n            </div>\n        );\n    };\n\n    const getTotalUsers = (nodes) => {\n        let total = 0;\n        const countNodes = (nodeList) => {\n            nodeList.forEach(node => {\n                total++;\n                if (node.children && node.children.length > 0) {\n                    countNodes(node.children);\n                }\n            });\n        };\n        countNodes(nodes);\n        return total;\n    };\n\n    const getRootUsersCount = () => {\n        return filteredTree.length;\n    };\n\n    if (loading) {\n        return (\n            <Container className=\"d-flex justify-content-center align-items-center\" style={{ minHeight: '400px' }}>\n                <div className=\"text-center\">\n                    <Spinner animation=\"border\" role=\"status\" className=\"mb-3\" />\n                    <div>{t('loading_referral_data')}</div>\n                </div>\n            </Container>\n        );\n    }\n\n    if (error) {\n        return (\n            <Container>\n                <Alert variant=\"danger\">\n                    {error}\n                </Alert>\n            </Container>\n        );\n    }\n\n    return (\n        <Container>\n            <Row className=\"mb-4\">\n                <Col>\n                    <h2>{t('referral_relationships')}</h2>\n                    <p className=\"text-muted\">{t('referral_tree_description')}</p>\n                </Col>\n            </Row>\n\n            <Row className=\"mb-3\">\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Row className=\"align-items-end\">\n                                <Col md={4}>\n                                    <Form.Group>\n                                        <Form.Label>{t('search_users')}</Form.Label>\n                                        <Form.Control\n                                            type=\"text\"\n                                            placeholder={t('search_by_email_or_id')}\n                                            value={searchTerm}\n                                            onChange={(e) => setSearchTerm(e.target.value)}\n                                        />\n                                    </Form.Group>\n                                </Col>\n                                <Col md={4}>\n                                    <div className=\"d-flex gap-2\">\n                                        <Button\n                                            variant=\"outline-primary\"\n                                            size=\"sm\"\n                                            onClick={expandAll}\n                                        >\n                                            <FaExpandArrowsAlt className=\"me-1\" />\n                                            {t('expand_all')}\n                                        </Button>\n                                        <Button\n                                            variant=\"outline-secondary\"\n                                            size=\"sm\"\n                                            onClick={collapseAll}\n                                        >\n                                            <FaCompressArrowsAlt className=\"me-1\" />\n                                            {t('collapse_all')}\n                                        </Button>\n                                    </div>\n                                </Col>\n                            </Row>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            <Row className=\"mb-3\">\n                <Col md={4}>\n                    <Card className=\"bg-primary text-white\">\n                        <Card.Body>\n                            <h5>{t('total_users')}</h5>\n                            <h3>{getTotalUsers(filteredTree)}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={4}>\n                    <Card className=\"bg-success text-white\">\n                        <Card.Body>\n                            <h5>{t('root_users')}</h5>\n                            <h3>{getRootUsersCount()}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={4}>\n                    <Card className=\"bg-info text-white\">\n                        <Card.Body>\n                            <h5>{t('expanded_nodes')}</h5>\n                            <h3>{expandedNodes.size}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Header>\n                            <h5 className=\"mb-0\">{t('referral_tree')}</h5>\n                            <small className=\"text-muted\">\n                                {t('click_to_expand_collapse')}\n                            </small>\n                        </Card.Header>\n                        <Card.Body style={{ maxHeight: '600px', overflowY: 'auto' }}>\n                            {filteredTree.length === 0 ? (\n                                <div className=\"text-center text-muted py-4\">\n                                    {searchTerm ? t('no_search_results') : t('no_referral_data')}\n                                </div>\n                            ) : (\n                                <div>\n                                    {filteredTree.map(node => renderTreeNode(node))}\n                                </div>\n                            )}\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default Recommend;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,OAAO,CAAEC,KAAK,CAAEC,MAAM,CAAEC,IAAI,KAAQ,iBAAiB,CACzF,OAASC,aAAa,CAAEC,cAAc,CAAEC,MAAM,CAAEC,OAAO,CAAEC,iBAAiB,CAAEC,mBAAmB,CAAEC,QAAQ,KAAQ,gBAAgB,CACjI,OAASC,WAAW,KAAQ,sBAAsB,CAClD,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,SAAS,CAAGA,CAAA,GAAM,CACpB,KAAM,CAAEC,CAAE,CAAC,CAAGN,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACO,OAAO,CAAEC,UAAU,CAAC,CAAG1B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC2B,YAAY,CAAEC,eAAe,CAAC,CAAG5B,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC6B,YAAY,CAAEC,eAAe,CAAC,CAAG9B,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC+B,aAAa,CAAEC,gBAAgB,CAAC,CAAGhC,QAAQ,CAAC,GAAI,CAAAiC,GAAG,CAAC,CAAC,CAAC,CAC7D,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGnC,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAACoC,UAAU,CAAEC,aAAa,CAAC,CAAGrC,QAAQ,CAAC,EAAE,CAAC,CAEhDC,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAqC,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CAClC,KAAM,CAAAC,QAAQ,CAAGtB,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACsB,QAAQ,CAAE,OAEfb,UAAU,CAAC,IAAI,CAAC,CAChBS,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAI,CAAAK,WAAW,CAAG,IAAI,CAEtB,GAAI,CACA,KAAM,CAAEC,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAACC,OAAO,CAAC,CAAC,CACxDJ,WAAW,CAAGE,IAAI,CAElB,GAAI,CAACA,IAAI,CAAE,CACPP,QAAQ,CAACX,CAAC,CAAC,oBAAoB,CAAC,CAAC,CACjCE,UAAU,CAAC,KAAK,CAAC,CACjB,OACJ,CAEA;AACA,KAAM,CAAEe,IAAI,CAAEI,YAAY,CAAEX,KAAK,CAAEY,UAAW,CAAC,CAAG,KAAM,CAAAP,QAAQ,CAC3DQ,IAAI,CAAC,gBAAgB,CAAC,CACtBC,MAAM,CAAC,UAAU,CAAC,CAClBC,EAAE,CAAC,SAAS,CAAEP,IAAI,CAACQ,EAAE,CAAC,CACtBC,MAAM,CAAC,CAAC,CAEb,GAAIL,UAAU,CAAE,CACZM,OAAO,CAAClB,KAAK,CAAC,+BAA+B,CAAEY,UAAU,CAAC,CAC1DX,QAAQ,CAACX,CAAC,CAAC,yBAAyB,CAAC,CAAC,CACtCE,UAAU,CAAC,KAAK,CAAC,CACjB,OACJ,CAEA;AACA;AACA,KAAM,CAAEe,IAAI,CAAEY,SAAS,CAAEnB,KAAK,CAAEoB,cAAe,CAAC,CAAG,KAAM,CAAAf,QAAQ,CAC5DQ,IAAI,CAAC,mBAAmB,CAAC,CACzBC,MAAM,CAAC;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,CAAC,CACDC,EAAE,CAAC,UAAU,CAAEP,IAAI,CAACQ,EAAE,CAAC,CACvBK,KAAK,CAAC,GAAG,CAAC,CAAE;AAEjB,GAAID,cAAc,CAAE,CAChBF,OAAO,CAAClB,KAAK,CAAC,2BAA2B,CAAEoB,cAAc,CAAC,CAC1DnB,QAAQ,CAACX,CAAC,CAAC,8BAA8B,CAAC,CAAC,CAC3CE,UAAU,CAAC,KAAK,CAAC,CACjB,OACJ,CAEA;AACA,KAAM,CAAA8B,aAAa,CAAG,CAAAH,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEI,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACC,KAAK,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,GAAI,EAAE,CAExE;AACA,KAAM,CAAAC,cAAc,CAAG,cAAAA,CAAOC,OAAO,CAA8B,IAA5B,CAAAC,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,IAAE,CAAAG,QAAQ,CAAAH,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAC1D,GAAID,KAAK,EAAII,QAAQ,EAAIL,OAAO,CAACG,MAAM,GAAK,CAAC,CAAE,CAC3C,MAAO,EAAE,CACb,CAEA,GAAI,CACA,KAAM,CAAEzB,IAAI,CAAE4B,YAAY,CAAEnC,KAAK,CAAEoC,aAAc,CAAC,CAAG,KAAM,CAAA/B,QAAQ,CAC9DQ,IAAI,CAAC,OAAO,CAAC,CACbC,MAAM,CAAC,0CAA0C,CAAC,CAClDuB,EAAE,CAAC,IAAI,CAAER,OAAO,CAAC,CACjBR,KAAK,CAAC,EAAE,CAAC,CAAE;AAEhB,GAAIe,aAAa,CAAE,CACflB,OAAO,CAAClB,KAAK,CAAC,qCAAqC8B,KAAK,GAAG,CAAEM,aAAa,CAAC,CAC3E,MAAO,EAAE,CACb,CAEA,KAAM,CAAAE,SAAS,CAAGH,YAAY,EAAI,EAAE,CAEpC;AACA,KAAM,CAAAI,YAAY,CAAG,CAAC,GAAG,GAAI,CAAAxC,GAAG,CAACuC,SAAS,CACrCf,GAAG,CAACiB,CAAC,EAAIA,CAAC,CAACC,WAAW,CAAC,CACvBf,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC,CAEtB;AACA,KAAM,CAAAe,kBAAkB,CAAG,KAAM,CAAAd,cAAc,CAACW,YAAY,CAAET,KAAK,CAAG,CAAC,CAAEI,QAAQ,CAAC,CAElF,MAAO,CAAC,GAAGI,SAAS,CAAE,GAAGI,kBAAkB,CAAC,CAChD,CAAE,MAAO1C,KAAK,CAAE,CACZkB,OAAO,CAAClB,KAAK,CAAC,oCAAoC8B,KAAK,GAAG,CAAE9B,KAAK,CAAC,CAClE,MAAO,EAAE,CACb,CACJ,CAAC,CAED;AACA,KAAM,CAAA2C,kBAAkB,CAAG,CAAC,GAAG,GAAI,CAAA5C,GAAG,CAACuB,aAAa,CAC/CC,GAAG,CAACiB,CAAC,EAAIA,CAAC,CAACC,WAAW,CAAC,CACvBf,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC,CAEtB,KAAM,CAAAW,SAAS,CAAG,KAAM,CAAAV,cAAc,CAACe,kBAAkB,CAAC,CAE1D;AACA,KAAM,CAAAC,QAAQ,CAAG,CAAC,GAAGtB,aAAa,CAAE,GAAGgB,SAAS,CAAC,CAEjD;AACA,KAAM,CAAAO,WAAW,CAAGD,QAAQ,CAAClB,MAAM,CAAC,CAAClB,IAAI,CAAEsC,KAAK,CAAEC,IAAI,GAClDD,KAAK,GAAKC,IAAI,CAACC,SAAS,CAACR,CAAC,EAAIA,CAAC,CAACxB,EAAE,GAAKR,IAAI,CAACQ,EAAE,CAClD,CAAC,CAED;AACA,KAAM,CAAAiC,IAAI,CAAGC,iBAAiB,CAACL,WAAW,CAAC,CAC3CnD,eAAe,CAACuD,IAAI,CAAC,CACrBrD,eAAe,CAACqD,IAAI,CAAC,CAEzB,CAAE,MAAOE,GAAG,CAAE,CACVjC,OAAO,CAAClB,KAAK,CAAC,QAAQ,CAAEmD,GAAG,CAAC,CAE5B;AACA,GAAI,KAAAC,YAAA,CACAlC,OAAO,CAACmC,GAAG,CAAC,8BAA8B,CAAC,CAE3C;AACA,KAAM,CAAAC,WAAW,EAAAF,YAAA,CAAG9C,WAAW,UAAA8C,YAAA,iBAAXA,YAAA,CAAapC,EAAE,CACnC,GAAI,CAACsC,WAAW,CAAE,CACd,KAAM,CAAE/C,IAAI,CAAE,CAAEC,IAAI,CAAE+C,YAAa,CAAE,CAAC,CAAG,KAAM,CAAAlD,QAAQ,CAACI,IAAI,CAACC,OAAO,CAAC,CAAC,CACtE,GAAI,CAAC6C,YAAY,CAAE,CACftD,QAAQ,CAACX,CAAC,CAAC,oBAAoB,CAAC,CAAC,CACjC,OACJ,CACAgB,WAAW,CAAGiD,YAAY,CAC9B,CAEA,KAAM,CAAEhD,IAAI,CAAEiD,eAAe,CAAExD,KAAK,CAAEyD,WAAY,CAAC,CAAG,KAAM,CAAApD,QAAQ,CAC/DQ,IAAI,CAAC,mBAAmB,CAAC,CACzBC,MAAM,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,CAAC,CACDC,EAAE,CAAC,UAAU,CAAET,WAAW,CAACU,EAAE,CAAC,CAC9BK,KAAK,CAAC,EAAE,CAAC,CAAE;AAEhB,GAAI,CAACoC,WAAW,EAAID,eAAe,EAAIA,eAAe,CAACxB,MAAM,CAAG,CAAC,CAAE,CAC/D,KAAM,CAAA0B,WAAW,CAAGF,eAAe,CAC9BjC,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACC,KAAK,CAAC,CACjBC,MAAM,CAACC,OAAO,CAAC,CACfJ,GAAG,CAACiB,CAAC,GAAK,CACP,GAAGA,CAAC,CACJC,WAAW,CAAE,IAAI,CACjBkB,QAAQ,CAAE,EACd,CAAC,CAAC,CAAC,CAAE;AAETzC,OAAO,CAACmC,GAAG,CAAC,6BAA6B,CAAEK,WAAW,CAAC1B,MAAM,CAAE,OAAO,CAAC,CACvEtC,eAAe,CAACgE,WAAW,CAAC,CAC5B9D,eAAe,CAAC8D,WAAW,CAAC,CAC5BzD,QAAQ,CAACX,CAAC,CAAC,uBAAuB,CAAC,CAAC,CACxC,CAAC,IAAM,CACH4B,OAAO,CAACmC,GAAG,CAAC,6BAA6B,CAAEI,WAAW,CAAC,CACvD/D,eAAe,CAAC,EAAE,CAAC,CACnBE,eAAe,CAAC,EAAE,CAAC,CACnBK,QAAQ,CAACX,CAAC,CAAC,kBAAkB,CAAC,CAAC,CACnC,CACJ,CAAE,MAAOsE,WAAW,CAAE,CAClB1C,OAAO,CAAClB,KAAK,CAAC,iBAAiB,CAAE4D,WAAW,CAAC,CAC7ClE,eAAe,CAAC,EAAE,CAAC,CACnBE,eAAe,CAAC,EAAE,CAAC,CACnBK,QAAQ,CAACX,CAAC,CAAC,kBAAkB,CAAC,CAAC,CACnC,CACJ,CAAC,OAAS,CACNE,UAAU,CAAC,KAAK,CAAC,CACrB,CACJ,CAAC,CAEDY,iBAAiB,CAAC,CAAC,CACvB,CAAC,CAAE,CAACd,CAAC,CAAC,CAAC,CAEP;AACAvB,SAAS,CAAC,IAAM,CACZ,GAAI,CAACmC,UAAU,CAAC2D,IAAI,CAAC,CAAC,CAAE,CACpBjE,eAAe,CAACH,YAAY,CAAC,CAC7B,OACJ,CAEA,KAAM,CAAAqE,UAAU,CAAIC,KAAK,EAAK,CAC1B,MAAO,CAAAA,KAAK,CAACrC,MAAM,CAACsC,IAAI,EAAI,CACxB,KAAM,CAAAC,aAAa,CAAGD,IAAI,CAACE,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClE,UAAU,CAACiE,WAAW,CAAC,CAAC,CAAC,EAC7DH,IAAI,CAAChD,EAAE,CAACmD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClE,UAAU,CAACiE,WAAW,CAAC,CAAC,CAAC,CAE5E,KAAM,CAAAE,gBAAgB,CAAGL,IAAI,CAACL,QAAQ,CAAGG,UAAU,CAACE,IAAI,CAACL,QAAQ,CAAC,CAAG,EAAE,CAEvE,MAAO,CAAAM,aAAa,EAAII,gBAAgB,CAACrC,MAAM,CAAG,CAAC,CACvD,CAAC,CAAC,CAACT,GAAG,CAACyC,IAAI,GAAK,CACZ,GAAGA,IAAI,CACPL,QAAQ,CAAEK,IAAI,CAACL,QAAQ,CAAGG,UAAU,CAACE,IAAI,CAACL,QAAQ,CAAC,CAAG,EAC1D,CAAC,CAAC,CAAC,CACP,CAAC,CAED/D,eAAe,CAACkE,UAAU,CAACrE,YAAY,CAAC,CAAC,CAC7C,CAAC,CAAE,CAACS,UAAU,CAAET,YAAY,CAAC,CAAC,CAE9B,KAAM,CAAAyD,iBAAiB,CAAIzB,KAAK,EAAK,CACjC,GAAI,CAACA,KAAK,EAAIA,KAAK,CAACO,MAAM,GAAK,CAAC,CAAE,CAC9B,MAAO,EAAE,CACb,CAEA,KAAM,CAAAsC,OAAO,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CACzB,KAAM,CAAAC,SAAS,CAAG,EAAE,CAEpB;AACA/C,KAAK,CAACgD,OAAO,CAACjE,IAAI,EAAI,CAClB8D,OAAO,CAACI,GAAG,CAAClE,IAAI,CAACQ,EAAE,CAAE,CACjB,GAAGR,IAAI,CACPmD,QAAQ,CAAE,EACd,CAAC,CAAC,CACN,CAAC,CAAC,CAEF;AACAlC,KAAK,CAACgD,OAAO,CAACjE,IAAI,EAAI,CAClB,GAAIA,IAAI,CAACiC,WAAW,CAAE,CAClB,KAAM,CAAAkC,MAAM,CAAGL,OAAO,CAACM,GAAG,CAACpE,IAAI,CAACiC,WAAW,CAAC,CAC5C,GAAIkC,MAAM,CAAE,CACRA,MAAM,CAAChB,QAAQ,CAACkB,IAAI,CAACP,OAAO,CAACM,GAAG,CAACpE,IAAI,CAACQ,EAAE,CAAC,CAAC,CAC9C,CAAC,IAAM,CACH;AACAwD,SAAS,CAACK,IAAI,CAACP,OAAO,CAACM,GAAG,CAACpE,IAAI,CAACQ,EAAE,CAAC,CAAC,CACxC,CACJ,CAAC,IAAM,CACH;AACAwD,SAAS,CAACK,IAAI,CAACP,OAAO,CAACM,GAAG,CAACpE,IAAI,CAACQ,EAAE,CAAC,CAAC,CACxC,CACJ,CAAC,CAAC,CAEF,MAAO,CAAAwD,SAAS,CACpB,CAAC,CAED,KAAM,CAAAM,UAAU,CAAIC,MAAM,EAAK,CAC3B,KAAM,CAAAC,WAAW,CAAG,GAAI,CAAAjF,GAAG,CAACF,aAAa,CAAC,CAC1C,GAAImF,WAAW,CAACC,GAAG,CAACF,MAAM,CAAC,CAAE,CACzBC,WAAW,CAACE,MAAM,CAACH,MAAM,CAAC,CAC9B,CAAC,IAAM,CACHC,WAAW,CAACG,GAAG,CAACJ,MAAM,CAAC,CAC3B,CACAjF,gBAAgB,CAACkF,WAAW,CAAC,CACjC,CAAC,CAED,KAAM,CAAAI,SAAS,CAAGA,CAAA,GAAM,CACpB,KAAM,CAAAC,UAAU,CAAG,GAAI,CAAAtF,GAAG,CAAC,CAAC,CAC5B,KAAM,CAAAuF,cAAc,CAAIvB,KAAK,EAAK,CAC9BA,KAAK,CAACU,OAAO,CAACT,IAAI,EAAI,CAClB,GAAIA,IAAI,CAACL,QAAQ,EAAIK,IAAI,CAACL,QAAQ,CAAC3B,MAAM,CAAG,CAAC,CAAE,CAC3CqD,UAAU,CAACF,GAAG,CAACnB,IAAI,CAAChD,EAAE,CAAC,CACvBsE,cAAc,CAACtB,IAAI,CAACL,QAAQ,CAAC,CACjC,CACJ,CAAC,CAAC,CACN,CAAC,CACD2B,cAAc,CAAC3F,YAAY,CAAC,CAC5BG,gBAAgB,CAACuF,UAAU,CAAC,CAChC,CAAC,CAED,KAAM,CAAAE,WAAW,CAAGA,CAAA,GAAM,CACtBzF,gBAAgB,CAAC,GAAI,CAAAC,GAAG,CAAC,CAAC,CAAC,CAC/B,CAAC,CAED,KAAM,CAAAyF,YAAY,CAAIxE,EAAE,EAAK,CACzB,MAAO,CAAAA,EAAE,CAACyE,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAC,WAAW,CAAIC,IAAI,EAAK,CAC1B,OAAQA,IAAI,EACR,IAAK,OAAO,CACR,mBAAOzG,IAAA,CAACP,OAAO,EAACiH,SAAS,CAAC,mBAAmB,CAAE,CAAC,CACpD,IAAK,OAAO,CACR,mBAAO1G,IAAA,CAACR,MAAM,EAACkH,SAAS,CAAC,mBAAmB,CAAE,CAAC,CACnD,IAAK,UAAU,CACX,mBAAO1G,IAAA,CAACR,MAAM,EAACkH,SAAS,CAAC,gBAAgB,CAAE,CAAC,CAChD,QACI,mBAAO1G,IAAA,CAACR,MAAM,EAACkH,SAAS,CAAC,qBAAqB,CAAE,CAAC,CACzD,CACJ,CAAC,CAED,KAAM,CAAAC,cAAc,CAAG,QAAAA,CAAC7B,IAAI,CAAgB,IAAd,CAAAlC,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CACnC,KAAM,CAAA+D,WAAW,CAAG9B,IAAI,CAACL,QAAQ,EAAIK,IAAI,CAACL,QAAQ,CAAC3B,MAAM,CAAG,CAAC,CAC7D,KAAM,CAAA+D,UAAU,CAAGlG,aAAa,CAACoF,GAAG,CAACjB,IAAI,CAAChD,EAAE,CAAC,CAC7C,KAAM,CAAAgF,WAAW,CAAGlE,KAAK,CAAG,EAAE,CAE9B,mBACI1C,KAAA,QAAmBwG,SAAS,CAAC,MAAM,CAAAjC,QAAA,eAC/BvE,KAAA,QACIwG,SAAS,CAAC,6CAA6C,CACvDK,KAAK,CAAE,CAAED,WAAW,CAAE,GAAGA,WAAW,IAAI,CAAEE,MAAM,CAAEJ,WAAW,CAAG,SAAS,CAAG,SAAU,CAAE,CACxFK,OAAO,CAAEA,CAAA,GAAML,WAAW,EAAIhB,UAAU,CAACd,IAAI,CAAChD,EAAE,CAAE,CAAA2C,QAAA,EAEjDmC,WAAW,CACRC,UAAU,cACN7G,IAAA,CAACV,aAAa,EAACoH,SAAS,CAAC,iBAAiB,CAAE,CAAC,cAE7C1G,IAAA,CAACT,cAAc,EAACmH,SAAS,CAAC,iBAAiB,CAAE,CAChD,cAED1G,IAAA,SAAM0G,SAAS,CAAC,MAAM,CAAO,CAChC,CAEAF,WAAW,CAAC1B,IAAI,CAAC2B,IAAI,CAAC,cAEvBzG,IAAA,SAAM0G,SAAS,CAAC,MAAM,CAAAjC,QAAA,CACjBK,IAAI,CAACE,KAAK,CACT,CAAC,cAEP9E,KAAA,SAAMwG,SAAS,CAAC,kBAAkB,CAAAjC,QAAA,EAAC,GAC9B,CAAC6B,YAAY,CAACxB,IAAI,CAAChD,EAAE,CAAC,CAAC,GAC5B,EAAM,CAAC,CAEN8E,WAAW,eACR5G,IAAA,SAAM0G,SAAS,CAAC,4BAA4B,CAAAjC,QAAA,CACvCK,IAAI,CAACL,QAAQ,CAAC3B,MAAM,CACnB,CACT,EACA,CAAC,CAEL8D,WAAW,EAAIC,UAAU,eACtB7G,IAAA,QAAAyE,QAAA,CACKK,IAAI,CAACL,QAAQ,CAACpC,GAAG,CAAC6E,KAAK,EAAIP,cAAc,CAACO,KAAK,CAAEtE,KAAK,CAAG,CAAC,CAAC,CAAC,CAC5D,CACR,GArCKkC,IAAI,CAAChD,EAsCV,CAAC,CAEd,CAAC,CAED,KAAM,CAAAqF,aAAa,CAAItC,KAAK,EAAK,CAC7B,GAAI,CAAAuC,KAAK,CAAG,CAAC,CACb,KAAM,CAAAC,UAAU,CAAIC,QAAQ,EAAK,CAC7BA,QAAQ,CAAC/B,OAAO,CAACT,IAAI,EAAI,CACrBsC,KAAK,EAAE,CACP,GAAItC,IAAI,CAACL,QAAQ,EAAIK,IAAI,CAACL,QAAQ,CAAC3B,MAAM,CAAG,CAAC,CAAE,CAC3CuE,UAAU,CAACvC,IAAI,CAACL,QAAQ,CAAC,CAC7B,CACJ,CAAC,CAAC,CACN,CAAC,CACD4C,UAAU,CAACxC,KAAK,CAAC,CACjB,MAAO,CAAAuC,KAAK,CAChB,CAAC,CAED,KAAM,CAAAG,iBAAiB,CAAGA,CAAA,GAAM,CAC5B,MAAO,CAAA9G,YAAY,CAACqC,MAAM,CAC9B,CAAC,CAED,GAAIzC,OAAO,CAAE,CACT,mBACIL,IAAA,CAAClB,SAAS,EAAC4H,SAAS,CAAC,kDAAkD,CAACK,KAAK,CAAE,CAAES,SAAS,CAAE,OAAQ,CAAE,CAAA/C,QAAA,cAClGvE,KAAA,QAAKwG,SAAS,CAAC,aAAa,CAAAjC,QAAA,eACxBzE,IAAA,CAACd,OAAO,EAACuI,SAAS,CAAC,QAAQ,CAAChB,IAAI,CAAC,QAAQ,CAACC,SAAS,CAAC,MAAM,CAAE,CAAC,cAC7D1G,IAAA,QAAAyE,QAAA,CAAMrE,CAAC,CAAC,uBAAuB,CAAC,CAAM,CAAC,EACtC,CAAC,CACC,CAAC,CAEpB,CAEA,GAAIU,KAAK,CAAE,CACP,mBACId,IAAA,CAAClB,SAAS,EAAA2F,QAAA,cACNzE,IAAA,CAACb,KAAK,EAACuI,OAAO,CAAC,QAAQ,CAAAjD,QAAA,CAClB3D,KAAK,CACH,CAAC,CACD,CAAC,CAEpB,CAEA,mBACIZ,KAAA,CAACpB,SAAS,EAAA2F,QAAA,eACNzE,IAAA,CAACjB,GAAG,EAAC2H,SAAS,CAAC,MAAM,CAAAjC,QAAA,cACjBvE,KAAA,CAAClB,GAAG,EAAAyF,QAAA,eACAzE,IAAA,OAAAyE,QAAA,CAAKrE,CAAC,CAAC,wBAAwB,CAAC,CAAK,CAAC,cACtCJ,IAAA,MAAG0G,SAAS,CAAC,YAAY,CAAAjC,QAAA,CAAErE,CAAC,CAAC,2BAA2B,CAAC,CAAI,CAAC,EAC7D,CAAC,CACL,CAAC,cAENJ,IAAA,CAACjB,GAAG,EAAC2H,SAAS,CAAC,MAAM,CAAAjC,QAAA,cACjBzE,IAAA,CAAChB,GAAG,EAAAyF,QAAA,cACAzE,IAAA,CAACf,IAAI,EAAAwF,QAAA,cACDzE,IAAA,CAACf,IAAI,CAAC0I,IAAI,EAAAlD,QAAA,cACNvE,KAAA,CAACnB,GAAG,EAAC2H,SAAS,CAAC,iBAAiB,CAAAjC,QAAA,eAC5BzE,IAAA,CAAChB,GAAG,EAAC4I,EAAE,CAAE,CAAE,CAAAnD,QAAA,cACPvE,KAAA,CAACb,IAAI,CAACwI,KAAK,EAAApD,QAAA,eACPzE,IAAA,CAACX,IAAI,CAACyI,KAAK,EAAArD,QAAA,CAAErE,CAAC,CAAC,cAAc,CAAC,CAAa,CAAC,cAC5CJ,IAAA,CAACX,IAAI,CAAC0I,OAAO,EACTC,IAAI,CAAC,MAAM,CACXC,WAAW,CAAE7H,CAAC,CAAC,uBAAuB,CAAE,CACxC8H,KAAK,CAAElH,UAAW,CAClBmH,QAAQ,CAAGC,CAAC,EAAKnH,aAAa,CAACmH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAClD,CAAC,EACM,CAAC,CACZ,CAAC,cACNlI,IAAA,CAAChB,GAAG,EAAC4I,EAAE,CAAE,CAAE,CAAAnD,QAAA,cACPvE,KAAA,QAAKwG,SAAS,CAAC,cAAc,CAAAjC,QAAA,eACzBvE,KAAA,CAACd,MAAM,EACHsI,OAAO,CAAC,iBAAiB,CACzBY,IAAI,CAAC,IAAI,CACTrB,OAAO,CAAEf,SAAU,CAAAzB,QAAA,eAEnBzE,IAAA,CAACN,iBAAiB,EAACgH,SAAS,CAAC,MAAM,CAAE,CAAC,CACrCtG,CAAC,CAAC,YAAY,CAAC,EACZ,CAAC,cACTF,KAAA,CAACd,MAAM,EACHsI,OAAO,CAAC,mBAAmB,CAC3BY,IAAI,CAAC,IAAI,CACTrB,OAAO,CAAEZ,WAAY,CAAA5B,QAAA,eAErBzE,IAAA,CAACL,mBAAmB,EAAC+G,SAAS,CAAC,MAAM,CAAE,CAAC,CACvCtG,CAAC,CAAC,cAAc,CAAC,EACd,CAAC,EACR,CAAC,CACL,CAAC,EACL,CAAC,CACC,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,cAENF,KAAA,CAACnB,GAAG,EAAC2H,SAAS,CAAC,MAAM,CAAAjC,QAAA,eACjBzE,IAAA,CAAChB,GAAG,EAAC4I,EAAE,CAAE,CAAE,CAAAnD,QAAA,cACPzE,IAAA,CAACf,IAAI,EAACyH,SAAS,CAAC,uBAAuB,CAAAjC,QAAA,cACnCvE,KAAA,CAACjB,IAAI,CAAC0I,IAAI,EAAAlD,QAAA,eACNzE,IAAA,OAAAyE,QAAA,CAAKrE,CAAC,CAAC,aAAa,CAAC,CAAK,CAAC,cAC3BJ,IAAA,OAAAyE,QAAA,CAAK0C,aAAa,CAAC1G,YAAY,CAAC,CAAK,CAAC,EAC/B,CAAC,CACV,CAAC,CACN,CAAC,cACNT,IAAA,CAAChB,GAAG,EAAC4I,EAAE,CAAE,CAAE,CAAAnD,QAAA,cACPzE,IAAA,CAACf,IAAI,EAACyH,SAAS,CAAC,uBAAuB,CAAAjC,QAAA,cACnCvE,KAAA,CAACjB,IAAI,CAAC0I,IAAI,EAAAlD,QAAA,eACNzE,IAAA,OAAAyE,QAAA,CAAKrE,CAAC,CAAC,YAAY,CAAC,CAAK,CAAC,cAC1BJ,IAAA,OAAAyE,QAAA,CAAK8C,iBAAiB,CAAC,CAAC,CAAK,CAAC,EACvB,CAAC,CACV,CAAC,CACN,CAAC,cACNvH,IAAA,CAAChB,GAAG,EAAC4I,EAAE,CAAE,CAAE,CAAAnD,QAAA,cACPzE,IAAA,CAACf,IAAI,EAACyH,SAAS,CAAC,oBAAoB,CAAAjC,QAAA,cAChCvE,KAAA,CAACjB,IAAI,CAAC0I,IAAI,EAAAlD,QAAA,eACNzE,IAAA,OAAAyE,QAAA,CAAKrE,CAAC,CAAC,gBAAgB,CAAC,CAAK,CAAC,cAC9BJ,IAAA,OAAAyE,QAAA,CAAK9D,aAAa,CAAC2H,IAAI,CAAK,CAAC,EACtB,CAAC,CACV,CAAC,CACN,CAAC,EACL,CAAC,cAENtI,IAAA,CAACjB,GAAG,EAAA0F,QAAA,cACAzE,IAAA,CAAChB,GAAG,EAAAyF,QAAA,cACAvE,KAAA,CAACjB,IAAI,EAAAwF,QAAA,eACDvE,KAAA,CAACjB,IAAI,CAACsJ,MAAM,EAAA9D,QAAA,eACRzE,IAAA,OAAI0G,SAAS,CAAC,MAAM,CAAAjC,QAAA,CAAErE,CAAC,CAAC,eAAe,CAAC,CAAK,CAAC,cAC9CJ,IAAA,UAAO0G,SAAS,CAAC,YAAY,CAAAjC,QAAA,CACxBrE,CAAC,CAAC,0BAA0B,CAAC,CAC3B,CAAC,EACC,CAAC,cACdJ,IAAA,CAACf,IAAI,CAAC0I,IAAI,EAACZ,KAAK,CAAE,CAAEyB,SAAS,CAAE,OAAO,CAAEC,SAAS,CAAE,MAAO,CAAE,CAAAhE,QAAA,CACvDhE,YAAY,CAACqC,MAAM,GAAK,CAAC,cACtB9C,IAAA,QAAK0G,SAAS,CAAC,6BAA6B,CAAAjC,QAAA,CACvCzD,UAAU,CAAGZ,CAAC,CAAC,mBAAmB,CAAC,CAAGA,CAAC,CAAC,kBAAkB,CAAC,CAC3D,CAAC,cAENJ,IAAA,QAAAyE,QAAA,CACKhE,YAAY,CAAC4B,GAAG,CAACyC,IAAI,EAAI6B,cAAc,CAAC7B,IAAI,CAAC,CAAC,CAC9C,CACR,CACM,CAAC,EACV,CAAC,CACN,CAAC,CACL,CAAC,EACC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAA3E,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}