"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[53],{1072:(e,r,s)=>{s.d(r,{A:()=>c});var a=s(8139),l=s.n(a),t=s(5043),n=s(7852),i=s(579);const o=t.forwardRef((e,r)=>{let{bsPrefix:s,className:a,as:t="div",...o}=e;const c=(0,n.oU)(s,"row"),d=(0,n.gy)(),f=(0,n.Jm)(),m=`${c}-cols`,u=[];return d.forEach(e=>{const r=o[e];let s;delete o[e],null!=r&&"object"===typeof r?({cols:s}=r):s=r;const a=e!==f?`-${e}`:"";null!=s&&u.push(`${m}${a}-${s}`)}),(0,i.jsx)(t,{ref:r,...o,className:l()(a,c,...u)})});o.displayName="Row";const c=o},1719:(e,r,s)=>{s.d(r,{A:()=>y});var a=s(8139),l=s.n(a),t=s(5043),n=s(1969),i=s(6618),o=s(7852),c=s(4488),d=s(579);const f=(0,c.A)("h4");f.displayName="DivStyledAsH4";const m=t.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:t=f,...n}=e;return a=(0,o.oU)(a,"alert-heading"),(0,d.jsx)(t,{ref:r,className:l()(s,a),...n})});m.displayName="AlertHeading";const u=m;var x=s(7071);const h=t.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:t=x.A,...n}=e;return a=(0,o.oU)(a,"alert-link"),(0,d.jsx)(t,{ref:r,className:l()(s,a),...n})});h.displayName="AlertLink";const g=h;var N=s(8072),j=s(5632);const b=t.forwardRef((e,r)=>{const{bsPrefix:s,show:a=!0,closeLabel:t="Close alert",closeVariant:c,className:f,children:m,variant:u="primary",onClose:x,dismissible:h,transition:g=N.A,...b}=(0,n.Zw)(e,{show:"onClose"}),y=(0,o.oU)(s,"alert"),p=(0,i.A)(e=>{x&&x(!1,e)}),A=!0===g?N.A:g,v=(0,d.jsxs)("div",{role:"alert",...A?void 0:b,ref:r,className:l()(f,y,u&&`${y}-${u}`,h&&`${y}-dismissible`),children:[h&&(0,d.jsx)(j.A,{onClick:p,"aria-label":t,variant:c}),m]});return A?(0,d.jsx)(A,{unmountOnExit:!0,...b,ref:void 0,in:a,children:v}):a?v:null});b.displayName="Alert";const y=Object.assign(b,{Link:g,Heading:u})},2053:(e,r,s)=>{s.r(r),s.d(r,{default:()=>u});var a=s(5043),l=s(3519),t=s(1072),n=s(8602),i=s(8628),o=s(1719),c=s(4282),d=s(4117),f=s(4312),m=s(579);const u=()=>{const{t:e}=(0,d.Bd)(),[r,s]=(0,a.useState)(null),[u,x]=(0,a.useState)(!0);(0,a.useEffect)(()=>{(async()=>{const e=(0,f.b)();if(!e)return s({error:"Supabase not initialized"}),void x(!1);x(!0);const{data:{user:r}}=await e.auth.getUser();if(!r)return s({error:"User not logged in"}),void x(!1);const a={userId:r.id,userEmail:r.email,roleFromLocalStorage:localStorage.getItem("user_role"),userMetadata:r.user_metadata};try{const{data:s,error:l}=await e.from("users").select("*").eq("id",r.id).single();a.usersTableData=s,a.usersTableError=l}catch(l){a.usersTableError=l}try{const{data:s,error:l}=await e.from("agent_profiles").select("*").eq("user_id",r.id).single();a.agentProfileData=s,a.agentProfileError=l}catch(l){a.agentProfileError=l}try{const{data:s,error:l}=await e.from("maker_profiles").select("*").eq("user_id",r.id).single();a.makerProfileData=s,a.makerProfileError=l}catch(l){a.makerProfileError=l}s(a),x(!1)})()},[]);return u?(0,m.jsx)("div",{children:"Loading debug information..."}):(0,m.jsxs)(l.A,{fluid:!0,children:[(0,m.jsx)(t.A,{className:"mb-3",children:(0,m.jsx)(n.A,{children:(0,m.jsx)("h2",{children:"Agent Debug Information"})})}),r&&(0,m.jsx)(t.A,{children:(0,m.jsxs)(n.A,{children:[(0,m.jsxs)(i.A,{className:"mb-3",children:[(0,m.jsx)(i.A.Header,{children:"User Information"}),(0,m.jsxs)(i.A.Body,{children:[(0,m.jsxs)("p",{children:[(0,m.jsx)("strong",{children:"User ID:"})," ",r.userId]}),(0,m.jsxs)("p",{children:[(0,m.jsx)("strong",{children:"Email:"})," ",r.userEmail]}),(0,m.jsxs)("p",{children:[(0,m.jsx)("strong",{children:"Role from localStorage:"})," ",r.roleFromLocalStorage]}),(0,m.jsxs)("p",{children:[(0,m.jsx)("strong",{children:"User Metadata:"})," ",JSON.stringify(r.userMetadata,null,2)]})]})]}),(0,m.jsxs)(i.A,{className:"mb-3",children:[(0,m.jsx)(i.A.Header,{children:"Users Table Data"}),(0,m.jsx)(i.A.Body,{children:r.usersTableError?(0,m.jsxs)(o.A,{variant:"danger",children:["Error: ",JSON.stringify(r.usersTableError,null,2)]}):(0,m.jsx)("pre",{children:JSON.stringify(r.usersTableData,null,2)})})]}),(0,m.jsxs)(i.A,{className:"mb-3",children:[(0,m.jsx)(i.A.Header,{children:"Agent Profile Data"}),(0,m.jsx)(i.A.Body,{children:r.agentProfileError?(0,m.jsxs)(o.A,{variant:"danger",children:["Error: ",JSON.stringify(r.agentProfileError,null,2),"PGRST116"===r.agentProfileError.code&&(0,m.jsxs)("div",{className:"mt-2",children:[(0,m.jsx)("p",{children:"No agent profile found. This is likely the cause of the issue."}),(0,m.jsx)(c.A,{onClick:async()=>{const e=(0,f.b)();if(e&&r)try{const{data:s,error:a}=await e.from("agent_profiles").insert([{user_id:r.userId,brand_name:"Default Agent",commission_pct:.05,kyc_status:"pending"}]).select().single();a?alert("Error creating agent profile: "+a.message):(alert("Agent profile created successfully!"),window.location.reload())}catch(s){alert("Error: "+s.message)}},variant:"primary",children:"Create Agent Profile"})]})]}):(0,m.jsx)("pre",{children:JSON.stringify(r.agentProfileData,null,2)})})]}),(0,m.jsxs)(i.A,{className:"mb-3",children:[(0,m.jsx)(i.A.Header,{children:"Maker Profile Data (for reference)"}),(0,m.jsx)(i.A.Body,{children:r.makerProfileError?(0,m.jsxs)(o.A,{variant:"warning",children:["Error: ",JSON.stringify(r.makerProfileError,null,2)]}):(0,m.jsx)("pre",{children:JSON.stringify(r.makerProfileData,null,2)})})]})]})})]})}},8602:(e,r,s)=>{s.d(r,{A:()=>c});var a=s(8139),l=s.n(a),t=s(5043),n=s(7852),i=s(579);const o=t.forwardRef((e,r)=>{const[{className:s,...a},{as:t="div",bsPrefix:o,spans:c}]=function(e){let{as:r,bsPrefix:s,className:a,...t}=e;s=(0,n.oU)(s,"col");const i=(0,n.gy)(),o=(0,n.Jm)(),c=[],d=[];return i.forEach(e=>{const r=t[e];let a,l,n;delete t[e],"object"===typeof r&&null!=r?({span:a,offset:l,order:n}=r):a=r;const i=e!==o?`-${e}`:"";a&&c.push(!0===a?`${s}${i}`:`${s}${i}-${a}`),null!=n&&d.push(`order${i}-${n}`),null!=l&&d.push(`offset${i}-${l}`)}),[{...t,className:l()(a,...c,...d)},{as:r,bsPrefix:s,spans:c}]}(e);return(0,i.jsx)(t,{...a,ref:r,className:l()(s,!c.length&&o)})});o.displayName="Col";const c=o},8628:(e,r,s)=>{s.d(r,{A:()=>R});var a=s(8139),l=s.n(a),t=s(5043),n=s(7852),i=s(579);const o=t.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:t="div",...o}=e;return a=(0,n.oU)(a,"card-body"),(0,i.jsx)(t,{ref:r,className:l()(s,a),...o})});o.displayName="CardBody";const c=o,d=t.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:t="div",...o}=e;return a=(0,n.oU)(a,"card-footer"),(0,i.jsx)(t,{ref:r,className:l()(s,a),...o})});d.displayName="CardFooter";const f=d;var m=s(1778);const u=t.forwardRef((e,r)=>{let{bsPrefix:s,className:a,as:o="div",...c}=e;const d=(0,n.oU)(s,"card-header"),f=(0,t.useMemo)(()=>({cardHeaderBsPrefix:d}),[d]);return(0,i.jsx)(m.A.Provider,{value:f,children:(0,i.jsx)(o,{ref:r,...c,className:l()(a,d)})})});u.displayName="CardHeader";const x=u,h=t.forwardRef((e,r)=>{let{bsPrefix:s,className:a,variant:t,as:o="img",...c}=e;const d=(0,n.oU)(s,"card-img");return(0,i.jsx)(o,{ref:r,className:l()(t?`${d}-${t}`:d,a),...c})});h.displayName="CardImg";const g=h,N=t.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:t="div",...o}=e;return a=(0,n.oU)(a,"card-img-overlay"),(0,i.jsx)(t,{ref:r,className:l()(s,a),...o})});N.displayName="CardImgOverlay";const j=N,b=t.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:t="a",...o}=e;return a=(0,n.oU)(a,"card-link"),(0,i.jsx)(t,{ref:r,className:l()(s,a),...o})});b.displayName="CardLink";const y=b;var p=s(4488);const A=(0,p.A)("h6"),v=t.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:t=A,...o}=e;return a=(0,n.oU)(a,"card-subtitle"),(0,i.jsx)(t,{ref:r,className:l()(s,a),...o})});v.displayName="CardSubtitle";const P=v,w=t.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:t="p",...o}=e;return a=(0,n.oU)(a,"card-text"),(0,i.jsx)(t,{ref:r,className:l()(s,a),...o})});w.displayName="CardText";const E=w,$=(0,p.A)("h5"),k=t.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:t=$,...o}=e;return a=(0,n.oU)(a,"card-title"),(0,i.jsx)(t,{ref:r,className:l()(s,a),...o})});k.displayName="CardTitle";const U=k,C=t.forwardRef((e,r)=>{let{bsPrefix:s,className:a,bg:t,text:o,border:d,body:f=!1,children:m,as:u="div",...x}=e;const h=(0,n.oU)(s,"card");return(0,i.jsx)(u,{ref:r,...x,className:l()(a,h,t&&`bg-${t}`,o&&`text-${o}`,d&&`border-${d}`),children:f?(0,i.jsx)(c,{children:m}):m})});C.displayName="Card";const R=Object.assign(C,{Img:g,Title:U,Subtitle:P,Body:c,Link:y,Text:E,Header:x,Footer:f,ImgOverlay:j})}}]);
//# sourceMappingURL=53.3199adeb.chunk.js.map