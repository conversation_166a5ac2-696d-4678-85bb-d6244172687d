import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const Transactions = () => {
    const { t } = useTranslation();
    const [transactions, setTransactions] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchTransactions = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // Fetch transactions with user information
            const { data, error } = await supabase
                .from('transactions')
                .select(`
                    id,
                    tx_date,
                    sender_user_id,
                    receiver_user_id,
                    amount_net,
                    tx_type,
                    filecoin_msg_id,
                    agent_id,
                    created_at,
                    sender:users!sender_user_id (
                        email
                    ),
                    receiver:users!receiver_user_id (
                        email
                    ),
                    agent:agent_profiles!agent_id (
                        user_id,
                        users (
                            email
                        )
                    )
                `)
                .order('tx_date', { ascending: false });

            if (error) {
                console.error('Error fetching transactions:', error);
            } else {
                setTransactions(data);
            }
            setLoading(false);
        };

        fetchTransactions();
    }, []);

    const getTransactionTypeColor = (txType) => {
        switch (txType) {
            case 'deposit':
                return 'success';
            case 'withdrawal':
                return 'danger';
            case 'transfer':
                return 'primary';
            case 'reward':
                return 'info';
            default:
                return 'secondary';
        }
    };

    if (loading) {
        return <div>{t('loading_transactions')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('transactions')}</h2>
            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('transaction_id')}</th>
                                        <th>{t('tx_date')}</th>
                                        <th>{t('sender')}</th>
                                        <th>{t('receiver')}</th>
                                        <th>{t('amount')}</th>
                                        <th>{t('tx_type')}</th>
                                        <th>{t('filecoin_msg_id')}</th>
                                        <th>{t('agent')}</th>
                                        <th>{t('created_at')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {transactions.length === 0 ? (
                                        <tr>
                                            <td colSpan="9" className="text-center">{t('no_transactions_available')}</td>
                                        </tr>
                                    ) : (
                                        transactions.map((transaction) => (
                                            <tr key={transaction.id}>
                                                <td>{transaction.id.substring(0, 8)}...</td>
                                                <td>{new Date(transaction.tx_date).toLocaleString()}</td>
                                                <td>{transaction.sender?.email || '-'}</td>
                                                <td>{transaction.receiver?.email || '-'}</td>
                                                <td>{transaction.amount_net ? Number(transaction.amount_net).toFixed(6) : '0'} FIL</td>
                                                <td>
                                                    <Badge bg={getTransactionTypeColor(transaction.tx_type)}>
                                                        {transaction.tx_type || 'unknown'}
                                                    </Badge>
                                                </td>
                                                <td>
                                                    {transaction.filecoin_msg_id ? (
                                                        <span title={transaction.filecoin_msg_id}>
                                                            {transaction.filecoin_msg_id.substring(0, 10)}...
                                                        </span>
                                                    ) : '-'}
                                                </td>
                                                <td>{transaction.agent?.users?.email || '-'}</td>
                                                <td>{new Date(transaction.created_at).toLocaleString()}</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default Transactions;
