import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge, ProgressBar } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const OrderDistributions = () => {
    const { t } = useTranslation();
    const [distributions, setDistributions] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchDistributions = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // Fetch order distributions with related information
            const { data, error } = await supabase
                .from('order_distributions')
                .select(`
                    id,
                    share_amount,
                    reward_amount,
                    fee_amount,
                    progress,
                    created_at,
                    distribution_batches (
                        id,
                        currency_code,
                        batch_amount,
                        per_share_amount,
                        status,
                        distributed_at,
                        products (
                            name,
                            category
                        )
                    ),
                    orders (
                        id,
                        cid,
                        shares,
                        products (
                            name,
                            category
                        )
                    ),
                    customer_profiles (
                        real_name,
                        users (
                            email
                        )
                    )
                `)
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Error fetching order distributions:', error);
            } else {
                setDistributions(data);
            }
            setLoading(false);
        };

        fetchDistributions();
    }, []);

    const getStatusBadge = (status) => {
        switch (status) {
            case 'completed':
                return <Badge bg="success">{t('completed')}</Badge>;
            case 'pending':
                return <Badge bg="warning">{t('pending')}</Badge>;
            case 'processing':
                return <Badge bg="info">{t('processing')}</Badge>;
            default:
                return <Badge bg="secondary">{status || '-'}</Badge>;
        }
    };

    if (loading) {
        return <div>{t('loading_order_distributions')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('order_distributions')}</h2>
            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('distribution_id')}</th>
                                        <th>{t('batch_id')}</th>
                                        <th>{t('order_id')}</th>
                                        <th>{t('customer')}</th>
                                        <th>{t('product_name')}</th>
                                        <th>{t('currency_code')}</th>
                                        <th>{t('share_amount')}</th>
                                        <th>{t('reward_amount')}</th>
                                        <th>{t('fee_amount')}</th>
                                        <th>{t('progress')}</th>
                                        <th>{t('batch_status')}</th>
                                        <th>{t('created_at')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {distributions.length === 0 ? (
                                        <tr>
                                            <td colSpan="12" className="text-center">{t('no_order_distributions_available')}</td>
                                        </tr>
                                    ) : (
                                        distributions.map(distribution => (
                                            <tr key={distribution.id}>
                                                <td>{distribution.id}</td>
                                                <td>{distribution.distribution_batches?.id || '-'}</td>
                                                <td>{distribution.orders?.cid || distribution.orders?.id || '-'}</td>
                                                <td>
                                                    <div>
                                                        <div>{distribution.customer_profiles?.real_name || '-'}</div>
                                                        <small className="text-muted">
                                                            {distribution.customer_profiles?.users?.email || '-'}
                                                        </small>
                                                    </div>
                                                </td>
                                                <td>
                                                    {distribution.orders?.products?.name || 
                                                     distribution.distribution_batches?.products?.name || '-'}
                                                </td>
                                                <td>{distribution.distribution_batches?.currency_code || '-'}</td>
                                                <td>{distribution.share_amount?.toFixed(2) || '0.00'}</td>
                                                <td>{distribution.reward_amount?.toFixed(6) || '0.000000'}</td>
                                                <td>{distribution.fee_amount?.toFixed(6) || '0.000000'}</td>
                                                <td>
                                                    <div>
                                                        <ProgressBar 
                                                            now={distribution.progress * 100} 
                                                            label={`${(distribution.progress * 100).toFixed(1)}%`}
                                                            style={{ minWidth: '100px' }}
                                                        />
                                                    </div>
                                                </td>
                                                <td>{getStatusBadge(distribution.distribution_batches?.status)}</td>
                                                <td>{new Date(distribution.created_at).toLocaleString()}</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default OrderDistributions;
