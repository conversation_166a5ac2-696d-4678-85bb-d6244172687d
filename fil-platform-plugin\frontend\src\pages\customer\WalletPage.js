
import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Button, Nav } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const WalletPage = () => {
    const { t } = useTranslation();
    const [assets, setAssets] = useState([]);
    const [loading, setLoading] = useState(true);
    const [activeTab, setActiveTab] = useState('overview'); // overview, deposit, withdraw, exchange

    useEffect(() => {
        const fetchAssets = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            const { data, error } = await supabase
                .from('user_assets')
                .select('*')
                .eq('user_id', user.id);

            if (error) {
                console.error('Error fetching assets:', error);
            } else {
                setAssets(data);
            }
            setLoading(false);
        };

        fetchAssets();
    }, []);

    if (loading) {
        return <div>{t('loading_wallet')}</div>;
    }

    const renderContent = () => {
        switch (activeTab) {
            case 'overview':
                return (
                    <Table striped bordered hover responsive>
                        <thead>
                            <tr>
                                <th>{t('currency')}</th>
                                <th>{t('available_balance')}</th>
                                <th>{t('locked_balance')}</th>
                                <th>{t('total_balance')}</th>
                                <th>{t('withdrawn')}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {assets.length === 0 ? (
                                <tr>
                                    <td colSpan="5" className="text-center">{t('no_assets')}</td>
                                </tr>
                            ) : (
                                assets.map(asset => (
                                    <tr key={asset.currency_code}>
                                        <td>{asset.currency_code}</td>
                                        <td>{asset.available_balance}</td>
                                        <td>{asset.balance_locked}</td>
                                        <td>{asset.balance_total}</td>
                                        <td>{asset.withdrawn_total}</td>
                                    </tr>
                                ))
                            )}
                        </tbody>
                    </Table>
                );
            case 'deposit':
                return <div>{t('deposit_coming_soon')}</div>; // Placeholder for deposit UI
            case 'withdraw':
                return <div>{t('withdraw_coming_soon')}</div>; // Placeholder for withdraw UI
            case 'exchange':
                return <div>{t('exchange_coming_soon')}</div>; // Placeholder for exchange UI
            default:
                return null;
        }
    };

    return (
        <Container>
            <h2 className="mb-4">{t('my_wallet')}</h2>
            <Row>
                <Col>
                    <Card>
                        <Card.Header>
                            <Nav variant="tabs" defaultActiveKey="overview" onSelect={(selectedKey) => setActiveTab(selectedKey)}>
                                <Nav.Item>
                                    <Nav.Link eventKey="overview">{t('overview')}</Nav.Link>
                                </Nav.Item>
                                <Nav.Item>
                                    <Nav.Link eventKey="deposit">{t('deposit')}</Nav.Link>
                                </Nav.Item>
                                <Nav.Item>
                                    <Nav.Link eventKey="withdraw">{t('withdraw')}</Nav.Link>
                                </Nav.Item>
                                <Nav.Item>
                                    <Nav.Link eventKey="exchange">{t('exchange')}</Nav.Link>
                                </Nav.Item>
                            </Nav>
                        </Card.Header>
                        <Card.Body>
                            {renderContent()}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default WalletPage;
