#!/usr/bin/env node

/**
 * Monitor script for Filfox Scraper
 * 
 * This script checks the health of the scraper and database
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

function log(message) {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] ${message}`);
}

function logError(message, error) {
    const timestamp = new Date().toISOString();
    console.error(`[${timestamp}] ERROR: ${message}`, error);
}

/**
 * Check database connectivity
 */
async function checkDatabase() {
    try {
        log('Checking database connectivity...');
        
        const { data, error } = await supabase
            .from('network_stats')
            .select('count')
            .limit(1);

        if (error) {
            throw error;
        }

        log('✅ Database connectivity: OK');
        return true;
    } catch (error) {
        logError('❌ Database connectivity: FAILED', error);
        return false;
    }
}

/**
 * Check recent data
 */
async function checkRecentData() {
    try {
        log('Checking recent data...');
        
        const today = new Date().toISOString().split('T')[0];
        const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0];
        
        const { data, error } = await supabase
            .from('network_stats')
            .select('*')
            .in('stat_date', [today, yesterday])
            .order('stat_date', { ascending: false });

        if (error) {
            throw error;
        }

        if (data.length === 0) {
            log('⚠️  No recent data found');
            return false;
        }

        log(`✅ Found ${data.length} recent records:`);
        data.forEach(record => {
            log(`   ${record.stat_date}: Height=${record.blockchain_height}, Reward=${record.fil_per_tib}`);
        });

        return true;
    } catch (error) {
        logError('❌ Recent data check: FAILED', error);
        return false;
    }
}

/**
 * Check data quality
 */
async function checkDataQuality() {
    try {
        log('Checking data quality...');
        
        const { data, error } = await supabase
            .from('network_stats')
            .select('*')
            .order('stat_date', { ascending: false })
            .limit(7); // Last 7 days

        if (error) {
            throw error;
        }

        if (data.length === 0) {
            log('⚠️  No data available for quality check');
            return false;
        }

        let issues = [];

        data.forEach(record => {
            // Check block height
            if (!record.blockchain_height || record.blockchain_height < 2000000) {
                issues.push(`Invalid block height on ${record.stat_date}: ${record.blockchain_height}`);
            }

            // Check mining reward
            if (!record.fil_per_tib || record.fil_per_tib < 0 || record.fil_per_tib > 100) {
                issues.push(`Invalid mining reward on ${record.stat_date}: ${record.fil_per_tib}`);
            }
        });

        if (issues.length > 0) {
            log('⚠️  Data quality issues found:');
            issues.forEach(issue => log(`   - ${issue}`));
            return false;
        }

        log('✅ Data quality: OK');
        return true;
    } catch (error) {
        logError('❌ Data quality check: FAILED', error);
        return false;
    }
}

/**
 * Generate health report
 */
async function generateHealthReport() {
    log('=== Filfox Scraper Health Check ===');
    
    const checks = [
        { name: 'Database Connectivity', fn: checkDatabase },
        { name: 'Recent Data', fn: checkRecentData },
        { name: 'Data Quality', fn: checkDataQuality }
    ];

    let allPassed = true;
    const results = [];

    for (const check of checks) {
        try {
            const result = await check.fn();
            results.push({ name: check.name, passed: result });
            if (!result) allPassed = false;
        } catch (error) {
            results.push({ name: check.name, passed: false, error: error.message });
            allPassed = false;
        }
    }

    log('\n=== Health Check Summary ===');
    results.forEach(result => {
        const status = result.passed ? '✅ PASS' : '❌ FAIL';
        log(`${status} - ${result.name}`);
        if (result.error) {
            log(`   Error: ${result.error}`);
        }
    });

    log(`\nOverall Status: ${allPassed ? '✅ HEALTHY' : '❌ UNHEALTHY'}`);
    
    return allPassed;
}

/**
 * Main function
 */
async function main() {
    try {
        // Check environment variables
        if (!supabaseUrl || !supabaseServiceKey) {
            throw new Error('Missing required environment variables: SUPABASE_URL or SUPABASE_SERVICE_KEY');
        }

        const isHealthy = await generateHealthReport();
        process.exit(isHealthy ? 0 : 1);

    } catch (error) {
        logError('Monitor script failed', error);
        process.exit(1);
    }
}

// Run the monitor
if (require.main === module) {
    main();
}
