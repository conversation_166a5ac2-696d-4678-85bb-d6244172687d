{"ast": null, "code": "import React,{useState,useEffect}from'react';import{<PERSON><PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,Spinner,Al<PERSON>,But<PERSON>,Form}from'react-bootstrap';import{FaChevronDown,FaChevronRight,FaUser,FaUsers,FaExpandArrowsAlt,FaCompressArrowsAlt,FaSearch}from'react-icons/fa';import{getSupabase}from'../../supabaseClient';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Recommend=()=>{const{t}=useTranslation();const[loading,setLoading]=useState(true);const[referralTree,setReferralTree]=useState([]);const[filteredTree,setFilteredTree]=useState([]);const[expandedNodes,setExpandedNodes]=useState(new Set());const[error,setError]=useState(null);const[searchTerm,setSearchTerm]=useState('');useEffect(()=>{const fetchReferralData=async()=>{const supabase=getSupabase();if(!supabase)return;setLoading(true);setError(null);let currentUser=null;let customers=[];let customersError=null;try{const{data:{user}}=await supabase.auth.getUser();currentUser=user;if(!user){setError(t('user_not_logged_in'));setLoading(false);return;}// First, get the agent's maker_id to determine scope\nconst{data:agentProfile,error:agentError}=await supabase.from('agent_profiles').select('maker_id').eq('user_id',user.id).single();if(agentError){console.error('Error fetching agent profile:',agentError);setError(t('agent_profile_not_found'));setLoading(false);return;}// Step 1: 获取 customer_profiles 列表\nconst{data:customerProfiles,error:profileError}=await supabase.from('customer_profiles').select('user_id')// 不嵌套 users 表\n.eq('agent_id',user.id).limit(100);if(profileError){console.error('Error fetching customer profiles:',profileError);setError(t('failed_to_load_referral_data'));setLoading(false);return;}// Step 2: 用 user_id 去单独获取 users 表\nconst userIds=customerProfiles.map(p=>p.user_id).filter(Boolean);const{data:usersData,error:usersError}=await supabase.from('users').select('id, email, referred_by, created_at, role').in('id',userIds);if(usersError){console.error('Error fetching user info:',usersError);setError(t('failed_to_load_referral_data'));setLoading(false);return;}if(customersError){console.error('Error fetching customers:',customersError);setError(t('failed_to_load_referral_data'));setLoading(false);return;}// Extract user data from customers\nconst customerUsers=usersData||[];// Get referrer information recursively (up to 3 levels to avoid infinite loops)\nconst fetchReferrers=async function(userIds){let level=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;let maxLevel=arguments.length>2&&arguments[2]!==undefined?arguments[2]:3;if(level>=maxLevel||userIds.length===0){return[];}try{const{data:referrerData,error:referrerError}=await supabase.from('users').select('id, email, referred_by, created_at, role').in('id',userIds).limit(50);// Limit each level to prevent large queries\nif(referrerError){console.error(`Error fetching referrers at level ${level}:`,referrerError);return[];}const referrers=referrerData||[];// Get next level referrer IDs\nconst nextLevelIds=[...new Set(referrers.map(u=>u.referred_by).filter(Boolean))];// Recursively fetch next level\nconst nextLevelReferrers=await fetchReferrers(nextLevelIds,level+1,maxLevel);return[...referrers,...nextLevelReferrers];}catch(error){console.error(`Error in fetchReferrers at level ${level}:`,error);return[];}};// Get unique referred_by IDs from customers\nconst initialReferrerIds=[...new Set(customerUsers.map(u=>u.referred_by).filter(Boolean))];const referrers=await fetchReferrers(initialReferrerIds);// Combine customer users and their referrers\nconst allUsers=[...customerUsers,...referrers];// Remove duplicates based on user ID\nconst uniqueUsers=allUsers.filter((user,index,self)=>index===self.findIndex(u=>u.id===user.id));// Build the referral tree\nconst tree=buildReferralTree(uniqueUsers);setReferralTree(tree);setFilteredTree(tree);}catch(err){console.error('Error:',err);// Fallback: try to get just the direct customers without referrer data\ntry{var _currentUser;console.log('Attempting fallback query...');// Use currentUser if available, otherwise try to get user again\nconst userIdToUse=(_currentUser=currentUser)===null||_currentUser===void 0?void 0:_currentUser.id;if(!userIdToUse){const{data:{user:fallbackUser}}=await supabase.auth.getUser();if(!fallbackUser){setError(t('user_not_logged_in'));return;}currentUser=fallbackUser;}const{data:simpleCustomers,error:simpleError}=await supabase.from('customer_profiles').select(`\n                            users (\n                                id,\n                                email,\n                                created_at,\n                                role\n                            )\n                        `).eq('agent_id',currentUser.id).limit(50);// Further limit for fallback\nif(!simpleError&&simpleCustomers&&simpleCustomers.length>0){const simpleUsers=simpleCustomers.map(c=>c.users).filter(Boolean).map(u=>({...u,referred_by:null,children:[]}));// Remove referral info for simplicity\nconsole.log('Fallback successful, loaded',simpleUsers.length,'users');setReferralTree(simpleUsers);setFilteredTree(simpleUsers);setError(t('limited_referral_data'));}else{console.log('Fallback failed or no data:',simpleError);setReferralTree([]);setFilteredTree([]);setError(t('no_referral_data'));}}catch(fallbackErr){console.error('Fallback error:',fallbackErr);setReferralTree([]);setFilteredTree([]);setError(t('unexpected_error'));}}finally{setLoading(false);}};fetchReferralData();},[t]);// Filter tree based on search term\nuseEffect(()=>{if(!searchTerm.trim()){setFilteredTree(referralTree);return;}const filterTree=nodes=>{return nodes.filter(node=>{const matchesSearch=node.email.toLowerCase().includes(searchTerm.toLowerCase())||node.id.toLowerCase().includes(searchTerm.toLowerCase());const filteredChildren=node.children?filterTree(node.children):[];return matchesSearch||filteredChildren.length>0;}).map(node=>({...node,children:node.children?filterTree(node.children):[]}));};setFilteredTree(filterTree(referralTree));},[searchTerm,referralTree]);const buildReferralTree=users=>{if(!users||users.length===0){return[];}const userMap=new Map();const rootUsers=[];// Create a map of all users\nusers.forEach(user=>{userMap.set(user.id,{...user,children:[]});});// Build the tree structure\nusers.forEach(user=>{if(user.referred_by){const parent=userMap.get(user.referred_by);if(parent){parent.children.push(userMap.get(user.id));}else{// Parent not found, treat as root\nrootUsers.push(userMap.get(user.id));}}else{// No referrer, this is a root user\nrootUsers.push(userMap.get(user.id));}});return rootUsers;};const toggleNode=userId=>{const newExpanded=new Set(expandedNodes);if(newExpanded.has(userId)){newExpanded.delete(userId);}else{newExpanded.add(userId);}setExpandedNodes(newExpanded);};const expandAll=()=>{const allNodeIds=new Set();const collectNodeIds=nodes=>{nodes.forEach(node=>{if(node.children&&node.children.length>0){allNodeIds.add(node.id);collectNodeIds(node.children);}});};collectNodeIds(filteredTree);setExpandedNodes(allNodeIds);};const collapseAll=()=>{setExpandedNodes(new Set());};const formatUserId=id=>{return id.substring(0,8);};const getRoleIcon=role=>{switch(role){case'maker':return/*#__PURE__*/_jsx(FaUsers,{className:\"text-primary me-1\"});case'agent':return/*#__PURE__*/_jsx(FaUser,{className:\"text-success me-1\"});case'customer':return/*#__PURE__*/_jsx(FaUser,{className:\"text-info me-1\"});default:return/*#__PURE__*/_jsx(FaUser,{className:\"text-secondary me-1\"});}};const renderTreeNode=function(node){let level=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;const hasChildren=node.children&&node.children.length>0;const isExpanded=expandedNodes.has(node.id);const paddingLeft=level*20;return/*#__PURE__*/_jsxs(\"div\",{className:\"mb-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center p-2 border-bottom\",style:{paddingLeft:`${paddingLeft}px`,cursor:hasChildren?'pointer':'default'},onClick:()=>hasChildren&&toggleNode(node.id),children:[hasChildren?isExpanded?/*#__PURE__*/_jsx(FaChevronDown,{className:\"me-2 text-muted\"}):/*#__PURE__*/_jsx(FaChevronRight,{className:\"me-2 text-muted\"}):/*#__PURE__*/_jsx(\"span\",{className:\"me-4\"}),getRoleIcon(node.role),/*#__PURE__*/_jsx(\"span\",{className:\"me-2\",children:node.email}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-muted small\",children:[\"[\",formatUserId(node.id),\"]\"]}),hasChildren&&/*#__PURE__*/_jsx(\"span\",{className:\"ms-auto badge bg-secondary\",children:node.children.length})]}),hasChildren&&isExpanded&&/*#__PURE__*/_jsx(\"div\",{children:node.children.map(child=>renderTreeNode(child,level+1))})]},node.id);};const getTotalUsers=nodes=>{let total=0;const countNodes=nodeList=>{nodeList.forEach(node=>{total++;if(node.children&&node.children.length>0){countNodes(node.children);}});};countNodes(nodes);return total;};const getRootUsersCount=()=>{return filteredTree.length;};if(loading){return/*#__PURE__*/_jsx(Container,{className:\"d-flex justify-content-center align-items-center\",style:{minHeight:'400px'},children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\",role:\"status\",className:\"mb-3\"}),/*#__PURE__*/_jsx(\"div\",{children:t('loading_referral_data')})]})});}if(error){return/*#__PURE__*/_jsx(Container,{children:/*#__PURE__*/_jsx(Alert,{variant:\"danger\",children:error})});}return/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(Row,{className:\"mb-4\",children:/*#__PURE__*/_jsxs(Col,{children:[/*#__PURE__*/_jsx(\"h2\",{children:t('referral_relationships')}),/*#__PURE__*/_jsx(\"p\",{className:\"text-muted\",children:t('referral_tree_description')})]})}),/*#__PURE__*/_jsx(Row,{className:\"mb-3\",children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Row,{className:\"align-items-end\",children:[/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsxs(Form.Group,{children:[/*#__PURE__*/_jsx(Form.Label,{children:t('search_users')}),/*#__PURE__*/_jsx(Form.Control,{type:\"text\",placeholder:t('search_by_email_or_id'),value:searchTerm,onChange:e=>setSearchTerm(e.target.value)})]})}),/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex gap-2\",children:[/*#__PURE__*/_jsxs(Button,{variant:\"outline-primary\",size:\"sm\",onClick:expandAll,children:[/*#__PURE__*/_jsx(FaExpandArrowsAlt,{className:\"me-1\"}),t('expand_all')]}),/*#__PURE__*/_jsxs(Button,{variant:\"outline-secondary\",size:\"sm\",onClick:collapseAll,children:[/*#__PURE__*/_jsx(FaCompressArrowsAlt,{className:\"me-1\"}),t('collapse_all')]})]})})]})})})})}),/*#__PURE__*/_jsxs(Row,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsx(Card,{className:\"bg-primary text-white\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"h5\",{children:t('total_users')}),/*#__PURE__*/_jsx(\"h3\",{children:getTotalUsers(filteredTree)})]})})}),/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsx(Card,{className:\"bg-success text-white\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"h5\",{children:t('root_users')}),/*#__PURE__*/_jsx(\"h3\",{children:getRootUsersCount()})]})})}),/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsx(Card,{className:\"bg-info text-white\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"h5\",{children:t('expanded_nodes')}),/*#__PURE__*/_jsx(\"h3\",{children:expandedNodes.size})]})})})]}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(Card.Header,{children:[/*#__PURE__*/_jsx(\"h5\",{className:\"mb-0\",children:t('referral_tree')}),/*#__PURE__*/_jsx(\"small\",{className:\"text-muted\",children:t('click_to_expand_collapse')})]}),/*#__PURE__*/_jsx(Card.Body,{style:{maxHeight:'600px',overflowY:'auto'},children:filteredTree.length===0?/*#__PURE__*/_jsx(\"div\",{className:\"text-center text-muted py-4\",children:searchTerm?t('no_search_results'):t('no_referral_data')}):/*#__PURE__*/_jsx(\"div\",{children:filteredTree.map(node=>renderTreeNode(node))})})]})})})]});};export default Recommend;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Spinner", "<PERSON><PERSON>", "<PERSON><PERSON>", "Form", "FaChevronDown", "FaChevronRight", "FaUser", "FaUsers", "FaExpandArrowsAlt", "FaCompressArrowsAlt", "FaSearch", "getSupabase", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "Recommend", "t", "loading", "setLoading", "referralTree", "setReferralTree", "filteredTree", "setFilteredTree", "expandedNodes", "setExpandedNodes", "Set", "error", "setError", "searchTerm", "setSearchTerm", "fetchReferralData", "supabase", "currentUser", "customers", "customersError", "data", "user", "auth", "getUser", "agentProfile", "agent<PERSON><PERSON>r", "from", "select", "eq", "id", "single", "console", "customerProfiles", "profileError", "limit", "userIds", "map", "p", "user_id", "filter", "Boolean", "usersData", "usersError", "in", "customerUsers", "fetchReferrers", "level", "arguments", "length", "undefined", "maxLevel", "referrerData", "referrerError", "referrers", "nextLevelIds", "u", "referred_by", "nextLevelReferrers", "initialReferrerIds", "allUsers", "uniqueUsers", "index", "self", "findIndex", "tree", "buildReferralTree", "err", "_currentUser", "log", "userIdToUse", "fallbackUser", "simpleCustomers", "simpleError", "simpleUsers", "c", "users", "children", "fallbackErr", "trim", "filterTree", "nodes", "node", "matchesSearch", "email", "toLowerCase", "includes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "userMap", "Map", "rootUsers", "for<PERSON>ach", "set", "parent", "get", "push", "toggleNode", "userId", "newExpanded", "has", "delete", "add", "expandAll", "allNodeIds", "collectNodeIds", "collapseAll", "formatUserId", "substring", "getRoleIcon", "role", "className", "renderTreeNode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isExpanded", "paddingLeft", "style", "cursor", "onClick", "child", "getTotalUsers", "total", "countNodes", "nodeList", "getRootUsersCount", "minHeight", "animation", "variant", "Body", "md", "Group", "Label", "Control", "type", "placeholder", "value", "onChange", "e", "target", "size", "Header", "maxHeight", "overflowY"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/agent/Recommend.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Spinner, <PERSON><PERSON>, But<PERSON>, Form } from 'react-bootstrap';\nimport { FaChevronDown, FaChevronRight, FaUser, FaUsers, FaExpandArrowsAlt, FaCompressArrowsAlt, FaSearch } from 'react-icons/fa';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst Recommend = () => {\n    const { t } = useTranslation();\n    const [loading, setLoading] = useState(true);\n    const [referralTree, setReferralTree] = useState([]);\n    const [filteredTree, setFilteredTree] = useState([]);\n    const [expandedNodes, setExpandedNodes] = useState(new Set());\n    const [error, setError] = useState(null);\n    const [searchTerm, setSearchTerm] = useState('');\n\n    useEffect(() => {\n        const fetchReferralData = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            setError(null);\n\n            let currentUser = null;\n            let customers = [];\n            let customersError = null;\n\n\n            try {\n                const { data: { user } } = await supabase.auth.getUser();\n                currentUser = user;\n\n                if (!user) {\n                    setError(t('user_not_logged_in'));\n                    setLoading(false);\n                    return;\n                }\n\n                // First, get the agent's maker_id to determine scope\n                const { data: agentProfile, error: agentError } = await supabase\n                    .from('agent_profiles')\n                    .select('maker_id')\n                    .eq('user_id', user.id)\n                    .single();\n\n                if (agentError) {\n                    console.error('Error fetching agent profile:', agentError);\n                    setError(t('agent_profile_not_found'));\n                    setLoading(false);\n                    return;\n                }\n\n                // Step 1: 获取 customer_profiles 列表\n                const { data: customerProfiles, error: profileError } = await supabase\n                    .from('customer_profiles')\n                    .select('user_id')  // 不嵌套 users 表\n                    .eq('agent_id', user.id)\n                    .limit(100);\n\n                if (profileError) {\n                    console.error('Error fetching customer profiles:', profileError);\n                    setError(t('failed_to_load_referral_data'));\n                    setLoading(false);\n                    return;\n                }\n\n                // Step 2: 用 user_id 去单独获取 users 表\n                const userIds = customerProfiles.map(p => p.user_id).filter(Boolean);\n\n                const { data: usersData, error: usersError } = await supabase\n                    .from('users')\n                    .select('id, email, referred_by, created_at, role')\n                    .in('id', userIds);\n\n                if (usersError) {\n                    console.error('Error fetching user info:', usersError);\n                    setError(t('failed_to_load_referral_data'));\n                    setLoading(false);\n                    return;\n                }\n\n                if (customersError) {\n                    console.error('Error fetching customers:', customersError);\n                    setError(t('failed_to_load_referral_data'));\n                    setLoading(false);\n                    return;\n                }\n\n                // Extract user data from customers\n                const customerUsers = usersData || [];\n\n                // Get referrer information recursively (up to 3 levels to avoid infinite loops)\n                const fetchReferrers = async (userIds, level = 0, maxLevel = 3) => {\n                    if (level >= maxLevel || userIds.length === 0) {\n                        return [];\n                    }\n\n                    try {\n                        const { data: referrerData, error: referrerError } = await supabase\n                            .from('users')\n                            .select('id, email, referred_by, created_at, role')\n                            .in('id', userIds)\n                            .limit(50); // Limit each level to prevent large queries\n\n                        if (referrerError) {\n                            console.error(`Error fetching referrers at level ${level}:`, referrerError);\n                            return [];\n                        }\n\n                        const referrers = referrerData || [];\n\n                        // Get next level referrer IDs\n                        const nextLevelIds = [...new Set(referrers\n                            .map(u => u.referred_by)\n                            .filter(Boolean))];\n\n                        // Recursively fetch next level\n                        const nextLevelReferrers = await fetchReferrers(nextLevelIds, level + 1, maxLevel);\n\n                        return [...referrers, ...nextLevelReferrers];\n                    } catch (error) {\n                        console.error(`Error in fetchReferrers at level ${level}:`, error);\n                        return [];\n                    }\n                };\n\n                // Get unique referred_by IDs from customers\n                const initialReferrerIds = [...new Set(customerUsers\n                    .map(u => u.referred_by)\n                    .filter(Boolean))];\n\n                const referrers = await fetchReferrers(initialReferrerIds);\n\n                // Combine customer users and their referrers\n                const allUsers = [...customerUsers, ...referrers];\n\n                // Remove duplicates based on user ID\n                const uniqueUsers = allUsers.filter((user, index, self) =>\n                    index === self.findIndex(u => u.id === user.id)\n                );\n\n                // Build the referral tree\n                const tree = buildReferralTree(uniqueUsers);\n                setReferralTree(tree);\n                setFilteredTree(tree);\n\n            } catch (err) {\n                console.error('Error:', err);\n\n                // Fallback: try to get just the direct customers without referrer data\n                try {\n                    console.log('Attempting fallback query...');\n\n                    // Use currentUser if available, otherwise try to get user again\n                    const userIdToUse = currentUser?.id;\n                    if (!userIdToUse) {\n                        const { data: { user: fallbackUser } } = await supabase.auth.getUser();\n                        if (!fallbackUser) {\n                            setError(t('user_not_logged_in'));\n                            return;\n                        }\n                        currentUser = fallbackUser;\n                    }\n\n                    const { data: simpleCustomers, error: simpleError } = await supabase\n                        .from('customer_profiles')\n                        .select(`\n                            users (\n                                id,\n                                email,\n                                created_at,\n                                role\n                            )\n                        `)\n                        .eq('agent_id', currentUser.id)\n                        .limit(50); // Further limit for fallback\n\n                    if (!simpleError && simpleCustomers && simpleCustomers.length > 0) {\n                        const simpleUsers = simpleCustomers\n                            .map(c => c.users)\n                            .filter(Boolean)\n                            .map(u => ({\n                                ...u,\n                                referred_by: null,\n                                children: []\n                            })); // Remove referral info for simplicity\n\n                        console.log('Fallback successful, loaded', simpleUsers.length, 'users');\n                        setReferralTree(simpleUsers);\n                        setFilteredTree(simpleUsers);\n                        setError(t('limited_referral_data'));\n                    } else {\n                        console.log('Fallback failed or no data:', simpleError);\n                        setReferralTree([]);\n                        setFilteredTree([]);\n                        setError(t('no_referral_data'));\n                    }\n                } catch (fallbackErr) {\n                    console.error('Fallback error:', fallbackErr);\n                    setReferralTree([]);\n                    setFilteredTree([]);\n                    setError(t('unexpected_error'));\n                }\n            } finally {\n                setLoading(false);\n            }\n        };\n\n        fetchReferralData();\n    }, [t]);\n\n    // Filter tree based on search term\n    useEffect(() => {\n        if (!searchTerm.trim()) {\n            setFilteredTree(referralTree);\n            return;\n        }\n\n        const filterTree = (nodes) => {\n            return nodes.filter(node => {\n                const matchesSearch = node.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                                    node.id.toLowerCase().includes(searchTerm.toLowerCase());\n\n                const filteredChildren = node.children ? filterTree(node.children) : [];\n\n                return matchesSearch || filteredChildren.length > 0;\n            }).map(node => ({\n                ...node,\n                children: node.children ? filterTree(node.children) : []\n            }));\n        };\n\n        setFilteredTree(filterTree(referralTree));\n    }, [searchTerm, referralTree]);\n\n    const buildReferralTree = (users) => {\n        if (!users || users.length === 0) {\n            return [];\n        }\n\n        const userMap = new Map();\n        const rootUsers = [];\n\n        // Create a map of all users\n        users.forEach(user => {\n            userMap.set(user.id, {\n                ...user,\n                children: []\n            });\n        });\n\n        // Build the tree structure\n        users.forEach(user => {\n            if (user.referred_by) {\n                const parent = userMap.get(user.referred_by);\n                if (parent) {\n                    parent.children.push(userMap.get(user.id));\n                } else {\n                    // Parent not found, treat as root\n                    rootUsers.push(userMap.get(user.id));\n                }\n            } else {\n                // No referrer, this is a root user\n                rootUsers.push(userMap.get(user.id));\n            }\n        });\n\n        return rootUsers;\n    };\n\n    const toggleNode = (userId) => {\n        const newExpanded = new Set(expandedNodes);\n        if (newExpanded.has(userId)) {\n            newExpanded.delete(userId);\n        } else {\n            newExpanded.add(userId);\n        }\n        setExpandedNodes(newExpanded);\n    };\n\n    const expandAll = () => {\n        const allNodeIds = new Set();\n        const collectNodeIds = (nodes) => {\n            nodes.forEach(node => {\n                if (node.children && node.children.length > 0) {\n                    allNodeIds.add(node.id);\n                    collectNodeIds(node.children);\n                }\n            });\n        };\n        collectNodeIds(filteredTree);\n        setExpandedNodes(allNodeIds);\n    };\n\n    const collapseAll = () => {\n        setExpandedNodes(new Set());\n    };\n\n    const formatUserId = (id) => {\n        return id.substring(0, 8);\n    };\n\n    const getRoleIcon = (role) => {\n        switch (role) {\n            case 'maker':\n                return <FaUsers className=\"text-primary me-1\" />;\n            case 'agent':\n                return <FaUser className=\"text-success me-1\" />;\n            case 'customer':\n                return <FaUser className=\"text-info me-1\" />;\n            default:\n                return <FaUser className=\"text-secondary me-1\" />;\n        }\n    };\n\n    const renderTreeNode = (node, level = 0) => {\n        const hasChildren = node.children && node.children.length > 0;\n        const isExpanded = expandedNodes.has(node.id);\n        const paddingLeft = level * 20;\n\n        return (\n            <div key={node.id} className=\"mb-1\">\n                <div \n                    className=\"d-flex align-items-center p-2 border-bottom\"\n                    style={{ paddingLeft: `${paddingLeft}px`, cursor: hasChildren ? 'pointer' : 'default' }}\n                    onClick={() => hasChildren && toggleNode(node.id)}\n                >\n                    {hasChildren ? (\n                        isExpanded ? (\n                            <FaChevronDown className=\"me-2 text-muted\" />\n                        ) : (\n                            <FaChevronRight className=\"me-2 text-muted\" />\n                        )\n                    ) : (\n                        <span className=\"me-4\"></span>\n                    )}\n                    \n                    {getRoleIcon(node.role)}\n                    \n                    <span className=\"me-2\">\n                        {node.email}\n                    </span>\n                    \n                    <span className=\"text-muted small\">\n                        [{formatUserId(node.id)}]\n                    </span>\n                    \n                    {hasChildren && (\n                        <span className=\"ms-auto badge bg-secondary\">\n                            {node.children.length}\n                        </span>\n                    )}\n                </div>\n                \n                {hasChildren && isExpanded && (\n                    <div>\n                        {node.children.map(child => renderTreeNode(child, level + 1))}\n                    </div>\n                )}\n            </div>\n        );\n    };\n\n    const getTotalUsers = (nodes) => {\n        let total = 0;\n        const countNodes = (nodeList) => {\n            nodeList.forEach(node => {\n                total++;\n                if (node.children && node.children.length > 0) {\n                    countNodes(node.children);\n                }\n            });\n        };\n        countNodes(nodes);\n        return total;\n    };\n\n    const getRootUsersCount = () => {\n        return filteredTree.length;\n    };\n\n    if (loading) {\n        return (\n            <Container className=\"d-flex justify-content-center align-items-center\" style={{ minHeight: '400px' }}>\n                <div className=\"text-center\">\n                    <Spinner animation=\"border\" role=\"status\" className=\"mb-3\" />\n                    <div>{t('loading_referral_data')}</div>\n                </div>\n            </Container>\n        );\n    }\n\n    if (error) {\n        return (\n            <Container>\n                <Alert variant=\"danger\">\n                    {error}\n                </Alert>\n            </Container>\n        );\n    }\n\n    return (\n        <Container>\n            <Row className=\"mb-4\">\n                <Col>\n                    <h2>{t('referral_relationships')}</h2>\n                    <p className=\"text-muted\">{t('referral_tree_description')}</p>\n                </Col>\n            </Row>\n\n            <Row className=\"mb-3\">\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Row className=\"align-items-end\">\n                                <Col md={4}>\n                                    <Form.Group>\n                                        <Form.Label>{t('search_users')}</Form.Label>\n                                        <Form.Control\n                                            type=\"text\"\n                                            placeholder={t('search_by_email_or_id')}\n                                            value={searchTerm}\n                                            onChange={(e) => setSearchTerm(e.target.value)}\n                                        />\n                                    </Form.Group>\n                                </Col>\n                                <Col md={4}>\n                                    <div className=\"d-flex gap-2\">\n                                        <Button\n                                            variant=\"outline-primary\"\n                                            size=\"sm\"\n                                            onClick={expandAll}\n                                        >\n                                            <FaExpandArrowsAlt className=\"me-1\" />\n                                            {t('expand_all')}\n                                        </Button>\n                                        <Button\n                                            variant=\"outline-secondary\"\n                                            size=\"sm\"\n                                            onClick={collapseAll}\n                                        >\n                                            <FaCompressArrowsAlt className=\"me-1\" />\n                                            {t('collapse_all')}\n                                        </Button>\n                                    </div>\n                                </Col>\n                            </Row>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            <Row className=\"mb-3\">\n                <Col md={4}>\n                    <Card className=\"bg-primary text-white\">\n                        <Card.Body>\n                            <h5>{t('total_users')}</h5>\n                            <h3>{getTotalUsers(filteredTree)}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={4}>\n                    <Card className=\"bg-success text-white\">\n                        <Card.Body>\n                            <h5>{t('root_users')}</h5>\n                            <h3>{getRootUsersCount()}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={4}>\n                    <Card className=\"bg-info text-white\">\n                        <Card.Body>\n                            <h5>{t('expanded_nodes')}</h5>\n                            <h3>{expandedNodes.size}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Header>\n                            <h5 className=\"mb-0\">{t('referral_tree')}</h5>\n                            <small className=\"text-muted\">\n                                {t('click_to_expand_collapse')}\n                            </small>\n                        </Card.Header>\n                        <Card.Body style={{ maxHeight: '600px', overflowY: 'auto' }}>\n                            {filteredTree.length === 0 ? (\n                                <div className=\"text-center text-muted py-4\">\n                                    {searchTerm ? t('no_search_results') : t('no_referral_data')}\n                                </div>\n                            ) : (\n                                <div>\n                                    {filteredTree.map(node => renderTreeNode(node))}\n                                </div>\n                            )}\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default Recommend;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,OAAO,CAAEC,KAAK,CAAEC,MAAM,CAAEC,IAAI,KAAQ,iBAAiB,CACzF,OAASC,aAAa,CAAEC,cAAc,CAAEC,MAAM,CAAEC,OAAO,CAAEC,iBAAiB,CAAEC,mBAAmB,CAAEC,QAAQ,KAAQ,gBAAgB,CACjI,OAASC,WAAW,KAAQ,sBAAsB,CAClD,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,SAAS,CAAGA,CAAA,GAAM,CACpB,KAAM,CAAEC,CAAE,CAAC,CAAGN,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACO,OAAO,CAAEC,UAAU,CAAC,CAAG1B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC2B,YAAY,CAAEC,eAAe,CAAC,CAAG5B,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC6B,YAAY,CAAEC,eAAe,CAAC,CAAG9B,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC+B,aAAa,CAAEC,gBAAgB,CAAC,CAAGhC,QAAQ,CAAC,GAAI,CAAAiC,GAAG,CAAC,CAAC,CAAC,CAC7D,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGnC,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAACoC,UAAU,CAAEC,aAAa,CAAC,CAAGrC,QAAQ,CAAC,EAAE,CAAC,CAEhDC,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAqC,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CAClC,KAAM,CAAAC,QAAQ,CAAGtB,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACsB,QAAQ,CAAE,OAEfb,UAAU,CAAC,IAAI,CAAC,CAChBS,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAI,CAAAK,WAAW,CAAG,IAAI,CACtB,GAAI,CAAAC,SAAS,CAAG,EAAE,CAClB,GAAI,CAAAC,cAAc,CAAG,IAAI,CAGzB,GAAI,CACA,KAAM,CAAEC,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAL,QAAQ,CAACM,IAAI,CAACC,OAAO,CAAC,CAAC,CACxDN,WAAW,CAAGI,IAAI,CAElB,GAAI,CAACA,IAAI,CAAE,CACPT,QAAQ,CAACX,CAAC,CAAC,oBAAoB,CAAC,CAAC,CACjCE,UAAU,CAAC,KAAK,CAAC,CACjB,OACJ,CAEA;AACA,KAAM,CAAEiB,IAAI,CAAEI,YAAY,CAAEb,KAAK,CAAEc,UAAW,CAAC,CAAG,KAAM,CAAAT,QAAQ,CAC3DU,IAAI,CAAC,gBAAgB,CAAC,CACtBC,MAAM,CAAC,UAAU,CAAC,CAClBC,EAAE,CAAC,SAAS,CAAEP,IAAI,CAACQ,EAAE,CAAC,CACtBC,MAAM,CAAC,CAAC,CAEb,GAAIL,UAAU,CAAE,CACZM,OAAO,CAACpB,KAAK,CAAC,+BAA+B,CAAEc,UAAU,CAAC,CAC1Db,QAAQ,CAACX,CAAC,CAAC,yBAAyB,CAAC,CAAC,CACtCE,UAAU,CAAC,KAAK,CAAC,CACjB,OACJ,CAEA;AACA,KAAM,CAAEiB,IAAI,CAAEY,gBAAgB,CAAErB,KAAK,CAAEsB,YAAa,CAAC,CAAG,KAAM,CAAAjB,QAAQ,CACjEU,IAAI,CAAC,mBAAmB,CAAC,CACzBC,MAAM,CAAC,SAAS,CAAG;AAAA,CACnBC,EAAE,CAAC,UAAU,CAAEP,IAAI,CAACQ,EAAE,CAAC,CACvBK,KAAK,CAAC,GAAG,CAAC,CAEf,GAAID,YAAY,CAAE,CACdF,OAAO,CAACpB,KAAK,CAAC,mCAAmC,CAAEsB,YAAY,CAAC,CAChErB,QAAQ,CAACX,CAAC,CAAC,8BAA8B,CAAC,CAAC,CAC3CE,UAAU,CAAC,KAAK,CAAC,CACjB,OACJ,CAEA;AACA,KAAM,CAAAgC,OAAO,CAAGH,gBAAgB,CAACI,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACC,OAAO,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAEpE,KAAM,CAAEpB,IAAI,CAAEqB,SAAS,CAAE9B,KAAK,CAAE+B,UAAW,CAAC,CAAG,KAAM,CAAA1B,QAAQ,CACxDU,IAAI,CAAC,OAAO,CAAC,CACbC,MAAM,CAAC,0CAA0C,CAAC,CAClDgB,EAAE,CAAC,IAAI,CAAER,OAAO,CAAC,CAEtB,GAAIO,UAAU,CAAE,CACZX,OAAO,CAACpB,KAAK,CAAC,2BAA2B,CAAE+B,UAAU,CAAC,CACtD9B,QAAQ,CAACX,CAAC,CAAC,8BAA8B,CAAC,CAAC,CAC3CE,UAAU,CAAC,KAAK,CAAC,CACjB,OACJ,CAEA,GAAIgB,cAAc,CAAE,CAChBY,OAAO,CAACpB,KAAK,CAAC,2BAA2B,CAAEQ,cAAc,CAAC,CAC1DP,QAAQ,CAACX,CAAC,CAAC,8BAA8B,CAAC,CAAC,CAC3CE,UAAU,CAAC,KAAK,CAAC,CACjB,OACJ,CAEA;AACA,KAAM,CAAAyC,aAAa,CAAGH,SAAS,EAAI,EAAE,CAErC;AACA,KAAM,CAAAI,cAAc,CAAG,cAAAA,CAAOV,OAAO,CAA8B,IAA5B,CAAAW,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,IAAE,CAAAG,QAAQ,CAAAH,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAC1D,GAAID,KAAK,EAAII,QAAQ,EAAIf,OAAO,CAACa,MAAM,GAAK,CAAC,CAAE,CAC3C,MAAO,EAAE,CACb,CAEA,GAAI,CACA,KAAM,CAAE5B,IAAI,CAAE+B,YAAY,CAAExC,KAAK,CAAEyC,aAAc,CAAC,CAAG,KAAM,CAAApC,QAAQ,CAC9DU,IAAI,CAAC,OAAO,CAAC,CACbC,MAAM,CAAC,0CAA0C,CAAC,CAClDgB,EAAE,CAAC,IAAI,CAAER,OAAO,CAAC,CACjBD,KAAK,CAAC,EAAE,CAAC,CAAE;AAEhB,GAAIkB,aAAa,CAAE,CACfrB,OAAO,CAACpB,KAAK,CAAC,qCAAqCmC,KAAK,GAAG,CAAEM,aAAa,CAAC,CAC3E,MAAO,EAAE,CACb,CAEA,KAAM,CAAAC,SAAS,CAAGF,YAAY,EAAI,EAAE,CAEpC;AACA,KAAM,CAAAG,YAAY,CAAG,CAAC,GAAG,GAAI,CAAA5C,GAAG,CAAC2C,SAAS,CACrCjB,GAAG,CAACmB,CAAC,EAAIA,CAAC,CAACC,WAAW,CAAC,CACvBjB,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC,CAEtB;AACA,KAAM,CAAAiB,kBAAkB,CAAG,KAAM,CAAAZ,cAAc,CAACS,YAAY,CAAER,KAAK,CAAG,CAAC,CAAEI,QAAQ,CAAC,CAElF,MAAO,CAAC,GAAGG,SAAS,CAAE,GAAGI,kBAAkB,CAAC,CAChD,CAAE,MAAO9C,KAAK,CAAE,CACZoB,OAAO,CAACpB,KAAK,CAAC,oCAAoCmC,KAAK,GAAG,CAAEnC,KAAK,CAAC,CAClE,MAAO,EAAE,CACb,CACJ,CAAC,CAED;AACA,KAAM,CAAA+C,kBAAkB,CAAG,CAAC,GAAG,GAAI,CAAAhD,GAAG,CAACkC,aAAa,CAC/CR,GAAG,CAACmB,CAAC,EAAIA,CAAC,CAACC,WAAW,CAAC,CACvBjB,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC,CAEtB,KAAM,CAAAa,SAAS,CAAG,KAAM,CAAAR,cAAc,CAACa,kBAAkB,CAAC,CAE1D;AACA,KAAM,CAAAC,QAAQ,CAAG,CAAC,GAAGf,aAAa,CAAE,GAAGS,SAAS,CAAC,CAEjD;AACA,KAAM,CAAAO,WAAW,CAAGD,QAAQ,CAACpB,MAAM,CAAC,CAAClB,IAAI,CAAEwC,KAAK,CAAEC,IAAI,GAClDD,KAAK,GAAKC,IAAI,CAACC,SAAS,CAACR,CAAC,EAAIA,CAAC,CAAC1B,EAAE,GAAKR,IAAI,CAACQ,EAAE,CAClD,CAAC,CAED;AACA,KAAM,CAAAmC,IAAI,CAAGC,iBAAiB,CAACL,WAAW,CAAC,CAC3CvD,eAAe,CAAC2D,IAAI,CAAC,CACrBzD,eAAe,CAACyD,IAAI,CAAC,CAEzB,CAAE,MAAOE,GAAG,CAAE,CACVnC,OAAO,CAACpB,KAAK,CAAC,QAAQ,CAAEuD,GAAG,CAAC,CAE5B;AACA,GAAI,KAAAC,YAAA,CACApC,OAAO,CAACqC,GAAG,CAAC,8BAA8B,CAAC,CAE3C;AACA,KAAM,CAAAC,WAAW,EAAAF,YAAA,CAAGlD,WAAW,UAAAkD,YAAA,iBAAXA,YAAA,CAAatC,EAAE,CACnC,GAAI,CAACwC,WAAW,CAAE,CACd,KAAM,CAAEjD,IAAI,CAAE,CAAEC,IAAI,CAAEiD,YAAa,CAAE,CAAC,CAAG,KAAM,CAAAtD,QAAQ,CAACM,IAAI,CAACC,OAAO,CAAC,CAAC,CACtE,GAAI,CAAC+C,YAAY,CAAE,CACf1D,QAAQ,CAACX,CAAC,CAAC,oBAAoB,CAAC,CAAC,CACjC,OACJ,CACAgB,WAAW,CAAGqD,YAAY,CAC9B,CAEA,KAAM,CAAElD,IAAI,CAAEmD,eAAe,CAAE5D,KAAK,CAAE6D,WAAY,CAAC,CAAG,KAAM,CAAAxD,QAAQ,CAC/DU,IAAI,CAAC,mBAAmB,CAAC,CACzBC,MAAM,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,CAAC,CACDC,EAAE,CAAC,UAAU,CAAEX,WAAW,CAACY,EAAE,CAAC,CAC9BK,KAAK,CAAC,EAAE,CAAC,CAAE;AAEhB,GAAI,CAACsC,WAAW,EAAID,eAAe,EAAIA,eAAe,CAACvB,MAAM,CAAG,CAAC,CAAE,CAC/D,KAAM,CAAAyB,WAAW,CAAGF,eAAe,CAC9BnC,GAAG,CAACsC,CAAC,EAAIA,CAAC,CAACC,KAAK,CAAC,CACjBpC,MAAM,CAACC,OAAO,CAAC,CACfJ,GAAG,CAACmB,CAAC,GAAK,CACP,GAAGA,CAAC,CACJC,WAAW,CAAE,IAAI,CACjBoB,QAAQ,CAAE,EACd,CAAC,CAAC,CAAC,CAAE;AAET7C,OAAO,CAACqC,GAAG,CAAC,6BAA6B,CAAEK,WAAW,CAACzB,MAAM,CAAE,OAAO,CAAC,CACvE3C,eAAe,CAACoE,WAAW,CAAC,CAC5BlE,eAAe,CAACkE,WAAW,CAAC,CAC5B7D,QAAQ,CAACX,CAAC,CAAC,uBAAuB,CAAC,CAAC,CACxC,CAAC,IAAM,CACH8B,OAAO,CAACqC,GAAG,CAAC,6BAA6B,CAAEI,WAAW,CAAC,CACvDnE,eAAe,CAAC,EAAE,CAAC,CACnBE,eAAe,CAAC,EAAE,CAAC,CACnBK,QAAQ,CAACX,CAAC,CAAC,kBAAkB,CAAC,CAAC,CACnC,CACJ,CAAE,MAAO4E,WAAW,CAAE,CAClB9C,OAAO,CAACpB,KAAK,CAAC,iBAAiB,CAAEkE,WAAW,CAAC,CAC7CxE,eAAe,CAAC,EAAE,CAAC,CACnBE,eAAe,CAAC,EAAE,CAAC,CACnBK,QAAQ,CAACX,CAAC,CAAC,kBAAkB,CAAC,CAAC,CACnC,CACJ,CAAC,OAAS,CACNE,UAAU,CAAC,KAAK,CAAC,CACrB,CACJ,CAAC,CAEDY,iBAAiB,CAAC,CAAC,CACvB,CAAC,CAAE,CAACd,CAAC,CAAC,CAAC,CAEP;AACAvB,SAAS,CAAC,IAAM,CACZ,GAAI,CAACmC,UAAU,CAACiE,IAAI,CAAC,CAAC,CAAE,CACpBvE,eAAe,CAACH,YAAY,CAAC,CAC7B,OACJ,CAEA,KAAM,CAAA2E,UAAU,CAAIC,KAAK,EAAK,CAC1B,MAAO,CAAAA,KAAK,CAACzC,MAAM,CAAC0C,IAAI,EAAI,CACxB,KAAM,CAAAC,aAAa,CAAGD,IAAI,CAACE,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxE,UAAU,CAACuE,WAAW,CAAC,CAAC,CAAC,EAC7DH,IAAI,CAACpD,EAAE,CAACuD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxE,UAAU,CAACuE,WAAW,CAAC,CAAC,CAAC,CAE5E,KAAM,CAAAE,gBAAgB,CAAGL,IAAI,CAACL,QAAQ,CAAGG,UAAU,CAACE,IAAI,CAACL,QAAQ,CAAC,CAAG,EAAE,CAEvE,MAAO,CAAAM,aAAa,EAAII,gBAAgB,CAACtC,MAAM,CAAG,CAAC,CACvD,CAAC,CAAC,CAACZ,GAAG,CAAC6C,IAAI,GAAK,CACZ,GAAGA,IAAI,CACPL,QAAQ,CAAEK,IAAI,CAACL,QAAQ,CAAGG,UAAU,CAACE,IAAI,CAACL,QAAQ,CAAC,CAAG,EAC1D,CAAC,CAAC,CAAC,CACP,CAAC,CAEDrE,eAAe,CAACwE,UAAU,CAAC3E,YAAY,CAAC,CAAC,CAC7C,CAAC,CAAE,CAACS,UAAU,CAAET,YAAY,CAAC,CAAC,CAE9B,KAAM,CAAA6D,iBAAiB,CAAIU,KAAK,EAAK,CACjC,GAAI,CAACA,KAAK,EAAIA,KAAK,CAAC3B,MAAM,GAAK,CAAC,CAAE,CAC9B,MAAO,EAAE,CACb,CAEA,KAAM,CAAAuC,OAAO,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CACzB,KAAM,CAAAC,SAAS,CAAG,EAAE,CAEpB;AACAd,KAAK,CAACe,OAAO,CAACrE,IAAI,EAAI,CAClBkE,OAAO,CAACI,GAAG,CAACtE,IAAI,CAACQ,EAAE,CAAE,CACjB,GAAGR,IAAI,CACPuD,QAAQ,CAAE,EACd,CAAC,CAAC,CACN,CAAC,CAAC,CAEF;AACAD,KAAK,CAACe,OAAO,CAACrE,IAAI,EAAI,CAClB,GAAIA,IAAI,CAACmC,WAAW,CAAE,CAClB,KAAM,CAAAoC,MAAM,CAAGL,OAAO,CAACM,GAAG,CAACxE,IAAI,CAACmC,WAAW,CAAC,CAC5C,GAAIoC,MAAM,CAAE,CACRA,MAAM,CAAChB,QAAQ,CAACkB,IAAI,CAACP,OAAO,CAACM,GAAG,CAACxE,IAAI,CAACQ,EAAE,CAAC,CAAC,CAC9C,CAAC,IAAM,CACH;AACA4D,SAAS,CAACK,IAAI,CAACP,OAAO,CAACM,GAAG,CAACxE,IAAI,CAACQ,EAAE,CAAC,CAAC,CACxC,CACJ,CAAC,IAAM,CACH;AACA4D,SAAS,CAACK,IAAI,CAACP,OAAO,CAACM,GAAG,CAACxE,IAAI,CAACQ,EAAE,CAAC,CAAC,CACxC,CACJ,CAAC,CAAC,CAEF,MAAO,CAAA4D,SAAS,CACpB,CAAC,CAED,KAAM,CAAAM,UAAU,CAAIC,MAAM,EAAK,CAC3B,KAAM,CAAAC,WAAW,CAAG,GAAI,CAAAvF,GAAG,CAACF,aAAa,CAAC,CAC1C,GAAIyF,WAAW,CAACC,GAAG,CAACF,MAAM,CAAC,CAAE,CACzBC,WAAW,CAACE,MAAM,CAACH,MAAM,CAAC,CAC9B,CAAC,IAAM,CACHC,WAAW,CAACG,GAAG,CAACJ,MAAM,CAAC,CAC3B,CACAvF,gBAAgB,CAACwF,WAAW,CAAC,CACjC,CAAC,CAED,KAAM,CAAAI,SAAS,CAAGA,CAAA,GAAM,CACpB,KAAM,CAAAC,UAAU,CAAG,GAAI,CAAA5F,GAAG,CAAC,CAAC,CAC5B,KAAM,CAAA6F,cAAc,CAAIvB,KAAK,EAAK,CAC9BA,KAAK,CAACU,OAAO,CAACT,IAAI,EAAI,CAClB,GAAIA,IAAI,CAACL,QAAQ,EAAIK,IAAI,CAACL,QAAQ,CAAC5B,MAAM,CAAG,CAAC,CAAE,CAC3CsD,UAAU,CAACF,GAAG,CAACnB,IAAI,CAACpD,EAAE,CAAC,CACvB0E,cAAc,CAACtB,IAAI,CAACL,QAAQ,CAAC,CACjC,CACJ,CAAC,CAAC,CACN,CAAC,CACD2B,cAAc,CAACjG,YAAY,CAAC,CAC5BG,gBAAgB,CAAC6F,UAAU,CAAC,CAChC,CAAC,CAED,KAAM,CAAAE,WAAW,CAAGA,CAAA,GAAM,CACtB/F,gBAAgB,CAAC,GAAI,CAAAC,GAAG,CAAC,CAAC,CAAC,CAC/B,CAAC,CAED,KAAM,CAAA+F,YAAY,CAAI5E,EAAE,EAAK,CACzB,MAAO,CAAAA,EAAE,CAAC6E,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAC,WAAW,CAAIC,IAAI,EAAK,CAC1B,OAAQA,IAAI,EACR,IAAK,OAAO,CACR,mBAAO/G,IAAA,CAACP,OAAO,EAACuH,SAAS,CAAC,mBAAmB,CAAE,CAAC,CACpD,IAAK,OAAO,CACR,mBAAOhH,IAAA,CAACR,MAAM,EAACwH,SAAS,CAAC,mBAAmB,CAAE,CAAC,CACnD,IAAK,UAAU,CACX,mBAAOhH,IAAA,CAACR,MAAM,EAACwH,SAAS,CAAC,gBAAgB,CAAE,CAAC,CAChD,QACI,mBAAOhH,IAAA,CAACR,MAAM,EAACwH,SAAS,CAAC,qBAAqB,CAAE,CAAC,CACzD,CACJ,CAAC,CAED,KAAM,CAAAC,cAAc,CAAG,QAAAA,CAAC7B,IAAI,CAAgB,IAAd,CAAAnC,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CACnC,KAAM,CAAAgE,WAAW,CAAG9B,IAAI,CAACL,QAAQ,EAAIK,IAAI,CAACL,QAAQ,CAAC5B,MAAM,CAAG,CAAC,CAC7D,KAAM,CAAAgE,UAAU,CAAGxG,aAAa,CAAC0F,GAAG,CAACjB,IAAI,CAACpD,EAAE,CAAC,CAC7C,KAAM,CAAAoF,WAAW,CAAGnE,KAAK,CAAG,EAAE,CAE9B,mBACI/C,KAAA,QAAmB8G,SAAS,CAAC,MAAM,CAAAjC,QAAA,eAC/B7E,KAAA,QACI8G,SAAS,CAAC,6CAA6C,CACvDK,KAAK,CAAE,CAAED,WAAW,CAAE,GAAGA,WAAW,IAAI,CAAEE,MAAM,CAAEJ,WAAW,CAAG,SAAS,CAAG,SAAU,CAAE,CACxFK,OAAO,CAAEA,CAAA,GAAML,WAAW,EAAIhB,UAAU,CAACd,IAAI,CAACpD,EAAE,CAAE,CAAA+C,QAAA,EAEjDmC,WAAW,CACRC,UAAU,cACNnH,IAAA,CAACV,aAAa,EAAC0H,SAAS,CAAC,iBAAiB,CAAE,CAAC,cAE7ChH,IAAA,CAACT,cAAc,EAACyH,SAAS,CAAC,iBAAiB,CAAE,CAChD,cAEDhH,IAAA,SAAMgH,SAAS,CAAC,MAAM,CAAO,CAChC,CAEAF,WAAW,CAAC1B,IAAI,CAAC2B,IAAI,CAAC,cAEvB/G,IAAA,SAAMgH,SAAS,CAAC,MAAM,CAAAjC,QAAA,CACjBK,IAAI,CAACE,KAAK,CACT,CAAC,cAEPpF,KAAA,SAAM8G,SAAS,CAAC,kBAAkB,CAAAjC,QAAA,EAAC,GAC9B,CAAC6B,YAAY,CAACxB,IAAI,CAACpD,EAAE,CAAC,CAAC,GAC5B,EAAM,CAAC,CAENkF,WAAW,eACRlH,IAAA,SAAMgH,SAAS,CAAC,4BAA4B,CAAAjC,QAAA,CACvCK,IAAI,CAACL,QAAQ,CAAC5B,MAAM,CACnB,CACT,EACA,CAAC,CAEL+D,WAAW,EAAIC,UAAU,eACtBnH,IAAA,QAAA+E,QAAA,CACKK,IAAI,CAACL,QAAQ,CAACxC,GAAG,CAACiF,KAAK,EAAIP,cAAc,CAACO,KAAK,CAAEvE,KAAK,CAAG,CAAC,CAAC,CAAC,CAC5D,CACR,GArCKmC,IAAI,CAACpD,EAsCV,CAAC,CAEd,CAAC,CAED,KAAM,CAAAyF,aAAa,CAAItC,KAAK,EAAK,CAC7B,GAAI,CAAAuC,KAAK,CAAG,CAAC,CACb,KAAM,CAAAC,UAAU,CAAIC,QAAQ,EAAK,CAC7BA,QAAQ,CAAC/B,OAAO,CAACT,IAAI,EAAI,CACrBsC,KAAK,EAAE,CACP,GAAItC,IAAI,CAACL,QAAQ,EAAIK,IAAI,CAACL,QAAQ,CAAC5B,MAAM,CAAG,CAAC,CAAE,CAC3CwE,UAAU,CAACvC,IAAI,CAACL,QAAQ,CAAC,CAC7B,CACJ,CAAC,CAAC,CACN,CAAC,CACD4C,UAAU,CAACxC,KAAK,CAAC,CACjB,MAAO,CAAAuC,KAAK,CAChB,CAAC,CAED,KAAM,CAAAG,iBAAiB,CAAGA,CAAA,GAAM,CAC5B,MAAO,CAAApH,YAAY,CAAC0C,MAAM,CAC9B,CAAC,CAED,GAAI9C,OAAO,CAAE,CACT,mBACIL,IAAA,CAAClB,SAAS,EAACkI,SAAS,CAAC,kDAAkD,CAACK,KAAK,CAAE,CAAES,SAAS,CAAE,OAAQ,CAAE,CAAA/C,QAAA,cAClG7E,KAAA,QAAK8G,SAAS,CAAC,aAAa,CAAAjC,QAAA,eACxB/E,IAAA,CAACd,OAAO,EAAC6I,SAAS,CAAC,QAAQ,CAAChB,IAAI,CAAC,QAAQ,CAACC,SAAS,CAAC,MAAM,CAAE,CAAC,cAC7DhH,IAAA,QAAA+E,QAAA,CAAM3E,CAAC,CAAC,uBAAuB,CAAC,CAAM,CAAC,EACtC,CAAC,CACC,CAAC,CAEpB,CAEA,GAAIU,KAAK,CAAE,CACP,mBACId,IAAA,CAAClB,SAAS,EAAAiG,QAAA,cACN/E,IAAA,CAACb,KAAK,EAAC6I,OAAO,CAAC,QAAQ,CAAAjD,QAAA,CAClBjE,KAAK,CACH,CAAC,CACD,CAAC,CAEpB,CAEA,mBACIZ,KAAA,CAACpB,SAAS,EAAAiG,QAAA,eACN/E,IAAA,CAACjB,GAAG,EAACiI,SAAS,CAAC,MAAM,CAAAjC,QAAA,cACjB7E,KAAA,CAAClB,GAAG,EAAA+F,QAAA,eACA/E,IAAA,OAAA+E,QAAA,CAAK3E,CAAC,CAAC,wBAAwB,CAAC,CAAK,CAAC,cACtCJ,IAAA,MAAGgH,SAAS,CAAC,YAAY,CAAAjC,QAAA,CAAE3E,CAAC,CAAC,2BAA2B,CAAC,CAAI,CAAC,EAC7D,CAAC,CACL,CAAC,cAENJ,IAAA,CAACjB,GAAG,EAACiI,SAAS,CAAC,MAAM,CAAAjC,QAAA,cACjB/E,IAAA,CAAChB,GAAG,EAAA+F,QAAA,cACA/E,IAAA,CAACf,IAAI,EAAA8F,QAAA,cACD/E,IAAA,CAACf,IAAI,CAACgJ,IAAI,EAAAlD,QAAA,cACN7E,KAAA,CAACnB,GAAG,EAACiI,SAAS,CAAC,iBAAiB,CAAAjC,QAAA,eAC5B/E,IAAA,CAAChB,GAAG,EAACkJ,EAAE,CAAE,CAAE,CAAAnD,QAAA,cACP7E,KAAA,CAACb,IAAI,CAAC8I,KAAK,EAAApD,QAAA,eACP/E,IAAA,CAACX,IAAI,CAAC+I,KAAK,EAAArD,QAAA,CAAE3E,CAAC,CAAC,cAAc,CAAC,CAAa,CAAC,cAC5CJ,IAAA,CAACX,IAAI,CAACgJ,OAAO,EACTC,IAAI,CAAC,MAAM,CACXC,WAAW,CAAEnI,CAAC,CAAC,uBAAuB,CAAE,CACxCoI,KAAK,CAAExH,UAAW,CAClByH,QAAQ,CAAGC,CAAC,EAAKzH,aAAa,CAACyH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAClD,CAAC,EACM,CAAC,CACZ,CAAC,cACNxI,IAAA,CAAChB,GAAG,EAACkJ,EAAE,CAAE,CAAE,CAAAnD,QAAA,cACP7E,KAAA,QAAK8G,SAAS,CAAC,cAAc,CAAAjC,QAAA,eACzB7E,KAAA,CAACd,MAAM,EACH4I,OAAO,CAAC,iBAAiB,CACzBY,IAAI,CAAC,IAAI,CACTrB,OAAO,CAAEf,SAAU,CAAAzB,QAAA,eAEnB/E,IAAA,CAACN,iBAAiB,EAACsH,SAAS,CAAC,MAAM,CAAE,CAAC,CACrC5G,CAAC,CAAC,YAAY,CAAC,EACZ,CAAC,cACTF,KAAA,CAACd,MAAM,EACH4I,OAAO,CAAC,mBAAmB,CAC3BY,IAAI,CAAC,IAAI,CACTrB,OAAO,CAAEZ,WAAY,CAAA5B,QAAA,eAErB/E,IAAA,CAACL,mBAAmB,EAACqH,SAAS,CAAC,MAAM,CAAE,CAAC,CACvC5G,CAAC,CAAC,cAAc,CAAC,EACd,CAAC,EACR,CAAC,CACL,CAAC,EACL,CAAC,CACC,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,cAENF,KAAA,CAACnB,GAAG,EAACiI,SAAS,CAAC,MAAM,CAAAjC,QAAA,eACjB/E,IAAA,CAAChB,GAAG,EAACkJ,EAAE,CAAE,CAAE,CAAAnD,QAAA,cACP/E,IAAA,CAACf,IAAI,EAAC+H,SAAS,CAAC,uBAAuB,CAAAjC,QAAA,cACnC7E,KAAA,CAACjB,IAAI,CAACgJ,IAAI,EAAAlD,QAAA,eACN/E,IAAA,OAAA+E,QAAA,CAAK3E,CAAC,CAAC,aAAa,CAAC,CAAK,CAAC,cAC3BJ,IAAA,OAAA+E,QAAA,CAAK0C,aAAa,CAAChH,YAAY,CAAC,CAAK,CAAC,EAC/B,CAAC,CACV,CAAC,CACN,CAAC,cACNT,IAAA,CAAChB,GAAG,EAACkJ,EAAE,CAAE,CAAE,CAAAnD,QAAA,cACP/E,IAAA,CAACf,IAAI,EAAC+H,SAAS,CAAC,uBAAuB,CAAAjC,QAAA,cACnC7E,KAAA,CAACjB,IAAI,CAACgJ,IAAI,EAAAlD,QAAA,eACN/E,IAAA,OAAA+E,QAAA,CAAK3E,CAAC,CAAC,YAAY,CAAC,CAAK,CAAC,cAC1BJ,IAAA,OAAA+E,QAAA,CAAK8C,iBAAiB,CAAC,CAAC,CAAK,CAAC,EACvB,CAAC,CACV,CAAC,CACN,CAAC,cACN7H,IAAA,CAAChB,GAAG,EAACkJ,EAAE,CAAE,CAAE,CAAAnD,QAAA,cACP/E,IAAA,CAACf,IAAI,EAAC+H,SAAS,CAAC,oBAAoB,CAAAjC,QAAA,cAChC7E,KAAA,CAACjB,IAAI,CAACgJ,IAAI,EAAAlD,QAAA,eACN/E,IAAA,OAAA+E,QAAA,CAAK3E,CAAC,CAAC,gBAAgB,CAAC,CAAK,CAAC,cAC9BJ,IAAA,OAAA+E,QAAA,CAAKpE,aAAa,CAACiI,IAAI,CAAK,CAAC,EACtB,CAAC,CACV,CAAC,CACN,CAAC,EACL,CAAC,cAEN5I,IAAA,CAACjB,GAAG,EAAAgG,QAAA,cACA/E,IAAA,CAAChB,GAAG,EAAA+F,QAAA,cACA7E,KAAA,CAACjB,IAAI,EAAA8F,QAAA,eACD7E,KAAA,CAACjB,IAAI,CAAC4J,MAAM,EAAA9D,QAAA,eACR/E,IAAA,OAAIgH,SAAS,CAAC,MAAM,CAAAjC,QAAA,CAAE3E,CAAC,CAAC,eAAe,CAAC,CAAK,CAAC,cAC9CJ,IAAA,UAAOgH,SAAS,CAAC,YAAY,CAAAjC,QAAA,CACxB3E,CAAC,CAAC,0BAA0B,CAAC,CAC3B,CAAC,EACC,CAAC,cACdJ,IAAA,CAACf,IAAI,CAACgJ,IAAI,EAACZ,KAAK,CAAE,CAAEyB,SAAS,CAAE,OAAO,CAAEC,SAAS,CAAE,MAAO,CAAE,CAAAhE,QAAA,CACvDtE,YAAY,CAAC0C,MAAM,GAAK,CAAC,cACtBnD,IAAA,QAAKgH,SAAS,CAAC,6BAA6B,CAAAjC,QAAA,CACvC/D,UAAU,CAAGZ,CAAC,CAAC,mBAAmB,CAAC,CAAGA,CAAC,CAAC,kBAAkB,CAAC,CAC3D,CAAC,cAENJ,IAAA,QAAA+E,QAAA,CACKtE,YAAY,CAAC8B,GAAG,CAAC6C,IAAI,EAAI6B,cAAc,CAAC7B,IAAI,CAAC,CAAC,CAC9C,CACR,CACM,CAAC,EACV,CAAC,CACN,CAAC,CACL,CAAC,EACC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAAjF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}