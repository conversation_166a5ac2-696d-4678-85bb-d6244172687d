{"ast": null, "code": "import React,{useState,useEffect}from'react';import{<PERSON><PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,Spinner,<PERSON><PERSON>,But<PERSON>,Form}from'react-bootstrap';import{FaChevronDown,FaChevronRight,FaUser,FaUsers,FaExpandArrowsAlt,FaCompressArrowsAlt,FaSearch}from'react-icons/fa';import{getSupabase}from'../../supabaseClient';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Recommend=()=>{const{t}=useTranslation();const[loading,setLoading]=useState(true);const[referralTree,setReferralTree]=useState([]);const[filteredTree,setFilteredTree]=useState([]);const[expandedNodes,setExpandedNodes]=useState(new Set());const[error,setError]=useState(null);const[searchTerm,setSearchTerm]=useState('');useEffect(()=>{const fetchReferralData=async()=>{const supabase=getSupabase();if(!supabase)return;setLoading(true);setError(null);try{const{data:{user}}=await supabase.auth.getUser();if(!user){setError(t('user_not_logged_in'));setLoading(false);return;}// First, get the agent's maker_id to determine scope\nconst{data:agentProfile,error:agentError}=await supabase.from('agent_profiles').select('maker_id').eq('user_id',user.id).single();if(agentError){console.error('Error fetching agent profile:',agentError);setError(t('agent_profile_not_found'));setLoading(false);return;}// Fetch all users to build the referral tree\nconst{data:allUsers,error:usersError}=await supabase.from('users').select('id, email, referred_by, created_at, role').order('created_at',{ascending:true});if(usersError){console.error('Error fetching users:',usersError);setError(t('failed_to_load_referral_data'));setLoading(false);return;}// Build the referral tree\nconst tree=buildReferralTree(allUsers);setReferralTree(tree);setFilteredTree(tree);}catch(err){console.error('Error:',err);setError(t('unexpected_error'));}finally{setLoading(false);}};fetchReferralData();},[t]);// Filter tree based on search term\nuseEffect(()=>{if(!searchTerm.trim()){setFilteredTree(referralTree);return;}const filterTree=nodes=>{return nodes.filter(node=>{const matchesSearch=node.email.toLowerCase().includes(searchTerm.toLowerCase())||node.id.toLowerCase().includes(searchTerm.toLowerCase());const filteredChildren=node.children?filterTree(node.children):[];return matchesSearch||filteredChildren.length>0;}).map(node=>({...node,children:node.children?filterTree(node.children):[]}));};setFilteredTree(filterTree(referralTree));},[searchTerm,referralTree]);const buildReferralTree=users=>{const userMap=new Map();const rootUsers=[];// Create a map of all users\nusers.forEach(user=>{userMap.set(user.id,{...user,children:[]});});// Build the tree structure\nusers.forEach(user=>{if(user.referred_by){const parent=userMap.get(user.referred_by);if(parent){parent.children.push(userMap.get(user.id));}else{// Parent not found, treat as root\nrootUsers.push(userMap.get(user.id));}}else{// No referrer, this is a root user\nrootUsers.push(userMap.get(user.id));}});return rootUsers;};const toggleNode=userId=>{const newExpanded=new Set(expandedNodes);if(newExpanded.has(userId)){newExpanded.delete(userId);}else{newExpanded.add(userId);}setExpandedNodes(newExpanded);};const expandAll=()=>{const allNodeIds=new Set();const collectNodeIds=nodes=>{nodes.forEach(node=>{if(node.children&&node.children.length>0){allNodeIds.add(node.id);collectNodeIds(node.children);}});};collectNodeIds(filteredTree);setExpandedNodes(allNodeIds);};const collapseAll=()=>{setExpandedNodes(new Set());};const formatUserId=id=>{return id.substring(0,8);};const getRoleIcon=role=>{switch(role){case'maker':return/*#__PURE__*/_jsx(FaUsers,{className:\"text-primary me-1\"});case'agent':return/*#__PURE__*/_jsx(FaUser,{className:\"text-success me-1\"});case'customer':return/*#__PURE__*/_jsx(FaUser,{className:\"text-info me-1\"});default:return/*#__PURE__*/_jsx(FaUser,{className:\"text-secondary me-1\"});}};const renderTreeNode=function(node){let level=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;const hasChildren=node.children&&node.children.length>0;const isExpanded=expandedNodes.has(node.id);const paddingLeft=level*20;return/*#__PURE__*/_jsxs(\"div\",{className:\"mb-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center p-2 border-bottom\",style:{paddingLeft:`${paddingLeft}px`,cursor:hasChildren?'pointer':'default'},onClick:()=>hasChildren&&toggleNode(node.id),children:[hasChildren?isExpanded?/*#__PURE__*/_jsx(FaChevronDown,{className:\"me-2 text-muted\"}):/*#__PURE__*/_jsx(FaChevronRight,{className:\"me-2 text-muted\"}):/*#__PURE__*/_jsx(\"span\",{className:\"me-4\"}),getRoleIcon(node.role),/*#__PURE__*/_jsx(\"span\",{className:\"me-2\",children:node.email}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-muted small\",children:[\"[\",formatUserId(node.id),\"]\"]}),hasChildren&&/*#__PURE__*/_jsx(\"span\",{className:\"ms-auto badge bg-secondary\",children:node.children.length})]}),hasChildren&&isExpanded&&/*#__PURE__*/_jsx(\"div\",{children:node.children.map(child=>renderTreeNode(child,level+1))})]},node.id);};const getTotalUsers=nodes=>{let total=0;const countNodes=nodeList=>{nodeList.forEach(node=>{total++;if(node.children&&node.children.length>0){countNodes(node.children);}});};countNodes(nodes);return total;};const getRootUsersCount=()=>{return filteredTree.length;};if(loading){return/*#__PURE__*/_jsx(Container,{className:\"d-flex justify-content-center align-items-center\",style:{minHeight:'400px'},children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\",role:\"status\",className:\"mb-3\"}),/*#__PURE__*/_jsx(\"div\",{children:t('loading_referral_data')})]})});}if(error){return/*#__PURE__*/_jsx(Container,{children:/*#__PURE__*/_jsx(Alert,{variant:\"danger\",children:error})});}return/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(Row,{className:\"mb-4\",children:/*#__PURE__*/_jsxs(Col,{children:[/*#__PURE__*/_jsx(\"h2\",{children:t('referral_relationships')}),/*#__PURE__*/_jsx(\"p\",{className:\"text-muted\",children:t('referral_tree_description')})]})}),/*#__PURE__*/_jsx(Row,{className:\"mb-3\",children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Row,{className:\"align-items-end\",children:[/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsxs(Form.Group,{children:[/*#__PURE__*/_jsx(Form.Label,{children:t('search_users')}),/*#__PURE__*/_jsx(Form.Control,{type:\"text\",placeholder:t('search_by_email_or_id'),value:searchTerm,onChange:e=>setSearchTerm(e.target.value)})]})}),/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex gap-2\",children:[/*#__PURE__*/_jsxs(Button,{variant:\"outline-primary\",size:\"sm\",onClick:expandAll,children:[/*#__PURE__*/_jsx(FaExpandArrowsAlt,{className:\"me-1\"}),t('expand_all')]}),/*#__PURE__*/_jsxs(Button,{variant:\"outline-secondary\",size:\"sm\",onClick:collapseAll,children:[/*#__PURE__*/_jsx(FaCompressArrowsAlt,{className:\"me-1\"}),t('collapse_all')]})]})})]})})})})}),/*#__PURE__*/_jsxs(Row,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsx(Card,{className:\"bg-primary text-white\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"h5\",{children:t('total_users')}),/*#__PURE__*/_jsx(\"h3\",{children:getTotalUsers(filteredTree)})]})})}),/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsx(Card,{className:\"bg-success text-white\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"h5\",{children:t('root_users')}),/*#__PURE__*/_jsx(\"h3\",{children:getRootUsersCount()})]})})}),/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsx(Card,{className:\"bg-info text-white\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"h5\",{children:t('expanded_nodes')}),/*#__PURE__*/_jsx(\"h3\",{children:expandedNodes.size})]})})})]}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(Card.Header,{children:[/*#__PURE__*/_jsx(\"h5\",{className:\"mb-0\",children:t('referral_tree')}),/*#__PURE__*/_jsx(\"small\",{className:\"text-muted\",children:t('click_to_expand_collapse')})]}),/*#__PURE__*/_jsx(Card.Body,{style:{maxHeight:'600px',overflowY:'auto'},children:filteredTree.length===0?/*#__PURE__*/_jsx(\"div\",{className:\"text-center text-muted py-4\",children:searchTerm?t('no_search_results'):t('no_referral_data')}):/*#__PURE__*/_jsx(\"div\",{children:filteredTree.map(node=>renderTreeNode(node))})})]})})})]});};export default Recommend;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Spinner", "<PERSON><PERSON>", "<PERSON><PERSON>", "Form", "FaChevronDown", "FaChevronRight", "FaUser", "FaUsers", "FaExpandArrowsAlt", "FaCompressArrowsAlt", "FaSearch", "getSupabase", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "Recommend", "t", "loading", "setLoading", "referralTree", "setReferralTree", "filteredTree", "setFilteredTree", "expandedNodes", "setExpandedNodes", "Set", "error", "setError", "searchTerm", "setSearchTerm", "fetchReferralData", "supabase", "data", "user", "auth", "getUser", "agentProfile", "agent<PERSON><PERSON>r", "from", "select", "eq", "id", "single", "console", "allUsers", "usersError", "order", "ascending", "tree", "buildReferralTree", "err", "trim", "filterTree", "nodes", "filter", "node", "matchesSearch", "email", "toLowerCase", "includes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "length", "map", "users", "userMap", "Map", "rootUsers", "for<PERSON>ach", "set", "referred_by", "parent", "get", "push", "toggleNode", "userId", "newExpanded", "has", "delete", "add", "expandAll", "allNodeIds", "collectNodeIds", "collapseAll", "formatUserId", "substring", "getRoleIcon", "role", "className", "renderTreeNode", "level", "arguments", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isExpanded", "paddingLeft", "style", "cursor", "onClick", "child", "getTotalUsers", "total", "countNodes", "nodeList", "getRootUsersCount", "minHeight", "animation", "variant", "Body", "md", "Group", "Label", "Control", "type", "placeholder", "value", "onChange", "e", "target", "size", "Header", "maxHeight", "overflowY"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/agent/Recommend.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Spinner, <PERSON><PERSON>, But<PERSON>, Form } from 'react-bootstrap';\nimport { FaChevronDown, FaChevronRight, FaUser, FaUsers, FaExpandArrowsAlt, FaCompressArrowsAlt, FaSearch } from 'react-icons/fa';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst Recommend = () => {\n    const { t } = useTranslation();\n    const [loading, setLoading] = useState(true);\n    const [referralTree, setReferralTree] = useState([]);\n    const [filteredTree, setFilteredTree] = useState([]);\n    const [expandedNodes, setExpandedNodes] = useState(new Set());\n    const [error, setError] = useState(null);\n    const [searchTerm, setSearchTerm] = useState('');\n\n    useEffect(() => {\n        const fetchReferralData = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            setError(null);\n\n            try {\n                const { data: { user } } = await supabase.auth.getUser();\n\n                if (!user) {\n                    setError(t('user_not_logged_in'));\n                    setLoading(false);\n                    return;\n                }\n\n                // First, get the agent's maker_id to determine scope\n                const { data: agentProfile, error: agentError } = await supabase\n                    .from('agent_profiles')\n                    .select('maker_id')\n                    .eq('user_id', user.id)\n                    .single();\n\n                if (agentError) {\n                    console.error('Error fetching agent profile:', agentError);\n                    setError(t('agent_profile_not_found'));\n                    setLoading(false);\n                    return;\n                }\n\n                // Fetch all users to build the referral tree\n                const { data: allUsers, error: usersError } = await supabase\n                    .from('users')\n                    .select('id, email, referred_by, created_at, role')\n                    .order('created_at', { ascending: true });\n\n                if (usersError) {\n                    console.error('Error fetching users:', usersError);\n                    setError(t('failed_to_load_referral_data'));\n                    setLoading(false);\n                    return;\n                }\n\n                // Build the referral tree\n                const tree = buildReferralTree(allUsers);\n                setReferralTree(tree);\n                setFilteredTree(tree);\n\n            } catch (err) {\n                console.error('Error:', err);\n                setError(t('unexpected_error'));\n            } finally {\n                setLoading(false);\n            }\n        };\n\n        fetchReferralData();\n    }, [t]);\n\n    // Filter tree based on search term\n    useEffect(() => {\n        if (!searchTerm.trim()) {\n            setFilteredTree(referralTree);\n            return;\n        }\n\n        const filterTree = (nodes) => {\n            return nodes.filter(node => {\n                const matchesSearch = node.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                                    node.id.toLowerCase().includes(searchTerm.toLowerCase());\n\n                const filteredChildren = node.children ? filterTree(node.children) : [];\n\n                return matchesSearch || filteredChildren.length > 0;\n            }).map(node => ({\n                ...node,\n                children: node.children ? filterTree(node.children) : []\n            }));\n        };\n\n        setFilteredTree(filterTree(referralTree));\n    }, [searchTerm, referralTree]);\n\n    const buildReferralTree = (users) => {\n        const userMap = new Map();\n        const rootUsers = [];\n\n        // Create a map of all users\n        users.forEach(user => {\n            userMap.set(user.id, {\n                ...user,\n                children: []\n            });\n        });\n\n        // Build the tree structure\n        users.forEach(user => {\n            if (user.referred_by) {\n                const parent = userMap.get(user.referred_by);\n                if (parent) {\n                    parent.children.push(userMap.get(user.id));\n                } else {\n                    // Parent not found, treat as root\n                    rootUsers.push(userMap.get(user.id));\n                }\n            } else {\n                // No referrer, this is a root user\n                rootUsers.push(userMap.get(user.id));\n            }\n        });\n\n        return rootUsers;\n    };\n\n    const toggleNode = (userId) => {\n        const newExpanded = new Set(expandedNodes);\n        if (newExpanded.has(userId)) {\n            newExpanded.delete(userId);\n        } else {\n            newExpanded.add(userId);\n        }\n        setExpandedNodes(newExpanded);\n    };\n\n    const expandAll = () => {\n        const allNodeIds = new Set();\n        const collectNodeIds = (nodes) => {\n            nodes.forEach(node => {\n                if (node.children && node.children.length > 0) {\n                    allNodeIds.add(node.id);\n                    collectNodeIds(node.children);\n                }\n            });\n        };\n        collectNodeIds(filteredTree);\n        setExpandedNodes(allNodeIds);\n    };\n\n    const collapseAll = () => {\n        setExpandedNodes(new Set());\n    };\n\n    const formatUserId = (id) => {\n        return id.substring(0, 8);\n    };\n\n    const getRoleIcon = (role) => {\n        switch (role) {\n            case 'maker':\n                return <FaUsers className=\"text-primary me-1\" />;\n            case 'agent':\n                return <FaUser className=\"text-success me-1\" />;\n            case 'customer':\n                return <FaUser className=\"text-info me-1\" />;\n            default:\n                return <FaUser className=\"text-secondary me-1\" />;\n        }\n    };\n\n    const renderTreeNode = (node, level = 0) => {\n        const hasChildren = node.children && node.children.length > 0;\n        const isExpanded = expandedNodes.has(node.id);\n        const paddingLeft = level * 20;\n\n        return (\n            <div key={node.id} className=\"mb-1\">\n                <div \n                    className=\"d-flex align-items-center p-2 border-bottom\"\n                    style={{ paddingLeft: `${paddingLeft}px`, cursor: hasChildren ? 'pointer' : 'default' }}\n                    onClick={() => hasChildren && toggleNode(node.id)}\n                >\n                    {hasChildren ? (\n                        isExpanded ? (\n                            <FaChevronDown className=\"me-2 text-muted\" />\n                        ) : (\n                            <FaChevronRight className=\"me-2 text-muted\" />\n                        )\n                    ) : (\n                        <span className=\"me-4\"></span>\n                    )}\n                    \n                    {getRoleIcon(node.role)}\n                    \n                    <span className=\"me-2\">\n                        {node.email}\n                    </span>\n                    \n                    <span className=\"text-muted small\">\n                        [{formatUserId(node.id)}]\n                    </span>\n                    \n                    {hasChildren && (\n                        <span className=\"ms-auto badge bg-secondary\">\n                            {node.children.length}\n                        </span>\n                    )}\n                </div>\n                \n                {hasChildren && isExpanded && (\n                    <div>\n                        {node.children.map(child => renderTreeNode(child, level + 1))}\n                    </div>\n                )}\n            </div>\n        );\n    };\n\n    const getTotalUsers = (nodes) => {\n        let total = 0;\n        const countNodes = (nodeList) => {\n            nodeList.forEach(node => {\n                total++;\n                if (node.children && node.children.length > 0) {\n                    countNodes(node.children);\n                }\n            });\n        };\n        countNodes(nodes);\n        return total;\n    };\n\n    const getRootUsersCount = () => {\n        return filteredTree.length;\n    };\n\n    if (loading) {\n        return (\n            <Container className=\"d-flex justify-content-center align-items-center\" style={{ minHeight: '400px' }}>\n                <div className=\"text-center\">\n                    <Spinner animation=\"border\" role=\"status\" className=\"mb-3\" />\n                    <div>{t('loading_referral_data')}</div>\n                </div>\n            </Container>\n        );\n    }\n\n    if (error) {\n        return (\n            <Container>\n                <Alert variant=\"danger\">\n                    {error}\n                </Alert>\n            </Container>\n        );\n    }\n\n    return (\n        <Container>\n            <Row className=\"mb-4\">\n                <Col>\n                    <h2>{t('referral_relationships')}</h2>\n                    <p className=\"text-muted\">{t('referral_tree_description')}</p>\n                </Col>\n            </Row>\n\n            <Row className=\"mb-3\">\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Row className=\"align-items-end\">\n                                <Col md={4}>\n                                    <Form.Group>\n                                        <Form.Label>{t('search_users')}</Form.Label>\n                                        <Form.Control\n                                            type=\"text\"\n                                            placeholder={t('search_by_email_or_id')}\n                                            value={searchTerm}\n                                            onChange={(e) => setSearchTerm(e.target.value)}\n                                        />\n                                    </Form.Group>\n                                </Col>\n                                <Col md={4}>\n                                    <div className=\"d-flex gap-2\">\n                                        <Button\n                                            variant=\"outline-primary\"\n                                            size=\"sm\"\n                                            onClick={expandAll}\n                                        >\n                                            <FaExpandArrowsAlt className=\"me-1\" />\n                                            {t('expand_all')}\n                                        </Button>\n                                        <Button\n                                            variant=\"outline-secondary\"\n                                            size=\"sm\"\n                                            onClick={collapseAll}\n                                        >\n                                            <FaCompressArrowsAlt className=\"me-1\" />\n                                            {t('collapse_all')}\n                                        </Button>\n                                    </div>\n                                </Col>\n                            </Row>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            <Row className=\"mb-3\">\n                <Col md={4}>\n                    <Card className=\"bg-primary text-white\">\n                        <Card.Body>\n                            <h5>{t('total_users')}</h5>\n                            <h3>{getTotalUsers(filteredTree)}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={4}>\n                    <Card className=\"bg-success text-white\">\n                        <Card.Body>\n                            <h5>{t('root_users')}</h5>\n                            <h3>{getRootUsersCount()}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={4}>\n                    <Card className=\"bg-info text-white\">\n                        <Card.Body>\n                            <h5>{t('expanded_nodes')}</h5>\n                            <h3>{expandedNodes.size}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Header>\n                            <h5 className=\"mb-0\">{t('referral_tree')}</h5>\n                            <small className=\"text-muted\">\n                                {t('click_to_expand_collapse')}\n                            </small>\n                        </Card.Header>\n                        <Card.Body style={{ maxHeight: '600px', overflowY: 'auto' }}>\n                            {filteredTree.length === 0 ? (\n                                <div className=\"text-center text-muted py-4\">\n                                    {searchTerm ? t('no_search_results') : t('no_referral_data')}\n                                </div>\n                            ) : (\n                                <div>\n                                    {filteredTree.map(node => renderTreeNode(node))}\n                                </div>\n                            )}\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default Recommend;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,OAAO,CAAEC,KAAK,CAAEC,MAAM,CAAEC,IAAI,KAAQ,iBAAiB,CACzF,OAASC,aAAa,CAAEC,cAAc,CAAEC,MAAM,CAAEC,OAAO,CAAEC,iBAAiB,CAAEC,mBAAmB,CAAEC,QAAQ,KAAQ,gBAAgB,CACjI,OAASC,WAAW,KAAQ,sBAAsB,CAClD,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,SAAS,CAAGA,CAAA,GAAM,CACpB,KAAM,CAAEC,CAAE,CAAC,CAAGN,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACO,OAAO,CAAEC,UAAU,CAAC,CAAG1B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC2B,YAAY,CAAEC,eAAe,CAAC,CAAG5B,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC6B,YAAY,CAAEC,eAAe,CAAC,CAAG9B,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC+B,aAAa,CAAEC,gBAAgB,CAAC,CAAGhC,QAAQ,CAAC,GAAI,CAAAiC,GAAG,CAAC,CAAC,CAAC,CAC7D,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGnC,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAACoC,UAAU,CAAEC,aAAa,CAAC,CAAGrC,QAAQ,CAAC,EAAE,CAAC,CAEhDC,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAqC,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CAClC,KAAM,CAAAC,QAAQ,CAAGtB,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACsB,QAAQ,CAAE,OAEfb,UAAU,CAAC,IAAI,CAAC,CAChBS,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAI,CACA,KAAM,CAAEK,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CAExD,GAAI,CAACF,IAAI,CAAE,CACPN,QAAQ,CAACX,CAAC,CAAC,oBAAoB,CAAC,CAAC,CACjCE,UAAU,CAAC,KAAK,CAAC,CACjB,OACJ,CAEA;AACA,KAAM,CAAEc,IAAI,CAAEI,YAAY,CAAEV,KAAK,CAAEW,UAAW,CAAC,CAAG,KAAM,CAAAN,QAAQ,CAC3DO,IAAI,CAAC,gBAAgB,CAAC,CACtBC,MAAM,CAAC,UAAU,CAAC,CAClBC,EAAE,CAAC,SAAS,CAAEP,IAAI,CAACQ,EAAE,CAAC,CACtBC,MAAM,CAAC,CAAC,CAEb,GAAIL,UAAU,CAAE,CACZM,OAAO,CAACjB,KAAK,CAAC,+BAA+B,CAAEW,UAAU,CAAC,CAC1DV,QAAQ,CAACX,CAAC,CAAC,yBAAyB,CAAC,CAAC,CACtCE,UAAU,CAAC,KAAK,CAAC,CACjB,OACJ,CAEA;AACA,KAAM,CAAEc,IAAI,CAAEY,QAAQ,CAAElB,KAAK,CAAEmB,UAAW,CAAC,CAAG,KAAM,CAAAd,QAAQ,CACvDO,IAAI,CAAC,OAAO,CAAC,CACbC,MAAM,CAAC,0CAA0C,CAAC,CAClDO,KAAK,CAAC,YAAY,CAAE,CAAEC,SAAS,CAAE,IAAK,CAAC,CAAC,CAE7C,GAAIF,UAAU,CAAE,CACZF,OAAO,CAACjB,KAAK,CAAC,uBAAuB,CAAEmB,UAAU,CAAC,CAClDlB,QAAQ,CAACX,CAAC,CAAC,8BAA8B,CAAC,CAAC,CAC3CE,UAAU,CAAC,KAAK,CAAC,CACjB,OACJ,CAEA;AACA,KAAM,CAAA8B,IAAI,CAAGC,iBAAiB,CAACL,QAAQ,CAAC,CACxCxB,eAAe,CAAC4B,IAAI,CAAC,CACrB1B,eAAe,CAAC0B,IAAI,CAAC,CAEzB,CAAE,MAAOE,GAAG,CAAE,CACVP,OAAO,CAACjB,KAAK,CAAC,QAAQ,CAAEwB,GAAG,CAAC,CAC5BvB,QAAQ,CAACX,CAAC,CAAC,kBAAkB,CAAC,CAAC,CACnC,CAAC,OAAS,CACNE,UAAU,CAAC,KAAK,CAAC,CACrB,CACJ,CAAC,CAEDY,iBAAiB,CAAC,CAAC,CACvB,CAAC,CAAE,CAACd,CAAC,CAAC,CAAC,CAEP;AACAvB,SAAS,CAAC,IAAM,CACZ,GAAI,CAACmC,UAAU,CAACuB,IAAI,CAAC,CAAC,CAAE,CACpB7B,eAAe,CAACH,YAAY,CAAC,CAC7B,OACJ,CAEA,KAAM,CAAAiC,UAAU,CAAIC,KAAK,EAAK,CAC1B,MAAO,CAAAA,KAAK,CAACC,MAAM,CAACC,IAAI,EAAI,CACxB,KAAM,CAAAC,aAAa,CAAGD,IAAI,CAACE,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/B,UAAU,CAAC8B,WAAW,CAAC,CAAC,CAAC,EAC7DH,IAAI,CAACd,EAAE,CAACiB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/B,UAAU,CAAC8B,WAAW,CAAC,CAAC,CAAC,CAE5E,KAAM,CAAAE,gBAAgB,CAAGL,IAAI,CAACM,QAAQ,CAAGT,UAAU,CAACG,IAAI,CAACM,QAAQ,CAAC,CAAG,EAAE,CAEvE,MAAO,CAAAL,aAAa,EAAII,gBAAgB,CAACE,MAAM,CAAG,CAAC,CACvD,CAAC,CAAC,CAACC,GAAG,CAACR,IAAI,GAAK,CACZ,GAAGA,IAAI,CACPM,QAAQ,CAAEN,IAAI,CAACM,QAAQ,CAAGT,UAAU,CAACG,IAAI,CAACM,QAAQ,CAAC,CAAG,EAC1D,CAAC,CAAC,CAAC,CACP,CAAC,CAEDvC,eAAe,CAAC8B,UAAU,CAACjC,YAAY,CAAC,CAAC,CAC7C,CAAC,CAAE,CAACS,UAAU,CAAET,YAAY,CAAC,CAAC,CAE9B,KAAM,CAAA8B,iBAAiB,CAAIe,KAAK,EAAK,CACjC,KAAM,CAAAC,OAAO,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CACzB,KAAM,CAAAC,SAAS,CAAG,EAAE,CAEpB;AACAH,KAAK,CAACI,OAAO,CAACnC,IAAI,EAAI,CAClBgC,OAAO,CAACI,GAAG,CAACpC,IAAI,CAACQ,EAAE,CAAE,CACjB,GAAGR,IAAI,CACP4B,QAAQ,CAAE,EACd,CAAC,CAAC,CACN,CAAC,CAAC,CAEF;AACAG,KAAK,CAACI,OAAO,CAACnC,IAAI,EAAI,CAClB,GAAIA,IAAI,CAACqC,WAAW,CAAE,CAClB,KAAM,CAAAC,MAAM,CAAGN,OAAO,CAACO,GAAG,CAACvC,IAAI,CAACqC,WAAW,CAAC,CAC5C,GAAIC,MAAM,CAAE,CACRA,MAAM,CAACV,QAAQ,CAACY,IAAI,CAACR,OAAO,CAACO,GAAG,CAACvC,IAAI,CAACQ,EAAE,CAAC,CAAC,CAC9C,CAAC,IAAM,CACH;AACA0B,SAAS,CAACM,IAAI,CAACR,OAAO,CAACO,GAAG,CAACvC,IAAI,CAACQ,EAAE,CAAC,CAAC,CACxC,CACJ,CAAC,IAAM,CACH;AACA0B,SAAS,CAACM,IAAI,CAACR,OAAO,CAACO,GAAG,CAACvC,IAAI,CAACQ,EAAE,CAAC,CAAC,CACxC,CACJ,CAAC,CAAC,CAEF,MAAO,CAAA0B,SAAS,CACpB,CAAC,CAED,KAAM,CAAAO,UAAU,CAAIC,MAAM,EAAK,CAC3B,KAAM,CAAAC,WAAW,CAAG,GAAI,CAAAnD,GAAG,CAACF,aAAa,CAAC,CAC1C,GAAIqD,WAAW,CAACC,GAAG,CAACF,MAAM,CAAC,CAAE,CACzBC,WAAW,CAACE,MAAM,CAACH,MAAM,CAAC,CAC9B,CAAC,IAAM,CACHC,WAAW,CAACG,GAAG,CAACJ,MAAM,CAAC,CAC3B,CACAnD,gBAAgB,CAACoD,WAAW,CAAC,CACjC,CAAC,CAED,KAAM,CAAAI,SAAS,CAAGA,CAAA,GAAM,CACpB,KAAM,CAAAC,UAAU,CAAG,GAAI,CAAAxD,GAAG,CAAC,CAAC,CAC5B,KAAM,CAAAyD,cAAc,CAAI7B,KAAK,EAAK,CAC9BA,KAAK,CAACe,OAAO,CAACb,IAAI,EAAI,CAClB,GAAIA,IAAI,CAACM,QAAQ,EAAIN,IAAI,CAACM,QAAQ,CAACC,MAAM,CAAG,CAAC,CAAE,CAC3CmB,UAAU,CAACF,GAAG,CAACxB,IAAI,CAACd,EAAE,CAAC,CACvByC,cAAc,CAAC3B,IAAI,CAACM,QAAQ,CAAC,CACjC,CACJ,CAAC,CAAC,CACN,CAAC,CACDqB,cAAc,CAAC7D,YAAY,CAAC,CAC5BG,gBAAgB,CAACyD,UAAU,CAAC,CAChC,CAAC,CAED,KAAM,CAAAE,WAAW,CAAGA,CAAA,GAAM,CACtB3D,gBAAgB,CAAC,GAAI,CAAAC,GAAG,CAAC,CAAC,CAAC,CAC/B,CAAC,CAED,KAAM,CAAA2D,YAAY,CAAI3C,EAAE,EAAK,CACzB,MAAO,CAAAA,EAAE,CAAC4C,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAC,WAAW,CAAIC,IAAI,EAAK,CAC1B,OAAQA,IAAI,EACR,IAAK,OAAO,CACR,mBAAO3E,IAAA,CAACP,OAAO,EAACmF,SAAS,CAAC,mBAAmB,CAAE,CAAC,CACpD,IAAK,OAAO,CACR,mBAAO5E,IAAA,CAACR,MAAM,EAACoF,SAAS,CAAC,mBAAmB,CAAE,CAAC,CACnD,IAAK,UAAU,CACX,mBAAO5E,IAAA,CAACR,MAAM,EAACoF,SAAS,CAAC,gBAAgB,CAAE,CAAC,CAChD,QACI,mBAAO5E,IAAA,CAACR,MAAM,EAACoF,SAAS,CAAC,qBAAqB,CAAE,CAAC,CACzD,CACJ,CAAC,CAED,KAAM,CAAAC,cAAc,CAAG,QAAAA,CAAClC,IAAI,CAAgB,IAAd,CAAAmC,KAAK,CAAAC,SAAA,CAAA7B,MAAA,IAAA6B,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,CAAC,CACnC,KAAM,CAAAE,WAAW,CAAGtC,IAAI,CAACM,QAAQ,EAAIN,IAAI,CAACM,QAAQ,CAACC,MAAM,CAAG,CAAC,CAC7D,KAAM,CAAAgC,UAAU,CAAGvE,aAAa,CAACsD,GAAG,CAACtB,IAAI,CAACd,EAAE,CAAC,CAC7C,KAAM,CAAAsD,WAAW,CAAGL,KAAK,CAAG,EAAE,CAE9B,mBACI5E,KAAA,QAAmB0E,SAAS,CAAC,MAAM,CAAA3B,QAAA,eAC/B/C,KAAA,QACI0E,SAAS,CAAC,6CAA6C,CACvDQ,KAAK,CAAE,CAAED,WAAW,CAAE,GAAGA,WAAW,IAAI,CAAEE,MAAM,CAAEJ,WAAW,CAAG,SAAS,CAAG,SAAU,CAAE,CACxFK,OAAO,CAAEA,CAAA,GAAML,WAAW,EAAInB,UAAU,CAACnB,IAAI,CAACd,EAAE,CAAE,CAAAoB,QAAA,EAEjDgC,WAAW,CACRC,UAAU,cACNlF,IAAA,CAACV,aAAa,EAACsF,SAAS,CAAC,iBAAiB,CAAE,CAAC,cAE7C5E,IAAA,CAACT,cAAc,EAACqF,SAAS,CAAC,iBAAiB,CAAE,CAChD,cAED5E,IAAA,SAAM4E,SAAS,CAAC,MAAM,CAAO,CAChC,CAEAF,WAAW,CAAC/B,IAAI,CAACgC,IAAI,CAAC,cAEvB3E,IAAA,SAAM4E,SAAS,CAAC,MAAM,CAAA3B,QAAA,CACjBN,IAAI,CAACE,KAAK,CACT,CAAC,cAEP3C,KAAA,SAAM0E,SAAS,CAAC,kBAAkB,CAAA3B,QAAA,EAAC,GAC9B,CAACuB,YAAY,CAAC7B,IAAI,CAACd,EAAE,CAAC,CAAC,GAC5B,EAAM,CAAC,CAENoD,WAAW,eACRjF,IAAA,SAAM4E,SAAS,CAAC,4BAA4B,CAAA3B,QAAA,CACvCN,IAAI,CAACM,QAAQ,CAACC,MAAM,CACnB,CACT,EACA,CAAC,CAEL+B,WAAW,EAAIC,UAAU,eACtBlF,IAAA,QAAAiD,QAAA,CACKN,IAAI,CAACM,QAAQ,CAACE,GAAG,CAACoC,KAAK,EAAIV,cAAc,CAACU,KAAK,CAAET,KAAK,CAAG,CAAC,CAAC,CAAC,CAC5D,CACR,GArCKnC,IAAI,CAACd,EAsCV,CAAC,CAEd,CAAC,CAED,KAAM,CAAA2D,aAAa,CAAI/C,KAAK,EAAK,CAC7B,GAAI,CAAAgD,KAAK,CAAG,CAAC,CACb,KAAM,CAAAC,UAAU,CAAIC,QAAQ,EAAK,CAC7BA,QAAQ,CAACnC,OAAO,CAACb,IAAI,EAAI,CACrB8C,KAAK,EAAE,CACP,GAAI9C,IAAI,CAACM,QAAQ,EAAIN,IAAI,CAACM,QAAQ,CAACC,MAAM,CAAG,CAAC,CAAE,CAC3CwC,UAAU,CAAC/C,IAAI,CAACM,QAAQ,CAAC,CAC7B,CACJ,CAAC,CAAC,CACN,CAAC,CACDyC,UAAU,CAACjD,KAAK,CAAC,CACjB,MAAO,CAAAgD,KAAK,CAChB,CAAC,CAED,KAAM,CAAAG,iBAAiB,CAAGA,CAAA,GAAM,CAC5B,MAAO,CAAAnF,YAAY,CAACyC,MAAM,CAC9B,CAAC,CAED,GAAI7C,OAAO,CAAE,CACT,mBACIL,IAAA,CAAClB,SAAS,EAAC8F,SAAS,CAAC,kDAAkD,CAACQ,KAAK,CAAE,CAAES,SAAS,CAAE,OAAQ,CAAE,CAAA5C,QAAA,cAClG/C,KAAA,QAAK0E,SAAS,CAAC,aAAa,CAAA3B,QAAA,eACxBjD,IAAA,CAACd,OAAO,EAAC4G,SAAS,CAAC,QAAQ,CAACnB,IAAI,CAAC,QAAQ,CAACC,SAAS,CAAC,MAAM,CAAE,CAAC,cAC7D5E,IAAA,QAAAiD,QAAA,CAAM7C,CAAC,CAAC,uBAAuB,CAAC,CAAM,CAAC,EACtC,CAAC,CACC,CAAC,CAEpB,CAEA,GAAIU,KAAK,CAAE,CACP,mBACId,IAAA,CAAClB,SAAS,EAAAmE,QAAA,cACNjD,IAAA,CAACb,KAAK,EAAC4G,OAAO,CAAC,QAAQ,CAAA9C,QAAA,CAClBnC,KAAK,CACH,CAAC,CACD,CAAC,CAEpB,CAEA,mBACIZ,KAAA,CAACpB,SAAS,EAAAmE,QAAA,eACNjD,IAAA,CAACjB,GAAG,EAAC6F,SAAS,CAAC,MAAM,CAAA3B,QAAA,cACjB/C,KAAA,CAAClB,GAAG,EAAAiE,QAAA,eACAjD,IAAA,OAAAiD,QAAA,CAAK7C,CAAC,CAAC,wBAAwB,CAAC,CAAK,CAAC,cACtCJ,IAAA,MAAG4E,SAAS,CAAC,YAAY,CAAA3B,QAAA,CAAE7C,CAAC,CAAC,2BAA2B,CAAC,CAAI,CAAC,EAC7D,CAAC,CACL,CAAC,cAENJ,IAAA,CAACjB,GAAG,EAAC6F,SAAS,CAAC,MAAM,CAAA3B,QAAA,cACjBjD,IAAA,CAAChB,GAAG,EAAAiE,QAAA,cACAjD,IAAA,CAACf,IAAI,EAAAgE,QAAA,cACDjD,IAAA,CAACf,IAAI,CAAC+G,IAAI,EAAA/C,QAAA,cACN/C,KAAA,CAACnB,GAAG,EAAC6F,SAAS,CAAC,iBAAiB,CAAA3B,QAAA,eAC5BjD,IAAA,CAAChB,GAAG,EAACiH,EAAE,CAAE,CAAE,CAAAhD,QAAA,cACP/C,KAAA,CAACb,IAAI,CAAC6G,KAAK,EAAAjD,QAAA,eACPjD,IAAA,CAACX,IAAI,CAAC8G,KAAK,EAAAlD,QAAA,CAAE7C,CAAC,CAAC,cAAc,CAAC,CAAa,CAAC,cAC5CJ,IAAA,CAACX,IAAI,CAAC+G,OAAO,EACTC,IAAI,CAAC,MAAM,CACXC,WAAW,CAAElG,CAAC,CAAC,uBAAuB,CAAE,CACxCmG,KAAK,CAAEvF,UAAW,CAClBwF,QAAQ,CAAGC,CAAC,EAAKxF,aAAa,CAACwF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAClD,CAAC,EACM,CAAC,CACZ,CAAC,cACNvG,IAAA,CAAChB,GAAG,EAACiH,EAAE,CAAE,CAAE,CAAAhD,QAAA,cACP/C,KAAA,QAAK0E,SAAS,CAAC,cAAc,CAAA3B,QAAA,eACzB/C,KAAA,CAACd,MAAM,EACH2G,OAAO,CAAC,iBAAiB,CACzBY,IAAI,CAAC,IAAI,CACTrB,OAAO,CAAElB,SAAU,CAAAnB,QAAA,eAEnBjD,IAAA,CAACN,iBAAiB,EAACkF,SAAS,CAAC,MAAM,CAAE,CAAC,CACrCxE,CAAC,CAAC,YAAY,CAAC,EACZ,CAAC,cACTF,KAAA,CAACd,MAAM,EACH2G,OAAO,CAAC,mBAAmB,CAC3BY,IAAI,CAAC,IAAI,CACTrB,OAAO,CAAEf,WAAY,CAAAtB,QAAA,eAErBjD,IAAA,CAACL,mBAAmB,EAACiF,SAAS,CAAC,MAAM,CAAE,CAAC,CACvCxE,CAAC,CAAC,cAAc,CAAC,EACd,CAAC,EACR,CAAC,CACL,CAAC,EACL,CAAC,CACC,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,cAENF,KAAA,CAACnB,GAAG,EAAC6F,SAAS,CAAC,MAAM,CAAA3B,QAAA,eACjBjD,IAAA,CAAChB,GAAG,EAACiH,EAAE,CAAE,CAAE,CAAAhD,QAAA,cACPjD,IAAA,CAACf,IAAI,EAAC2F,SAAS,CAAC,uBAAuB,CAAA3B,QAAA,cACnC/C,KAAA,CAACjB,IAAI,CAAC+G,IAAI,EAAA/C,QAAA,eACNjD,IAAA,OAAAiD,QAAA,CAAK7C,CAAC,CAAC,aAAa,CAAC,CAAK,CAAC,cAC3BJ,IAAA,OAAAiD,QAAA,CAAKuC,aAAa,CAAC/E,YAAY,CAAC,CAAK,CAAC,EAC/B,CAAC,CACV,CAAC,CACN,CAAC,cACNT,IAAA,CAAChB,GAAG,EAACiH,EAAE,CAAE,CAAE,CAAAhD,QAAA,cACPjD,IAAA,CAACf,IAAI,EAAC2F,SAAS,CAAC,uBAAuB,CAAA3B,QAAA,cACnC/C,KAAA,CAACjB,IAAI,CAAC+G,IAAI,EAAA/C,QAAA,eACNjD,IAAA,OAAAiD,QAAA,CAAK7C,CAAC,CAAC,YAAY,CAAC,CAAK,CAAC,cAC1BJ,IAAA,OAAAiD,QAAA,CAAK2C,iBAAiB,CAAC,CAAC,CAAK,CAAC,EACvB,CAAC,CACV,CAAC,CACN,CAAC,cACN5F,IAAA,CAAChB,GAAG,EAACiH,EAAE,CAAE,CAAE,CAAAhD,QAAA,cACPjD,IAAA,CAACf,IAAI,EAAC2F,SAAS,CAAC,oBAAoB,CAAA3B,QAAA,cAChC/C,KAAA,CAACjB,IAAI,CAAC+G,IAAI,EAAA/C,QAAA,eACNjD,IAAA,OAAAiD,QAAA,CAAK7C,CAAC,CAAC,gBAAgB,CAAC,CAAK,CAAC,cAC9BJ,IAAA,OAAAiD,QAAA,CAAKtC,aAAa,CAACgG,IAAI,CAAK,CAAC,EACtB,CAAC,CACV,CAAC,CACN,CAAC,EACL,CAAC,cAEN3G,IAAA,CAACjB,GAAG,EAAAkE,QAAA,cACAjD,IAAA,CAAChB,GAAG,EAAAiE,QAAA,cACA/C,KAAA,CAACjB,IAAI,EAAAgE,QAAA,eACD/C,KAAA,CAACjB,IAAI,CAAC2H,MAAM,EAAA3D,QAAA,eACRjD,IAAA,OAAI4E,SAAS,CAAC,MAAM,CAAA3B,QAAA,CAAE7C,CAAC,CAAC,eAAe,CAAC,CAAK,CAAC,cAC9CJ,IAAA,UAAO4E,SAAS,CAAC,YAAY,CAAA3B,QAAA,CACxB7C,CAAC,CAAC,0BAA0B,CAAC,CAC3B,CAAC,EACC,CAAC,cACdJ,IAAA,CAACf,IAAI,CAAC+G,IAAI,EAACZ,KAAK,CAAE,CAAEyB,SAAS,CAAE,OAAO,CAAEC,SAAS,CAAE,MAAO,CAAE,CAAA7D,QAAA,CACvDxC,YAAY,CAACyC,MAAM,GAAK,CAAC,cACtBlD,IAAA,QAAK4E,SAAS,CAAC,6BAA6B,CAAA3B,QAAA,CACvCjC,UAAU,CAAGZ,CAAC,CAAC,mBAAmB,CAAC,CAAGA,CAAC,CAAC,kBAAkB,CAAC,CAC3D,CAAC,cAENJ,IAAA,QAAAiD,QAAA,CACKxC,YAAY,CAAC0C,GAAG,CAACR,IAAI,EAAIkC,cAAc,CAAClC,IAAI,CAAC,CAAC,CAC9C,CACR,CACM,CAAC,EACV,CAAC,CACN,CAAC,CACL,CAAC,EACC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAAxC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}