"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[93],{1719:(e,s,a)=>{a.d(s,{A:()=>A});var r=a(8139),t=a.n(r),l=a(5043),i=a(1969),n=a(6618),o=a(7852),d=a(4488),c=a(579);const u=(0,d.A)("h4");u.displayName="DivStyledAsH4";const m=l.forwardRef((e,s)=>{let{className:a,bsPrefix:r,as:l=u,...i}=e;return r=(0,o.oU)(r,"alert-heading"),(0,c.jsx)(l,{ref:s,className:t()(a,r),...i})});m.displayName="AlertHeading";const h=m;var f=a(7071);const g=l.forwardRef((e,s)=>{let{className:a,bsPrefix:r,as:l=f.A,...i}=e;return r=(0,o.oU)(r,"alert-link"),(0,c.jsx)(l,{ref:s,className:t()(a,r),...i})});g.displayName="AlertLink";const x=g;var p=a(8072),v=a(5632);const w=l.forwardRef((e,s)=>{const{bsPrefix:a,show:r=!0,closeLabel:l="Close alert",closeVariant:d,className:u,children:m,variant:h="primary",onClose:f,dismissible:g,transition:x=p.A,...w}=(0,i.Zw)(e,{show:"onClose"}),A=(0,o.oU)(a,"alert"),b=(0,n.A)(e=>{f&&f(!1,e)}),j=!0===x?p.A:x,y=(0,c.jsxs)("div",{role:"alert",...j?void 0:w,ref:s,className:t()(u,A,h&&`${A}-${h}`,g&&`${A}-dismissible`),children:[g&&(0,c.jsx)(v.A,{onClick:b,"aria-label":l,variant:d}),m]});return j?(0,c.jsx)(j,{unmountOnExit:!0,...w,ref:void 0,in:r,children:y}):r?y:null});w.displayName="Alert";const A=Object.assign(w,{Link:x,Heading:h})},9093:(e,s,a)=>{a.r(s),a.d(s,{default:()=>m});var r=a(5043),t=a(8628),l=a(1719),i=a(3722),n=a(4282),o=a(4117),d=a(4312),c=a(1283),u=a(579);const m=()=>{const{t:e}=(0,o.Bd)(),[s,a]=(0,r.useState)(""),[m,h]=(0,r.useState)(""),[f,g]=(0,r.useState)(""),[x,p]=(0,r.useState)(!1),v=(0,c.Zp)();return(0,u.jsx)("div",{className:"d-flex justify-content-center align-items-center",style:{minHeight:"80vh"},children:(0,u.jsx)(t.A,{style:{width:"400px"},children:(0,u.jsxs)(t.A.Body,{children:[(0,u.jsx)("h2",{className:"text-center mb-4",children:e("login")}),f&&(0,u.jsx)(l.A,{variant:"danger",children:f}),(0,u.jsxs)(i.A,{onSubmit:async a=>{a.preventDefault(),g(""),p(!0);try{var r;const e=(0,d.b)(),{error:a}=await e.auth.signInWithPassword({email:s,password:m});if(a)throw a;const{data:{user:t},error:l}=await e.auth.getUser();if(l)throw l;let i=null===t||void 0===t||null===(r=t.user_metadata)||void 0===r?void 0:r.role;if(!i){const{data:s,error:a}=await e.from("users").select("role").eq("id",t.id).single();if(a)throw a;i=s.role}switch(localStorage.setItem("user_role",i),i){case"maker":v("/maker",{replace:!0});break;case"agent":v("/agent",{replace:!0});break;default:v("/",{replace:!0})}}catch(t){console.error("Login Error:",t),g(t.message||e("login_failed"))}p(!1)},children:[(0,u.jsxs)(i.A.Group,{id:"email",className:"mb-3",children:[(0,u.jsx)(i.A.Label,{children:e("email_address")}),(0,u.jsx)(i.A.Control,{type:"email",required:!0,value:s,onChange:e=>a(e.target.value)})]}),(0,u.jsxs)(i.A.Group,{id:"password",className:"mb-3",children:[(0,u.jsx)(i.A.Label,{children:e("password")}),(0,u.jsx)(i.A.Control,{type:"password",required:!0,value:m,onChange:e=>h(e.target.value)})]}),(0,u.jsx)(n.A,{disabled:x,className:"w-100",type:"submit",children:e(x?"logging_in":"login")})]})]})})})}}}]);
//# sourceMappingURL=93.67e065a2.chunk.js.map