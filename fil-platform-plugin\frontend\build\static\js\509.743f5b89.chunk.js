"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[509],{1072:(e,s,r)=>{r.d(s,{A:()=>c});var a=r(8139),t=r.n(a),d=r(5043),n=r(7852),l=r(579);const o=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:d="div",...o}=e;const c=(0,n.oU)(r,"row"),i=(0,n.gy)(),x=(0,n.Jm)(),f=`${c}-cols`,h=[];return i.forEach(e=>{const s=o[e];let r;delete o[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const a=e!==x?`-${e}`:"";null!=r&&h.push(`${f}${a}-${r}`)}),(0,l.jsx)(d,{ref:s,...o,className:t()(a,c,...h)})});o.displayName="Row";const c=o},1509:(e,s,r)=>{r.r(s),r.d(s,{default:()=>h});var a=r(5043),t=r(4063),d=r(3519),n=r(1072),l=r(8602),o=r(8628),c=r(4196),i=r(4312),x=r(4117),f=r(579);const h=()=>{const{t:e}=(0,x.Bd)(),[s,r]=(0,a.useState)([]),[h,m]=(0,a.useState)(!0);(0,a.useEffect)(()=>{(async()=>{const e=(0,i.b)();if(!e)return;m(!0);const{data:{user:s}}=await e.auth.getUser();if(!s)return void m(!1);const{data:a,error:t}=await e.from("orders").select("\n                    id,\n                    cid,\n                    shares,\n                    proof_image_url,\n                    storage_cost,\n                    pledge_cost,\n                    total_rate,\n                    tech_fee_pct,\n                    sales_fee_pct,\n                    ops_fee_pct,\n                    start_at,\n                    end_at,\n                    review_status,\n                    created_at,\n                    updated_at,\n                    products (\n                        name,\n                        category,\n                        price\n                    ),\n                    agent_profiles (\n                        brand_name,\n                        users (\n                            email\n                        )\n                    ),\n                    customer_profiles (\n                        real_name,\n                        users (\n                            email\n                        )\n                    )\n                ").order("created_at",{ascending:!1});t?console.error("Error fetching orders:",t):r(a),m(!1)})()},[]);const u=s=>{switch(s){case"approved":return(0,f.jsx)(t.A,{bg:"success",children:e("approved")});case"pending":return(0,f.jsx)(t.A,{bg:"warning",children:e("pending_review")});case"rejected":return(0,f.jsx)(t.A,{bg:"danger",children:e("rejected")});default:return(0,f.jsx)(t.A,{bg:"secondary",children:s||"-"})}};return h?(0,f.jsx)("div",{children:e("loading_order_reports")}):(0,f.jsxs)(d.A,{children:[(0,f.jsx)("h2",{className:"mb-4",children:e("order_reports")}),(0,f.jsx)(n.A,{children:(0,f.jsx)(l.A,{children:(0,f.jsx)(o.A,{children:(0,f.jsx)(o.A.Body,{children:(0,f.jsxs)(c.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,f.jsx)("thead",{children:(0,f.jsxs)("tr",{children:[(0,f.jsx)("th",{children:e("order_id")}),(0,f.jsx)("th",{children:e("product_name")}),(0,f.jsx)("th",{children:e("customer")}),(0,f.jsx)("th",{children:e("agent")}),(0,f.jsx)("th",{children:e("shares")}),(0,f.jsx)("th",{children:e("storage_cost")}),(0,f.jsx)("th",{children:e("pledge_cost")}),(0,f.jsx)("th",{children:e("total_rate")}),(0,f.jsx)("th",{children:e("start_date")}),(0,f.jsx)("th",{children:e("end_date")}),(0,f.jsx)("th",{children:e("review_status")}),(0,f.jsx)("th",{children:e("created_at")})]})}),(0,f.jsx)("tbody",{children:0===s.length?(0,f.jsx)("tr",{children:(0,f.jsx)("td",{colSpan:"12",className:"text-center",children:e("no_order_reports_available")})}):s.map(e=>{var s,r,a,t,d,n,l,o,c,i,x;return(0,f.jsxs)("tr",{children:[(0,f.jsx)("td",{children:e.cid||e.id}),(0,f.jsx)("td",{children:(null===(s=e.products)||void 0===s?void 0:s.name)||"-"}),(0,f.jsx)("td",{children:(0,f.jsxs)("div",{children:[(0,f.jsx)("div",{children:(null===(r=e.customer_profiles)||void 0===r?void 0:r.real_name)||"-"}),(0,f.jsx)("small",{className:"text-muted",children:(null===(a=e.customer_profiles)||void 0===a||null===(t=a.users)||void 0===t?void 0:t.email)||"-"})]})}),(0,f.jsx)("td",{children:(0,f.jsxs)("div",{children:[(0,f.jsx)("div",{children:(null===(d=e.agent_profiles)||void 0===d?void 0:d.brand_name)||"-"}),(0,f.jsx)("small",{className:"text-muted",children:(null===(n=e.agent_profiles)||void 0===n||null===(l=n.users)||void 0===l?void 0:l.email)||"-"})]})}),(0,f.jsx)("td",{children:(null===(o=e.shares)||void 0===o?void 0:o.toFixed(2))||"0.00"}),(0,f.jsx)("td",{children:(null===(c=e.storage_cost)||void 0===c?void 0:c.toFixed(6))||"0.000000"}),(0,f.jsx)("td",{children:(null===(i=e.pledge_cost)||void 0===i?void 0:i.toFixed(6))||"0.000000"}),(0,f.jsxs)("td",{children:[(null===(x=e.total_rate)||void 0===x?void 0:x.toFixed(4))||"0.0000","%"]}),(0,f.jsx)("td",{children:e.start_at||"-"}),(0,f.jsx)("td",{children:e.end_at||"-"}),(0,f.jsx)("td",{children:u(e.review_status)}),(0,f.jsx)("td",{children:new Date(e.created_at).toLocaleString()})]},e.id)})})]})})})})})]})}},4063:(e,s,r)=>{r.d(s,{A:()=>c});var a=r(8139),t=r.n(a),d=r(5043),n=r(7852),l=r(579);const o=d.forwardRef((e,s)=>{let{bsPrefix:r,bg:a="primary",pill:d=!1,text:o,className:c,as:i="span",...x}=e;const f=(0,n.oU)(r,"badge");return(0,l.jsx)(i,{ref:s,...x,className:t()(c,f,d&&"rounded-pill",o&&`text-${o}`,a&&`bg-${a}`)})});o.displayName="Badge";const c=o},4196:(e,s,r)=>{r.d(s,{A:()=>c});var a=r(8139),t=r.n(a),d=r(5043),n=r(7852),l=r(579);const o=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,striped:d,bordered:o,borderless:c,hover:i,size:x,variant:f,responsive:h,...m}=e;const u=(0,n.oU)(r,"table"),j=t()(a,u,f&&`${u}-${f}`,x&&`${u}-${x}`,d&&`${u}-${"string"===typeof d?`striped-${d}`:"striped"}`,o&&`${u}-bordered`,c&&`${u}-borderless`,i&&`${u}-hover`),p=(0,l.jsx)("table",{...m,className:j,ref:s});if(h){let e=`${u}-responsive`;return"string"===typeof h&&(e=`${e}-${h}`),(0,l.jsx)("div",{className:e,children:p})}return p});o.displayName="Table";const c=o},8602:(e,s,r)=>{r.d(s,{A:()=>c});var a=r(8139),t=r.n(a),d=r(5043),n=r(7852),l=r(579);const o=d.forwardRef((e,s)=>{const[{className:r,...a},{as:d="div",bsPrefix:o,spans:c}]=function(e){let{as:s,bsPrefix:r,className:a,...d}=e;r=(0,n.oU)(r,"col");const l=(0,n.gy)(),o=(0,n.Jm)(),c=[],i=[];return l.forEach(e=>{const s=d[e];let a,t,n;delete d[e],"object"===typeof s&&null!=s?({span:a,offset:t,order:n}=s):a=s;const l=e!==o?`-${e}`:"";a&&c.push(!0===a?`${r}${l}`:`${r}${l}-${a}`),null!=n&&i.push(`order${l}-${n}`),null!=t&&i.push(`offset${l}-${t}`)}),[{...d,className:t()(a,...c,...i)},{as:s,bsPrefix:r,spans:c}]}(e);return(0,l.jsx)(d,{...a,ref:s,className:t()(r,!c.length&&o)})});o.displayName="Col";const c=o},8628:(e,s,r)=>{r.d(s,{A:()=>k});var a=r(8139),t=r.n(a),d=r(5043),n=r(7852),l=r(579);const o=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...o}=e;return a=(0,n.oU)(a,"card-body"),(0,l.jsx)(d,{ref:s,className:t()(r,a),...o})});o.displayName="CardBody";const c=o,i=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...o}=e;return a=(0,n.oU)(a,"card-footer"),(0,l.jsx)(d,{ref:s,className:t()(r,a),...o})});i.displayName="CardFooter";const x=i;var f=r(1778);const h=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:o="div",...c}=e;const i=(0,n.oU)(r,"card-header"),x=(0,d.useMemo)(()=>({cardHeaderBsPrefix:i}),[i]);return(0,l.jsx)(f.A.Provider,{value:x,children:(0,l.jsx)(o,{ref:s,...c,className:t()(a,i)})})});h.displayName="CardHeader";const m=h,u=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,variant:d,as:o="img",...c}=e;const i=(0,n.oU)(r,"card-img");return(0,l.jsx)(o,{ref:s,className:t()(d?`${i}-${d}`:i,a),...c})});u.displayName="CardImg";const j=u,p=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...o}=e;return a=(0,n.oU)(a,"card-img-overlay"),(0,l.jsx)(d,{ref:s,className:t()(r,a),...o})});p.displayName="CardImgOverlay";const v=p,b=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="a",...o}=e;return a=(0,n.oU)(a,"card-link"),(0,l.jsx)(d,{ref:s,className:t()(r,a),...o})});b.displayName="CardLink";const _=b;var N=r(4488);const g=(0,N.A)("h6"),$=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d=g,...o}=e;return a=(0,n.oU)(a,"card-subtitle"),(0,l.jsx)(d,{ref:s,className:t()(r,a),...o})});$.displayName="CardSubtitle";const y=$,w=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="p",...o}=e;return a=(0,n.oU)(a,"card-text"),(0,l.jsx)(d,{ref:s,className:t()(r,a),...o})});w.displayName="CardText";const A=w,P=(0,N.A)("h5"),R=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d=P,...o}=e;return a=(0,n.oU)(a,"card-title"),(0,l.jsx)(d,{ref:s,className:t()(r,a),...o})});R.displayName="CardTitle";const U=R,C=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,bg:d,text:o,border:i,body:x=!1,children:f,as:h="div",...m}=e;const u=(0,n.oU)(r,"card");return(0,l.jsx)(h,{ref:s,...m,className:t()(a,u,d&&`bg-${d}`,o&&`text-${o}`,i&&`border-${i}`),children:x?(0,l.jsx)(c,{children:f}):f})});C.displayName="Card";const k=Object.assign(C,{Img:j,Title:U,Subtitle:y,Body:c,Link:_,Text:A,Header:m,Footer:x,ImgOverlay:v})}}]);
//# sourceMappingURL=509.743f5b89.chunk.js.map