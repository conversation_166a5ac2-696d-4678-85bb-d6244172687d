"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[254],{1072:(e,s,r)=>{r.d(s,{A:()=>o});var a=r(8139),t=r.n(a),d=r(5043),l=r(7852),n=r(579);const c=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:d="div",...c}=e;const o=(0,l.oU)(r,"row"),i=(0,l.gy)(),f=(0,l.Jm)(),h=`${o}-cols`,x=[];return i.forEach(e=>{const s=c[e];let r;delete c[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const a=e!==f?`-${e}`:"";null!=r&&x.push(`${h}${a}-${r}`)}),(0,n.jsx)(d,{ref:s,...c,className:t()(a,o,...x)})});c.displayName="Row";const o=c},4196:(e,s,r)=>{r.d(s,{A:()=>o});var a=r(8139),t=r.n(a),d=r(5043),l=r(7852),n=r(579);const c=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,striped:d,bordered:c,borderless:o,hover:i,size:f,variant:h,responsive:x,...m}=e;const u=(0,l.oU)(r,"table"),j=t()(a,u,h&&`${u}-${h}`,f&&`${u}-${f}`,d&&`${u}-${"string"===typeof d?`striped-${d}`:"striped"}`,c&&`${u}-bordered`,o&&`${u}-borderless`,i&&`${u}-hover`),p=(0,n.jsx)("table",{...m,className:j,ref:s});if(x){let e=`${u}-responsive`;return"string"===typeof x&&(e=`${e}-${x}`),(0,n.jsx)("div",{className:e,children:p})}return p});c.displayName="Table";const o=c},7254:(e,s,r)=>{r.r(s),r.d(s,{default:()=>h});var a=r(5043),t=r(3519),d=r(1072),l=r(8602),n=r(8628),c=r(4196),o=r(4312),i=r(4117),f=r(579);const h=()=>{const{t:e}=(0,i.Bd)(),[s,r]=(0,a.useState)([]),[h,x]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{(async()=>{const e=(0,o.b)();if(!e)return;x(!0);const{data:s,error:a}=await e.from("products").select("\n                    id,\n                    name,\n                    category,\n                    price,\n                    total_shares,\n                    sold_shares,\n                    is_disabled,\n                    agent_profiles ( brand_name )\n                ").eq("is_disabled",!1).order("created_at",{ascending:!1});a?console.error("Error fetching products:",a):r(s),x(!1)})()},[]),h?(0,f.jsx)("div",{children:e("loading_products")}):(0,f.jsxs)(t.A,{children:[(0,f.jsx)("h2",{className:"mb-4",children:e("products_on_sale")}),(0,f.jsx)(d.A,{children:(0,f.jsx)(l.A,{children:(0,f.jsx)(n.A,{children:(0,f.jsx)(n.A.Body,{children:(0,f.jsxs)(c.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,f.jsx)("thead",{children:(0,f.jsxs)("tr",{children:[(0,f.jsx)("th",{children:e("product_id")}),(0,f.jsx)("th",{children:e("product_name")}),(0,f.jsx)("th",{children:e("category")}),(0,f.jsx)("th",{children:e("price")}),(0,f.jsx)("th",{children:e("total_shares")}),(0,f.jsx)("th",{children:e("sold_shares")}),(0,f.jsx)("th",{children:e("remaining_shares")}),(0,f.jsx)("th",{children:e("maker")})]})}),(0,f.jsx)("tbody",{children:0===s.length?(0,f.jsx)("tr",{children:(0,f.jsx)("td",{colSpan:"8",className:"text-center",children:e("no_products")})}):s.map(e=>{var s;return(0,f.jsxs)("tr",{children:[(0,f.jsxs)("td",{children:[e.id.substring(0,8),"..."]}),(0,f.jsx)("td",{children:e.name}),(0,f.jsx)("td",{children:e.category}),(0,f.jsx)("td",{children:e.price}),(0,f.jsx)("td",{children:e.total_shares}),(0,f.jsx)("td",{children:e.sold_shares}),(0,f.jsx)("td",{children:e.total_shares-e.sold_shares}),(0,f.jsx)("td",{children:(null===(s=e.maker_profiles)||void 0===s?void 0:s.domain)||"N/A"})]},e.id)})})]})})})})})]})}},8602:(e,s,r)=>{r.d(s,{A:()=>o});var a=r(8139),t=r.n(a),d=r(5043),l=r(7852),n=r(579);const c=d.forwardRef((e,s)=>{const[{className:r,...a},{as:d="div",bsPrefix:c,spans:o}]=function(e){let{as:s,bsPrefix:r,className:a,...d}=e;r=(0,l.oU)(r,"col");const n=(0,l.gy)(),c=(0,l.Jm)(),o=[],i=[];return n.forEach(e=>{const s=d[e];let a,t,l;delete d[e],"object"===typeof s&&null!=s?({span:a,offset:t,order:l}=s):a=s;const n=e!==c?`-${e}`:"";a&&o.push(!0===a?`${r}${n}`:`${r}${n}-${a}`),null!=l&&i.push(`order${n}-${l}`),null!=t&&i.push(`offset${n}-${t}`)}),[{...d,className:t()(a,...o,...i)},{as:s,bsPrefix:r,spans:o}]}(e);return(0,n.jsx)(d,{...a,ref:s,className:t()(r,!o.length&&c)})});c.displayName="Col";const o=c},8628:(e,s,r)=>{r.d(s,{A:()=>k});var a=r(8139),t=r.n(a),d=r(5043),l=r(7852),n=r(579);const c=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...c}=e;return a=(0,l.oU)(a,"card-body"),(0,n.jsx)(d,{ref:s,className:t()(r,a),...c})});c.displayName="CardBody";const o=c,i=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...c}=e;return a=(0,l.oU)(a,"card-footer"),(0,n.jsx)(d,{ref:s,className:t()(r,a),...c})});i.displayName="CardFooter";const f=i;var h=r(1778);const x=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:c="div",...o}=e;const i=(0,l.oU)(r,"card-header"),f=(0,d.useMemo)(()=>({cardHeaderBsPrefix:i}),[i]);return(0,n.jsx)(h.A.Provider,{value:f,children:(0,n.jsx)(c,{ref:s,...o,className:t()(a,i)})})});x.displayName="CardHeader";const m=x,u=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,variant:d,as:c="img",...o}=e;const i=(0,l.oU)(r,"card-img");return(0,n.jsx)(c,{ref:s,className:t()(d?`${i}-${d}`:i,a),...o})});u.displayName="CardImg";const j=u,p=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...c}=e;return a=(0,l.oU)(a,"card-img-overlay"),(0,n.jsx)(d,{ref:s,className:t()(r,a),...c})});p.displayName="CardImgOverlay";const N=p,b=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="a",...c}=e;return a=(0,l.oU)(a,"card-link"),(0,n.jsx)(d,{ref:s,className:t()(r,a),...c})});b.displayName="CardLink";const $=b;var v=r(4488);const y=(0,v.A)("h6"),g=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d=y,...c}=e;return a=(0,l.oU)(a,"card-subtitle"),(0,n.jsx)(d,{ref:s,className:t()(r,a),...c})});g.displayName="CardSubtitle";const _=g,w=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="p",...c}=e;return a=(0,l.oU)(a,"card-text"),(0,n.jsx)(d,{ref:s,className:t()(r,a),...c})});w.displayName="CardText";const P=w,A=(0,v.A)("h5"),R=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d=A,...c}=e;return a=(0,l.oU)(a,"card-title"),(0,n.jsx)(d,{ref:s,className:t()(r,a),...c})});R.displayName="CardTitle";const C=R,U=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,bg:d,text:c,border:i,body:f=!1,children:h,as:x="div",...m}=e;const u=(0,l.oU)(r,"card");return(0,n.jsx)(x,{ref:s,...m,className:t()(a,u,d&&`bg-${d}`,c&&`text-${c}`,i&&`border-${i}`),children:f?(0,n.jsx)(o,{children:h}):h})});U.displayName="Card";const k=Object.assign(U,{Img:j,Title:C,Subtitle:_,Body:o,Link:$,Text:P,Header:m,Footer:f,ImgOverlay:N})}}]);
//# sourceMappingURL=254.d6827ab8.chunk.js.map