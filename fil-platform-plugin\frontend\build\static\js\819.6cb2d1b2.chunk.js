"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[819],{1072:(e,s,r)=>{r.d(s,{A:()=>c});var a=r(8139),t=r.n(a),n=r(5043),d=r(7852),i=r(579);const l=n.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:n="div",...l}=e;const c=(0,d.oU)(r,"row"),o=(0,d.gy)(),f=(0,d.Jm)(),x=`${c}-cols`,m=[];return o.forEach(e=>{const s=l[e];let r;delete l[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const a=e!==f?`-${e}`:"";null!=r&&m.push(`${x}${a}-${r}`)}),(0,i.jsx)(n,{ref:s,...l,className:t()(a,c,...m)})});l.displayName="Row";const c=l},4063:(e,s,r)=>{r.d(s,{A:()=>c});var a=r(8139),t=r.n(a),n=r(5043),d=r(7852),i=r(579);const l=n.forwardRef((e,s)=>{let{bsPrefix:r,bg:a="primary",pill:n=!1,text:l,className:c,as:o="span",...f}=e;const x=(0,d.oU)(r,"badge");return(0,i.jsx)(o,{ref:s,...f,className:t()(c,x,n&&"rounded-pill",l&&`text-${l}`,a&&`bg-${a}`)})});l.displayName="Badge";const c=l},4196:(e,s,r)=>{r.d(s,{A:()=>c});var a=r(8139),t=r.n(a),n=r(5043),d=r(7852),i=r(579);const l=n.forwardRef((e,s)=>{let{bsPrefix:r,className:a,striped:n,bordered:l,borderless:c,hover:o,size:f,variant:x,responsive:m,...u}=e;const h=(0,d.oU)(r,"table"),j=t()(a,h,x&&`${h}-${x}`,f&&`${h}-${f}`,n&&`${h}-${"string"===typeof n?`striped-${n}`:"striped"}`,l&&`${h}-bordered`,c&&`${h}-borderless`,o&&`${h}-hover`),p=(0,i.jsx)("table",{...u,className:j,ref:s});if(m){let e=`${h}-responsive`;return"string"===typeof m&&(e=`${e}-${m}`),(0,i.jsx)("div",{className:e,children:p})}return p});l.displayName="Table";const c=l},5819:(e,s,r)=>{r.r(s),r.d(s,{default:()=>m});var a=r(5043),t=r(3519),n=r(1072),d=r(8602),i=r(8628),l=r(4196),c=r(4063),o=r(4312),f=r(4117),x=r(579);const m=()=>{const{t:e}=(0,f.Bd)(),[s,r]=(0,a.useState)([]),[m,u]=(0,a.useState)(!0);(0,a.useEffect)(()=>{(async()=>{const e=(0,o.b)();if(!e)return;u(!0);const{data:{user:s}}=await e.auth.getUser();if(!s)return void u(!1);const{data:a,error:t}=await e.from("transactions").select("\n                    id,\n                    tx_date,\n                    sender_user_id,\n                    receiver_user_id,\n                    amount_net,\n                    tx_type,\n                    filecoin_msg_id,\n                    agent_id,\n                    created_at,\n                    sender:users!sender_user_id (\n                        email\n                    ),\n                    receiver:users!receiver_user_id (\n                        email\n                    ),\n                    agent:agent_profiles!agent_id (\n                        user_id,\n                        users (\n                            email\n                        )\n                    )\n                ").order("tx_date",{ascending:!1});t?console.error("Error fetching transactions:",t):r(a),u(!1)})()},[]);const h=e=>{switch(e){case"deposit":return"success";case"withdrawal":return"danger";case"transfer":return"primary";case"reward":return"info";default:return"secondary"}};return m?(0,x.jsx)("div",{children:e("loading_transactions")}):(0,x.jsxs)(t.A,{children:[(0,x.jsx)("h2",{className:"mb-4",children:e("transactions")}),(0,x.jsx)(n.A,{children:(0,x.jsx)(d.A,{children:(0,x.jsx)(i.A,{children:(0,x.jsx)(i.A.Body,{children:(0,x.jsxs)(l.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,x.jsx)("thead",{children:(0,x.jsxs)("tr",{children:[(0,x.jsx)("th",{children:e("transaction_id")}),(0,x.jsx)("th",{children:e("tx_date")}),(0,x.jsx)("th",{children:e("sender")}),(0,x.jsx)("th",{children:e("receiver")}),(0,x.jsx)("th",{children:e("amount")}),(0,x.jsx)("th",{children:e("tx_type")}),(0,x.jsx)("th",{children:e("filecoin_msg_id")}),(0,x.jsx)("th",{children:e("agent")}),(0,x.jsx)("th",{children:e("created_at")})]})}),(0,x.jsx)("tbody",{children:0===s.length?(0,x.jsx)("tr",{children:(0,x.jsx)("td",{colSpan:"9",className:"text-center",children:e("no_transactions_available")})}):s.map(e=>{var s,r,a,t;return(0,x.jsxs)("tr",{children:[(0,x.jsxs)("td",{children:[e.id.substring(0,8),"..."]}),(0,x.jsx)("td",{children:new Date(e.tx_date).toLocaleString()}),(0,x.jsx)("td",{children:(null===(s=e.sender)||void 0===s?void 0:s.email)||"-"}),(0,x.jsx)("td",{children:(null===(r=e.receiver)||void 0===r?void 0:r.email)||"-"}),(0,x.jsxs)("td",{children:[e.amount_net?Number(e.amount_net).toFixed(6):"0"," FIL"]}),(0,x.jsx)("td",{children:(0,x.jsx)(c.A,{bg:h(e.tx_type),children:e.tx_type||"unknown"})}),(0,x.jsx)("td",{children:e.filecoin_msg_id?(0,x.jsxs)("span",{title:e.filecoin_msg_id,children:[e.filecoin_msg_id.substring(0,10),"..."]}):"-"}),(0,x.jsx)("td",{children:(null===(a=e.agent)||void 0===a||null===(t=a.users)||void 0===t?void 0:t.email)||"-"}),(0,x.jsx)("td",{children:new Date(e.created_at).toLocaleString()})]},e.id)})})]})})})})})]})}},8602:(e,s,r)=>{r.d(s,{A:()=>c});var a=r(8139),t=r.n(a),n=r(5043),d=r(7852),i=r(579);const l=n.forwardRef((e,s)=>{const[{className:r,...a},{as:n="div",bsPrefix:l,spans:c}]=function(e){let{as:s,bsPrefix:r,className:a,...n}=e;r=(0,d.oU)(r,"col");const i=(0,d.gy)(),l=(0,d.Jm)(),c=[],o=[];return i.forEach(e=>{const s=n[e];let a,t,d;delete n[e],"object"===typeof s&&null!=s?({span:a,offset:t,order:d}=s):a=s;const i=e!==l?`-${e}`:"";a&&c.push(!0===a?`${r}${i}`:`${r}${i}-${a}`),null!=d&&o.push(`order${i}-${d}`),null!=t&&o.push(`offset${i}-${t}`)}),[{...n,className:t()(a,...c,...o)},{as:s,bsPrefix:r,spans:c}]}(e);return(0,i.jsx)(n,{...a,ref:s,className:t()(r,!c.length&&l)})});l.displayName="Col";const c=l},8628:(e,s,r)=>{r.d(s,{A:()=>k});var a=r(8139),t=r.n(a),n=r(5043),d=r(7852),i=r(579);const l=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n="div",...l}=e;return a=(0,d.oU)(a,"card-body"),(0,i.jsx)(n,{ref:s,className:t()(r,a),...l})});l.displayName="CardBody";const c=l,o=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n="div",...l}=e;return a=(0,d.oU)(a,"card-footer"),(0,i.jsx)(n,{ref:s,className:t()(r,a),...l})});o.displayName="CardFooter";const f=o;var x=r(1778);const m=n.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:l="div",...c}=e;const o=(0,d.oU)(r,"card-header"),f=(0,n.useMemo)(()=>({cardHeaderBsPrefix:o}),[o]);return(0,i.jsx)(x.A.Provider,{value:f,children:(0,i.jsx)(l,{ref:s,...c,className:t()(a,o)})})});m.displayName="CardHeader";const u=m,h=n.forwardRef((e,s)=>{let{bsPrefix:r,className:a,variant:n,as:l="img",...c}=e;const o=(0,d.oU)(r,"card-img");return(0,i.jsx)(l,{ref:s,className:t()(n?`${o}-${n}`:o,a),...c})});h.displayName="CardImg";const j=h,p=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n="div",...l}=e;return a=(0,d.oU)(a,"card-img-overlay"),(0,i.jsx)(n,{ref:s,className:t()(r,a),...l})});p.displayName="CardImgOverlay";const b=p,N=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n="a",...l}=e;return a=(0,d.oU)(a,"card-link"),(0,i.jsx)(n,{ref:s,className:t()(r,a),...l})});N.displayName="CardLink";const v=N;var g=r(4488);const _=(0,g.A)("h6"),y=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n=_,...l}=e;return a=(0,d.oU)(a,"card-subtitle"),(0,i.jsx)(n,{ref:s,className:t()(r,a),...l})});y.displayName="CardSubtitle";const $=y,w=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n="p",...l}=e;return a=(0,d.oU)(a,"card-text"),(0,i.jsx)(n,{ref:s,className:t()(r,a),...l})});w.displayName="CardText";const P=w,A=(0,g.A)("h5"),R=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n=A,...l}=e;return a=(0,d.oU)(a,"card-title"),(0,i.jsx)(n,{ref:s,className:t()(r,a),...l})});R.displayName="CardTitle";const U=R,C=n.forwardRef((e,s)=>{let{bsPrefix:r,className:a,bg:n,text:l,border:o,body:f=!1,children:x,as:m="div",...u}=e;const h=(0,d.oU)(r,"card");return(0,i.jsx)(m,{ref:s,...u,className:t()(a,h,n&&`bg-${n}`,l&&`text-${l}`,o&&`border-${o}`),children:f?(0,i.jsx)(c,{children:x}):x})});C.displayName="Card";const k=Object.assign(C,{Img:j,Title:U,Subtitle:$,Body:c,Link:v,Text:P,Header:u,Footer:f,ImgOverlay:b})}}]);
//# sourceMappingURL=819.6cb2d1b2.chunk.js.map