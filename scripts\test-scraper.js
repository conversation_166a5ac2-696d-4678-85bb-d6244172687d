#!/usr/bin/env node

/**
 * Test script for Filfox scraper
 * 
 * This script tests the scraping functionality without storing data to the database
 */

const { scrapeFilfoxData } = require('./filfox-scraper');

async function testScraper() {
    console.log('=== Testing Filfox Scraper ===');
    
    try {
        const data = await scrapeFilfoxData();
        
        console.log('Scraped Data:');
        console.log('- Block Height:', data.blockHeight);
        console.log('- Average Mining Reward:', data.avgMiningReward);
        console.log('- Page Title:', data.pageTitle);
        console.log('- URL:', data.url);
        
        if (data.blockHeight && data.avgMiningReward) {
            console.log('✅ Test PASSED: Successfully scraped required data');
        } else {
            console.log('❌ Test FAILED: Missing required data');
            console.log('Block Height found:', !!data.blockHeight);
            console.log('Average Mining Reward found:', !!data.avgMiningReward);
        }
        
    } catch (error) {
        console.error('❌ Test FAILED with error:', error.message);
        console.error('Full error:', error);
    }
    
    console.log('=== Test Completed ===');
}

// Run the test
testScraper();
