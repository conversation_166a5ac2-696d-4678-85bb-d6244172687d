{"version": 3, "file": "static/js/509.743f5b89.chunk.js", "mappings": "wKAMA,MAAMA,EAAmBC,EAAAA,WAAiB,CAAAC,EAMvCC,KAAQ,IANgC,SACzCC,EAAQ,UACRC,EAEAC,GAAIC,EAAY,SACbC,GACJN,EACC,MAAMO,GAAoBC,EAAAA,EAAAA,IAAmBN,EAAU,OACjDO,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChBC,EAAa,GAAGN,SAChBO,EAAU,GAehB,OAdAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAIE,SADGZ,EAAMU,GAEI,MAAbC,GAA0C,kBAAdA,IAE5BC,QACED,GAEJC,EAAOD,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GAChD,MAARE,GAAcJ,EAAQM,KAAK,GAAGP,IAAaM,KAASD,QAEtCG,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWI,KAAsBO,OAG3DhB,EAAIyB,YAAc,MAClB,S,sJClCA,MA4JA,EA5JqBC,KACjB,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAAQC,IAAaC,EAAAA,EAAAA,UAAS,KAC9BC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAEvCG,EAAAA,EAAAA,WAAU,KACcC,WAChB,MAAMC,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EAAU,OAEfH,GAAW,GACX,MAAQK,MAAM,KAAEC,UAAiBH,EAASI,KAAKC,UAE/C,IAAKF,EAED,YADAN,GAAW,GAKf,MAAM,KAAEK,EAAI,MAAEI,SAAgBN,EACzBO,KAAK,UACLC,OAAO,2hCAkCPC,MAAM,aAAc,CAAEC,WAAW,IAElCJ,EACAK,QAAQL,MAAM,yBAA0BA,GAExCZ,EAAUQ,GAEdL,GAAW,IAGfe,IACD,IAEH,MAAMC,EAAkBC,IACpB,OAAQA,GACJ,IAAK,WACD,OAAO3B,EAAAA,EAAAA,KAAC4B,EAAAA,EAAK,CAACC,GAAG,UAASC,SAAE1B,EAAE,cAClC,IAAK,UACD,OAAOJ,EAAAA,EAAAA,KAAC4B,EAAAA,EAAK,CAACC,GAAG,UAASC,SAAE1B,EAAE,oBAClC,IAAK,WACD,OAAOJ,EAAAA,EAAAA,KAAC4B,EAAAA,EAAK,CAACC,GAAG,SAAQC,SAAE1B,EAAE,cACjC,QACI,OAAOJ,EAAAA,EAAAA,KAAC4B,EAAAA,EAAK,CAACC,GAAG,YAAWC,SAAEH,GAAU,QAIpD,OAAIlB,GACOT,EAAAA,EAAAA,KAAA,OAAA8B,SAAM1B,EAAE,4BAIf2B,EAAAA,EAAAA,MAACC,EAAAA,EAAS,CAAAF,SAAA,EACN9B,EAAAA,EAAAA,KAAA,MAAIlB,UAAU,OAAMgD,SAAE1B,EAAE,oBACxBJ,EAAAA,EAAAA,KAACvB,EAAAA,EAAG,CAAAqD,UACA9B,EAAAA,EAAAA,KAACiC,EAAAA,EAAG,CAAAH,UACA9B,EAAAA,EAAAA,KAACkC,EAAAA,EAAI,CAAAJ,UACD9B,EAAAA,EAAAA,KAACkC,EAAAA,EAAKC,KAAI,CAAAL,UACNC,EAAAA,EAAAA,MAACK,EAAAA,EAAK,CAACC,SAAO,EAACC,UAAQ,EAACC,OAAK,EAACC,YAAU,EAAAV,SAAA,EACpC9B,EAAAA,EAAAA,KAAA,SAAA8B,UACIC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACI9B,EAAAA,EAAAA,KAAA,MAAA8B,SAAK1B,EAAE,eACPJ,EAAAA,EAAAA,KAAA,MAAA8B,SAAK1B,EAAE,mBACPJ,EAAAA,EAAAA,KAAA,MAAA8B,SAAK1B,EAAE,eACPJ,EAAAA,EAAAA,KAAA,MAAA8B,SAAK1B,EAAE,YACPJ,EAAAA,EAAAA,KAAA,MAAA8B,SAAK1B,EAAE,aACPJ,EAAAA,EAAAA,KAAA,MAAA8B,SAAK1B,EAAE,mBACPJ,EAAAA,EAAAA,KAAA,MAAA8B,SAAK1B,EAAE,kBACPJ,EAAAA,EAAAA,KAAA,MAAA8B,SAAK1B,EAAE,iBACPJ,EAAAA,EAAAA,KAAA,MAAA8B,SAAK1B,EAAE,iBACPJ,EAAAA,EAAAA,KAAA,MAAA8B,SAAK1B,EAAE,eACPJ,EAAAA,EAAAA,KAAA,MAAA8B,SAAK1B,EAAE,oBACPJ,EAAAA,EAAAA,KAAA,MAAA8B,SAAK1B,EAAE,sBAGfJ,EAAAA,EAAAA,KAAA,SAAA8B,SACuB,IAAlBxB,EAAOmC,QACJzC,EAAAA,EAAAA,KAAA,MAAA8B,UACI9B,EAAAA,EAAAA,KAAA,MAAI0C,QAAQ,KAAK5D,UAAU,cAAagD,SAAE1B,EAAE,kCAGhDE,EAAOqC,IAAIrB,IAAK,IAAAsB,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,OACZvB,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACI9B,EAAAA,EAAAA,KAAA,MAAA8B,SAAKR,EAAMiC,KAAOjC,EAAMkC,MACxBxD,EAAAA,EAAAA,KAAA,MAAA8B,UAAmB,QAAdc,EAAAtB,EAAMmC,gBAAQ,IAAAb,OAAA,EAAdA,EAAgBc,OAAQ,OAC7B1D,EAAAA,EAAAA,KAAA,MAAA8B,UACIC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACI9B,EAAAA,EAAAA,KAAA,OAAA8B,UAA6B,QAAvBe,EAAAvB,EAAMqC,yBAAiB,IAAAd,OAAA,EAAvBA,EAAyBe,YAAa,OAC5C5D,EAAAA,EAAAA,KAAA,SAAOlB,UAAU,aAAYgD,UACD,QAAvBgB,EAAAxB,EAAMqC,yBAAiB,IAAAb,GAAO,QAAPC,EAAvBD,EAAyBe,aAAK,IAAAd,OAAP,EAAvBA,EAAgCe,QAAS,YAItD9D,EAAAA,EAAAA,KAAA,MAAA8B,UACIC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACI9B,EAAAA,EAAAA,KAAA,OAAA8B,UAA0B,QAApBkB,EAAA1B,EAAMyC,sBAAc,IAAAf,OAAA,EAApBA,EAAsBgB,aAAc,OAC1ChE,EAAAA,EAAAA,KAAA,SAAOlB,UAAU,aAAYgD,UACJ,QAApBmB,EAAA3B,EAAMyC,sBAAc,IAAAd,GAAO,QAAPC,EAApBD,EAAsBY,aAAK,IAAAX,OAAP,EAApBA,EAA6BY,QAAS,YAInD9D,EAAAA,EAAAA,KAAA,MAAA8B,UAAiB,QAAZqB,EAAA7B,EAAM2C,cAAM,IAAAd,OAAA,EAAZA,EAAce,QAAQ,KAAM,UACjClE,EAAAA,EAAAA,KAAA,MAAA8B,UAAuB,QAAlBsB,EAAA9B,EAAM6C,oBAAY,IAAAf,OAAA,EAAlBA,EAAoBc,QAAQ,KAAM,cACvClE,EAAAA,EAAAA,KAAA,MAAA8B,UAAsB,QAAjBuB,EAAA/B,EAAM8C,mBAAW,IAAAf,OAAA,EAAjBA,EAAmBa,QAAQ,KAAM,cACtCnC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EAAqB,QAAhBwB,EAAAhC,EAAM+C,kBAAU,IAAAf,OAAA,EAAhBA,EAAkBY,QAAQ,KAAM,SAAS,QAC9ClE,EAAAA,EAAAA,KAAA,MAAA8B,SAAKR,EAAMgD,UAAY,OACvBtE,EAAAA,EAAAA,KAAA,MAAA8B,SAAKR,EAAMiD,QAAU,OACrBvE,EAAAA,EAAAA,KAAA,MAAA8B,SAAKJ,EAAeJ,EAAMkD,kBAC1BxE,EAAAA,EAAAA,KAAA,MAAA8B,SAAK,IAAI2C,KAAKnD,EAAMoD,YAAYC,qBA1B3BrD,EAAMkC,sB,sFCnH3D,MAAM5B,EAAqBlD,EAAAA,WAAiB,CAAAC,EAQzCC,KAAQ,IARkC,SAC3CC,EAAQ,GACRgD,EAAK,UAAS,KACd+C,GAAO,EAAK,KACZC,EAAI,UACJ/F,EACAC,GAAIC,EAAY,UACbC,GACJN,EACC,MAAMmG,GAAS3F,EAAAA,EAAAA,IAAmBN,EAAU,SAC5C,OAAoBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWgG,EAAQF,GAAQ,eAAgBC,GAAQ,QAAQA,IAAQhD,GAAM,MAAMA,SAGzGD,EAAM1B,YAAc,QACpB,S,sFCjBA,MAAMkC,EAAqB1D,EAAAA,WAAiB,CAAAC,EAWzCC,KAAQ,IAXkC,SAC3CC,EAAQ,UACRC,EAAS,QACTuD,EAAO,SACPC,EAAQ,WACRyC,EAAU,MACVxC,EAAK,KACLyC,EAAI,QACJC,EAAO,WACPzC,KACGvD,GACJN,EACC,MAAMO,GAAoBC,EAAAA,EAAAA,IAAmBN,EAAU,SACjDY,EAAUQ,IAAWnB,EAAWI,EAAmB+F,GAAW,GAAG/F,KAAqB+F,IAAWD,GAAQ,GAAG9F,KAAqB8F,IAAQ3C,GAAW,GAAGnD,KAAwC,kBAAZmD,EAAuB,WAAWA,IAAY,YAAaC,GAAY,GAAGpD,aAA8B6F,GAAc,GAAG7F,eAAgCqD,GAAS,GAAGrD,WACxVgG,GAAqBlF,EAAAA,EAAAA,KAAK,QAAS,IACpCf,EACHH,UAAWW,EACXb,IAAKA,IAEP,GAAI4D,EAAY,CACd,IAAI2C,EAAkB,GAAGjG,eAIzB,MAH0B,kBAAfsD,IACT2C,EAAkB,GAAGA,KAAmB3C,MAEtBxC,EAAAA,EAAAA,KAAK,MAAO,CAC9BlB,UAAWqG,EACXrD,SAAUoD,GAEd,CACA,OAAOA,IAET9C,EAAMlC,YAAc,QACpB,S,sFCQA,MAAM+B,EAAmBvD,EAAAA,WAEzB,CAACO,EAAOL,KACN,OAAO,UACLE,KACGsG,IAEHrG,GAAIC,EAAY,MAAK,SACrBH,EAAQ,MACRwG,IAjDG,SAAe1G,GAKnB,IALoB,GACrBI,EAAE,SACFF,EAAQ,UACRC,KACGG,GACJN,EACCE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,OACxC,MAAMO,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChB8F,EAAQ,GACR5F,EAAU,GAqBhB,OApBAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAI2F,EACAC,EACAjE,SAHGrC,EAAMU,GAIY,kBAAdC,GAAuC,MAAbA,IAEjC0F,OACAC,SACAjE,SACE1B,GAEJ0F,EAAO1F,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GACxD2F,GAAMD,EAAMtF,MAAc,IAATuF,EAAgB,GAAGzG,IAAWiB,IAAU,GAAGjB,IAAWiB,KAASwF,KACvE,MAAThE,GAAe7B,EAAQM,KAAK,QAAQD,KAASwB,KACnC,MAAViE,GAAgB9F,EAAQM,KAAK,SAASD,KAASyF,OAE9C,CAAC,IACHtG,EACHH,UAAWmB,IAAWnB,KAAcuG,KAAU5F,IAC7C,CACDV,KACAF,WACAwG,SAEJ,CAWOG,CAAOvG,GACZ,OAAoBe,EAAAA,EAAAA,KAAKhB,EAAW,IAC/BoG,EACHxG,IAAKA,EACLE,UAAWmB,IAAWnB,GAAYuG,EAAM5C,QAAU5D,OAGtDoD,EAAI/B,YAAc,MAClB,S,sFC1DA,MAAMuF,EAAwB/G,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPwG,EAASvF,YAAc,WACvB,UCdMwF,EAA0BhH,EAAAA,WAAiB,CAAAC,EAK9CC,KAAQ,IALuC,UAChDE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,gBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPyG,EAAWxF,YAAc,aACzB,U,cCZA,MAAMyF,EAA0BjH,EAAAA,WAAiB,CAAAC,EAM9CC,KAAQ,IANuC,SAChDC,EAAQ,UACRC,EAEAC,GAAIC,EAAY,SACbC,GACJN,EACC,MAAMmG,GAAS3F,EAAAA,EAAAA,IAAmBN,EAAU,eACtC+G,GAAeC,EAAAA,EAAAA,SAAQ,KAAM,CACjCC,mBAAoBhB,IAClB,CAACA,IACL,OAAoB9E,EAAAA,EAAAA,KAAK+F,EAAAA,EAAkBC,SAAU,CACnDC,MAAOL,EACP9D,UAAuB9B,EAAAA,EAAAA,KAAKhB,EAAW,CACrCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWgG,SAIvCa,EAAWzF,YAAc,aACzB,UCvBMgG,EAAuBxH,EAAAA,WAE7B,CAAAC,EAMGC,KAAQ,IANV,SACCC,EAAQ,UACRC,EAAS,QACTmG,EACAlG,GAAIC,EAAY,SACbC,GACJN,EACC,MAAMmG,GAAS3F,EAAAA,EAAAA,IAAmBN,EAAU,YAC5C,OAAoBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWgF,EAAU,GAAGH,KAAUG,IAAYH,EAAQhG,MAC9DG,MAGPiH,EAAQhG,YAAc,UACtB,UCjBMiG,EAA8BzH,EAAAA,WAAiB,CAAAC,EAKlDC,KAAQ,IAL2C,UACpDE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,qBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPkH,EAAejG,YAAc,iBAC7B,UCdMkG,EAAwB1H,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,OACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPmH,EAASlG,YAAc,WACvB,U,cCbA,MAAMmG,GAAgBC,EAAAA,EAAAA,GAAiB,MACjCC,EAA4B7H,EAAAA,WAAiB,CAAAC,EAKhDC,KAAQ,IALyC,UAClDE,EAAS,SACTD,EACAE,GAAIC,EAAYqH,KACbpH,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,kBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPsH,EAAarG,YAAc,eAC3B,UChBMsG,EAAwB9H,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,OACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPuH,EAAStG,YAAc,WACvB,UCbMuG,GAAgBH,EAAAA,EAAAA,GAAiB,MACjCI,EAAyBhI,EAAAA,WAAiB,CAAAC,EAK7CC,KAAQ,IALsC,UAC/CE,EAAS,SACTD,EACAE,GAAIC,EAAYyH,KACbxH,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,eACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPyH,EAAUxG,YAAc,YACxB,UCPMgC,EAAoBxD,EAAAA,WAAiB,CAAAC,EAWxCC,KAAQ,IAXiC,SAC1CC,EAAQ,UACRC,EAAS,GACT+C,EAAE,KACFgD,EAAI,OACJ8B,EAAM,KACNC,GAAO,EAAK,SACZ9E,EAEA/C,GAAIC,EAAY,SACbC,GACJN,EACC,MAAMmG,GAAS3F,EAAAA,EAAAA,IAAmBN,EAAU,QAC5C,OAAoBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWgG,EAAQjD,GAAM,MAAMA,IAAMgD,GAAQ,QAAQA,IAAQ8B,GAAU,UAAUA,KACvG7E,SAAU8E,GAAoB5G,EAAAA,EAAAA,KAAKyF,EAAU,CAC3C3D,SAAUA,IACPA,MAGTI,EAAKhC,YAAc,OACnB,QAAe2G,OAAOC,OAAO5E,EAAM,CACjC6E,IAAKb,EACLc,MAAON,EACPO,SAAUV,EACVpE,KAAMsD,EACNyB,KAAMd,EACNe,KAAMX,EACNY,OAAQzB,EACR0B,OAAQ3B,EACR4B,WAAYnB,G", "sources": ["../node_modules/react-bootstrap/esm/Row.js", "pages/maker/OrderReports.js", "../node_modules/react-bootstrap/esm/Badge.js", "../node_modules/react-bootstrap/esm/Table.js", "../node_modules/react-bootstrap/esm/Col.js", "../node_modules/react-bootstrap/esm/CardBody.js", "../node_modules/react-bootstrap/esm/CardFooter.js", "../node_modules/react-bootstrap/esm/CardHeader.js", "../node_modules/react-bootstrap/esm/CardImg.js", "../node_modules/react-bootstrap/esm/CardImgOverlay.js", "../node_modules/react-bootstrap/esm/CardLink.js", "../node_modules/react-bootstrap/esm/CardSubtitle.js", "../node_modules/react-bootstrap/esm/CardText.js", "../node_modules/react-bootstrap/esm/CardTitle.js", "../node_modules/react-bootstrap/esm/Card.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Row = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'row');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const sizePrefix = `${decoratedBsPrefix}-cols`;\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let cols;\n    if (propValue != null && typeof propValue === 'object') {\n      ({\n        cols\n      } = propValue);\n    } else {\n      cols = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (cols != null) classes.push(`${sizePrefix}${infix}-${cols}`);\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, decoratedBsPrefix, ...classes)\n  });\n});\nRow.displayName = 'Row';\nexport default Row;", "import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst OrderReports = () => {\n    const { t } = useTranslation();\n    const [orders, setOrders] = useState([]);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        const fetchOrders = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setLoading(false);\n                return; // User not logged in\n            }\n\n            // Fetch orders with related information\n            const { data, error } = await supabase\n                .from('orders')\n                .select(`\n                    id,\n                    cid,\n                    shares,\n                    proof_image_url,\n                    storage_cost,\n                    pledge_cost,\n                    total_rate,\n                    tech_fee_pct,\n                    sales_fee_pct,\n                    ops_fee_pct,\n                    start_at,\n                    end_at,\n                    review_status,\n                    created_at,\n                    updated_at,\n                    products (\n                        name,\n                        category,\n                        price\n                    ),\n                    agent_profiles (\n                        brand_name,\n                        users (\n                            email\n                        )\n                    ),\n                    customer_profiles (\n                        real_name,\n                        users (\n                            email\n                        )\n                    )\n                `)\n                .order('created_at', { ascending: false });\n\n            if (error) {\n                console.error('Error fetching orders:', error);\n            } else {\n                setOrders(data);\n            }\n            setLoading(false);\n        };\n\n        fetchOrders();\n    }, []);\n\n    const getStatusBadge = (status) => {\n        switch (status) {\n            case 'approved':\n                return <Badge bg=\"success\">{t('approved')}</Badge>;\n            case 'pending':\n                return <Badge bg=\"warning\">{t('pending_review')}</Badge>;\n            case 'rejected':\n                return <Badge bg=\"danger\">{t('rejected')}</Badge>;\n            default:\n                return <Badge bg=\"secondary\">{status || '-'}</Badge>;\n        }\n    };\n\n    if (loading) {\n        return <div>{t('loading_order_reports')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('order_reports')}</h2>\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('order_id')}</th>\n                                        <th>{t('product_name')}</th>\n                                        <th>{t('customer')}</th>\n                                        <th>{t('agent')}</th>\n                                        <th>{t('shares')}</th>\n                                        <th>{t('storage_cost')}</th>\n                                        <th>{t('pledge_cost')}</th>\n                                        <th>{t('total_rate')}</th>\n                                        <th>{t('start_date')}</th>\n                                        <th>{t('end_date')}</th>\n                                        <th>{t('review_status')}</th>\n                                        <th>{t('created_at')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {orders.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"12\" className=\"text-center\">{t('no_order_reports_available')}</td>\n                                        </tr>\n                                    ) : (\n                                        orders.map(order => (\n                                            <tr key={order.id}>\n                                                <td>{order.cid || order.id}</td>\n                                                <td>{order.products?.name || '-'}</td>\n                                                <td>\n                                                    <div>\n                                                        <div>{order.customer_profiles?.real_name || '-'}</div>\n                                                        <small className=\"text-muted\">\n                                                            {order.customer_profiles?.users?.email || '-'}\n                                                        </small>\n                                                    </div>\n                                                </td>\n                                                <td>\n                                                    <div>\n                                                        <div>{order.agent_profiles?.brand_name || '-'}</div>\n                                                        <small className=\"text-muted\">\n                                                            {order.agent_profiles?.users?.email || '-'}\n                                                        </small>\n                                                    </div>\n                                                </td>\n                                                <td>{order.shares?.toFixed(2) || '0.00'}</td>\n                                                <td>{order.storage_cost?.toFixed(6) || '0.000000'}</td>\n                                                <td>{order.pledge_cost?.toFixed(6) || '0.000000'}</td>\n                                                <td>{order.total_rate?.toFixed(4) || '0.0000'}%</td>\n                                                <td>{order.start_at || '-'}</td>\n                                                <td>{order.end_at || '-'}</td>\n                                                <td>{getStatusBadge(order.review_status)}</td>\n                                                <td>{new Date(order.created_at).toLocaleString()}</td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default OrderReports;\n", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Badge = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  bg = 'primary',\n  pill = false,\n  text,\n  className,\n  as: Component = 'span',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'badge');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, pill && `rounded-pill`, text && `text-${text}`, bg && `bg-${bg}`)\n  });\n});\nBadge.displayName = 'Badge';\nexport default Badge;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Table = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  striped,\n  bordered,\n  borderless,\n  hover,\n  size,\n  variant,\n  responsive,\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'table');\n  const classes = classNames(className, decoratedBsPrefix, variant && `${decoratedBsPrefix}-${variant}`, size && `${decoratedBsPrefix}-${size}`, striped && `${decoratedBsPrefix}-${typeof striped === 'string' ? `striped-${striped}` : 'striped'}`, bordered && `${decoratedBsPrefix}-bordered`, borderless && `${decoratedBsPrefix}-borderless`, hover && `${decoratedBsPrefix}-hover`);\n  const table = /*#__PURE__*/_jsx(\"table\", {\n    ...props,\n    className: classes,\n    ref: ref\n  });\n  if (responsive) {\n    let responsiveClass = `${decoratedBsPrefix}-responsive`;\n    if (typeof responsive === 'string') {\n      responsiveClass = `${responsiveClass}-${responsive}`;\n    }\n    return /*#__PURE__*/_jsx(\"div\", {\n      className: responsiveClass,\n      children: table\n    });\n  }\n  return table;\n});\nTable.displayName = 'Table';\nexport default Table;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function useCol({\n  as,\n  bsPrefix,\n  className,\n  ...props\n}) {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'col');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const spans = [];\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let span;\n    let offset;\n    let order;\n    if (typeof propValue === 'object' && propValue != null) {\n      ({\n        span,\n        offset,\n        order\n      } = propValue);\n    } else {\n      span = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (span) spans.push(span === true ? `${bsPrefix}${infix}` : `${bsPrefix}${infix}-${span}`);\n    if (order != null) classes.push(`order${infix}-${order}`);\n    if (offset != null) classes.push(`offset${infix}-${offset}`);\n  });\n  return [{\n    ...props,\n    className: classNames(className, ...spans, ...classes)\n  }, {\n    as,\n    bsPrefix,\n    spans\n  }];\n}\nconst Col = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n(props, ref) => {\n  const [{\n    className,\n    ...colProps\n  }, {\n    as: Component = 'div',\n    bsPrefix,\n    spans\n  }] = useCol(props);\n  return /*#__PURE__*/_jsx(Component, {\n    ...colProps,\n    ref: ref,\n    className: classNames(className, !spans.length && bsPrefix)\n  });\n});\nCol.displayName = 'Col';\nexport default Col;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});"], "names": ["Row", "React", "_ref", "ref", "bsPrefix", "className", "as", "Component", "props", "decoratedBsPrefix", "useBootstrapPrefix", "breakpoints", "useBootstrapBreakpoints", "minBreakpoint", "useBootstrapMinBreakpoint", "sizePrefix", "classes", "for<PERSON>ach", "brkPoint", "propValue", "cols", "infix", "push", "_jsx", "classNames", "displayName", "OrderReports", "t", "useTranslation", "orders", "setOrders", "useState", "loading", "setLoading", "useEffect", "async", "supabase", "getSupabase", "data", "user", "auth", "getUser", "error", "from", "select", "order", "ascending", "console", "fetchOrders", "getStatusBadge", "status", "Badge", "bg", "children", "_jsxs", "Container", "Col", "Card", "Body", "Table", "striped", "bordered", "hover", "responsive", "length", "colSpan", "map", "_order$products", "_order$customer_profi", "_order$customer_profi2", "_order$customer_profi3", "_order$agent_profiles", "_order$agent_profiles2", "_order$agent_profiles3", "_order$shares", "_order$storage_cost", "_order$pledge_cost", "_order$total_rate", "cid", "id", "products", "name", "customer_profiles", "real_name", "users", "email", "agent_profiles", "brand_name", "shares", "toFixed", "storage_cost", "pledge_cost", "total_rate", "start_at", "end_at", "review_status", "Date", "created_at", "toLocaleString", "pill", "text", "prefix", "borderless", "size", "variant", "table", "responsiveClass", "colProps", "spans", "span", "offset", "useCol", "CardBody", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "value", "CardImg", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "border", "body", "Object", "assign", "Img", "Title", "Subtitle", "Link", "Text", "Header", "Footer", "ImgOverlay"], "sourceRoot": ""}