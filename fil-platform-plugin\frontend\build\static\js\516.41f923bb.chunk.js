"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[516],{1072:(e,r,s)=>{s.d(r,{A:()=>c});var a=s(8139),t=s.n(a),n=s(5043),d=s(7852),l=s(579);const i=n.forwardRef((e,r)=>{let{bsPrefix:s,className:a,as:n="div",...i}=e;const c=(0,d.oU)(s,"row"),o=(0,d.gy)(),f=(0,d.Jm)(),m=`${c}-cols`,x=[];return o.forEach(e=>{const r=i[e];let s;delete i[e],null!=r&&"object"===typeof r?({cols:s}=r):s=r;const a=e!==f?`-${e}`:"";null!=s&&x.push(`${m}${a}-${s}`)}),(0,l.jsx)(n,{ref:r,...i,className:t()(a,c,...x)})});i.displayName="Row";const c=i},4196:(e,r,s)=>{s.d(r,{A:()=>c});var a=s(8139),t=s.n(a),n=s(5043),d=s(7852),l=s(579);const i=n.forwardRef((e,r)=>{let{bsPrefix:s,className:a,striped:n,bordered:i,borderless:c,hover:o,size:f,variant:m,responsive:x,...h}=e;const u=(0,d.oU)(s,"table"),j=t()(a,u,m&&`${u}-${m}`,f&&`${u}-${f}`,n&&`${u}-${"string"===typeof n?`striped-${n}`:"striped"}`,i&&`${u}-bordered`,c&&`${u}-borderless`,o&&`${u}-hover`),b=(0,l.jsx)("table",{...h,className:j,ref:r});if(x){let e=`${u}-responsive`;return"string"===typeof x&&(e=`${e}-${x}`),(0,l.jsx)("div",{className:e,children:b})}return b});i.displayName="Table";const c=i},6516:(e,r,s)=>{s.r(r),s.d(r,{default:()=>m});var a=s(5043),t=s(3519),n=s(1072),d=s(8602),l=s(8628),i=s(4196),c=s(4312),o=s(4117),f=s(579);const m=()=>{const{t:e}=(0,o.Bd)(),[r,s]=(0,a.useState)([]),[m,x]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{(async()=>{const e=(0,c.b)();if(!e)return;x(!0);const{data:{user:r}}=await e.auth.getUser();if(!r)return void x(!1);const{data:a,error:t}=await e.from("miner_daily_earnings").select("\n                    miner_id,\n                    earn_date,\n                    cumulative_reward,\n                    daily_reward,\n                    blocks_won,\n                    created_at,\n                    miners (\n                        filecoin_miner_id,\n                        category,\n                        facilities (\n                            name\n                        )\n                    )\n                ").order("earn_date",{ascending:!1});t?console.error("Error fetching miner earnings:",t):s(a),x(!1)})()},[]),m?(0,f.jsx)("div",{children:e("loading_earnings")}):(0,f.jsxs)(t.A,{children:[(0,f.jsx)("h2",{className:"mb-4",children:e("miner_earnings")}),(0,f.jsx)(n.A,{children:(0,f.jsx)(d.A,{children:(0,f.jsx)(l.A,{children:(0,f.jsx)(l.A.Body,{children:(0,f.jsxs)(i.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,f.jsx)("thead",{children:(0,f.jsxs)("tr",{children:[(0,f.jsx)("th",{children:e("miner_id")}),(0,f.jsx)("th",{children:e("facility")}),(0,f.jsx)("th",{children:e("earn_date")}),(0,f.jsx)("th",{children:e("daily_reward")}),(0,f.jsx)("th",{children:e("cumulative_reward")}),(0,f.jsx)("th",{children:e("blocks_won")}),(0,f.jsx)("th",{children:e("created_at")})]})}),(0,f.jsx)("tbody",{children:0===r.length?(0,f.jsx)("tr",{children:(0,f.jsx)("td",{colSpan:"7",className:"text-center",children:e("no_earnings_available")})}):r.map((e,r)=>{var s,a,t;return(0,f.jsxs)("tr",{children:[(0,f.jsx)("td",{children:(null===(s=e.miners)||void 0===s?void 0:s.filecoin_miner_id)||"-"}),(0,f.jsx)("td",{children:(null===(a=e.miners)||void 0===a||null===(t=a.facilities)||void 0===t?void 0:t.name)||"-"}),(0,f.jsx)("td",{children:new Date(e.earn_date).toLocaleDateString()}),(0,f.jsxs)("td",{children:[e.daily_reward?Number(e.daily_reward).toFixed(6):"0"," FIL"]}),(0,f.jsxs)("td",{children:[e.cumulative_reward?Number(e.cumulative_reward).toFixed(6):"0"," FIL"]}),(0,f.jsx)("td",{children:e.blocks_won||0}),(0,f.jsx)("td",{children:new Date(e.created_at).toLocaleString()})]},`${e.miner_id}-${e.earn_date}`)})})]})})})})})]})}},8602:(e,r,s)=>{s.d(r,{A:()=>c});var a=s(8139),t=s.n(a),n=s(5043),d=s(7852),l=s(579);const i=n.forwardRef((e,r)=>{const[{className:s,...a},{as:n="div",bsPrefix:i,spans:c}]=function(e){let{as:r,bsPrefix:s,className:a,...n}=e;s=(0,d.oU)(s,"col");const l=(0,d.gy)(),i=(0,d.Jm)(),c=[],o=[];return l.forEach(e=>{const r=n[e];let a,t,d;delete n[e],"object"===typeof r&&null!=r?({span:a,offset:t,order:d}=r):a=r;const l=e!==i?`-${e}`:"";a&&c.push(!0===a?`${s}${l}`:`${s}${l}-${a}`),null!=d&&o.push(`order${l}-${d}`),null!=t&&o.push(`offset${l}-${t}`)}),[{...n,className:t()(a,...c,...o)},{as:r,bsPrefix:s,spans:c}]}(e);return(0,l.jsx)(n,{...a,ref:r,className:t()(s,!c.length&&i)})});i.displayName="Col";const c=i},8628:(e,r,s)=>{s.d(r,{A:()=>k});var a=s(8139),t=s.n(a),n=s(5043),d=s(7852),l=s(579);const i=n.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:n="div",...i}=e;return a=(0,d.oU)(a,"card-body"),(0,l.jsx)(n,{ref:r,className:t()(s,a),...i})});i.displayName="CardBody";const c=i,o=n.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:n="div",...i}=e;return a=(0,d.oU)(a,"card-footer"),(0,l.jsx)(n,{ref:r,className:t()(s,a),...i})});o.displayName="CardFooter";const f=o;var m=s(1778);const x=n.forwardRef((e,r)=>{let{bsPrefix:s,className:a,as:i="div",...c}=e;const o=(0,d.oU)(s,"card-header"),f=(0,n.useMemo)(()=>({cardHeaderBsPrefix:o}),[o]);return(0,l.jsx)(m.A.Provider,{value:f,children:(0,l.jsx)(i,{ref:r,...c,className:t()(a,o)})})});x.displayName="CardHeader";const h=x,u=n.forwardRef((e,r)=>{let{bsPrefix:s,className:a,variant:n,as:i="img",...c}=e;const o=(0,d.oU)(s,"card-img");return(0,l.jsx)(i,{ref:r,className:t()(n?`${o}-${n}`:o,a),...c})});u.displayName="CardImg";const j=u,b=n.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:n="div",...i}=e;return a=(0,d.oU)(a,"card-img-overlay"),(0,l.jsx)(n,{ref:r,className:t()(s,a),...i})});b.displayName="CardImgOverlay";const N=b,v=n.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:n="a",...i}=e;return a=(0,d.oU)(a,"card-link"),(0,l.jsx)(n,{ref:r,className:t()(s,a),...i})});v.displayName="CardLink";const p=v;var y=s(4488);const $=(0,y.A)("h6"),w=n.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:n=$,...i}=e;return a=(0,d.oU)(a,"card-subtitle"),(0,l.jsx)(n,{ref:r,className:t()(s,a),...i})});w.displayName="CardSubtitle";const _=w,g=n.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:n="p",...i}=e;return a=(0,d.oU)(a,"card-text"),(0,l.jsx)(n,{ref:r,className:t()(s,a),...i})});g.displayName="CardText";const P=g,R=(0,y.A)("h5"),U=n.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:n=R,...i}=e;return a=(0,d.oU)(a,"card-title"),(0,l.jsx)(n,{ref:r,className:t()(s,a),...i})});U.displayName="CardTitle";const A=U,C=n.forwardRef((e,r)=>{let{bsPrefix:s,className:a,bg:n,text:i,border:o,body:f=!1,children:m,as:x="div",...h}=e;const u=(0,d.oU)(s,"card");return(0,l.jsx)(x,{ref:r,...h,className:t()(a,u,n&&`bg-${n}`,i&&`text-${i}`,o&&`border-${o}`),children:f?(0,l.jsx)(c,{children:m}):m})});C.displayName="Card";const k=Object.assign(C,{Img:j,Title:A,Subtitle:_,Body:c,Link:p,Text:P,Header:h,Footer:f,ImgOverlay:N})}}]);
//# sourceMappingURL=516.41f923bb.chunk.js.map