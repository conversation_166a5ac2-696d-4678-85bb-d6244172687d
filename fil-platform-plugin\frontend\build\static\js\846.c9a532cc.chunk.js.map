{"version": 3, "file": "static/js/846.c9a532cc.chunk.js", "mappings": "wKAMA,MAAMA,EAAmBC,EAAAA,WAAiB,CAAAC,EAMvCC,KAAQ,IANgC,SACzCC,EAAQ,UACRC,EAEAC,GAAIC,EAAY,SACbC,GACJN,EACC,MAAMO,GAAoBC,EAAAA,EAAAA,IAAmBN,EAAU,OACjDO,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChBC,EAAa,GAAGN,SAChBO,EAAU,GAehB,OAdAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAIE,SADGZ,EAAMU,GAEI,MAAbC,GAA0C,kBAAdA,IAE5BC,QACED,GAEJC,EAAOD,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GAChD,MAARE,GAAcJ,EAAQM,KAAK,GAAGP,IAAaM,KAASD,QAEtCG,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWI,KAAsBO,OAG3DhB,EAAIyB,YAAc,MAClB,S,sFCjCA,MAAMC,EAAqBzB,EAAAA,WAAiB,CAAAC,EAQzCC,KAAQ,IARkC,SAC3CC,EAAQ,GACRuB,EAAK,UAAS,KACdC,GAAO,EAAK,KACZC,EAAI,UACJxB,EACAC,GAAIC,EAAY,UACbC,GACJN,EACC,MAAM4B,GAASpB,EAAAA,EAAAA,IAAmBN,EAAU,SAC5C,OAAoBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWyB,EAAQF,GAAQ,eAAgBC,GAAQ,QAAQA,IAAQF,GAAM,MAAMA,SAGzGD,EAAMD,YAAc,QACpB,S,sFCjBA,MAAMM,EAAqB9B,EAAAA,WAAiB,CAAAC,EAWzCC,KAAQ,IAXkC,SAC3CC,EAAQ,UACRC,EAAS,QACT2B,EAAO,SACPC,EAAQ,WACRC,EAAU,MACVC,EAAK,KACLC,EAAI,QACJC,EAAO,WACPC,KACG9B,GACJN,EACC,MAAMO,GAAoBC,EAAAA,EAAAA,IAAmBN,EAAU,SACjDY,EAAUQ,IAAWnB,EAAWI,EAAmB4B,GAAW,GAAG5B,KAAqB4B,IAAWD,GAAQ,GAAG3B,KAAqB2B,IAAQJ,GAAW,GAAGvB,KAAwC,kBAAZuB,EAAuB,WAAWA,IAAY,YAAaC,GAAY,GAAGxB,aAA8ByB,GAAc,GAAGzB,eAAgC0B,GAAS,GAAG1B,WACxV8B,GAAqBhB,EAAAA,EAAAA,KAAK,QAAS,IACpCf,EACHH,UAAWW,EACXb,IAAKA,IAEP,GAAImC,EAAY,CACd,IAAIE,EAAkB,GAAG/B,eAIzB,MAH0B,kBAAf6B,IACTE,EAAkB,GAAGA,KAAmBF,MAEtBf,EAAAA,EAAAA,KAAK,MAAO,CAC9BlB,UAAWmC,EACXC,SAAUF,GAEd,CACA,OAAOA,IAETR,EAAMN,YAAc,QACpB,S,sJCjCA,MAkKA,EAlKqBiB,KACjB,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAAQC,IAAaC,EAAAA,EAAAA,UAAS,KAC9BC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAEvCG,EAAAA,EAAAA,WAAU,KACcC,WAChB,MAAMC,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EAAU,OAEfH,GAAW,GACX,MAAQK,MAAM,KAAEC,UAAiBH,EAASI,KAAKC,UAE/C,IAAKF,EAED,YADAN,GAAW,GAKf,MAAQK,KAAMI,EAAcC,MAAOC,SAAqBR,EACnDS,KAAK,kBACLC,OAAO,WACPC,GAAG,UAAWR,EAAKS,IACnBC,SAEL,GAAIL,IAAeF,EAGf,OAFAQ,QAAQP,MAAM,gCAAiCC,QAC/CX,GAAW,GAKf,MAAM,KAAEK,EAAI,MAAEK,SAAgBP,EACzBS,KAAK,UACLC,OAAO,y1BA4BPC,GAAG,WAAYL,EAAaS,SAC5BC,MAAM,aAAc,CAAEC,WAAW,IAElCV,EACAO,QAAQP,MAAM,yBAA0BA,GAExCb,EAAUQ,GAEdL,GAAW,IAGfqB,IACD,IAEH,MAAMC,EAAkBC,IACpB,OAAQA,GACJ,IAAK,WACD,OAAOjD,EAAAA,EAAAA,KAACG,EAAAA,EAAK,CAACC,GAAG,UAASc,SAAEE,EAAE,cAClC,IAAK,UACD,OAAOpB,EAAAA,EAAAA,KAACG,EAAAA,EAAK,CAACC,GAAG,UAASc,SAAEE,EAAE,oBAClC,IAAK,WACD,OAAOpB,EAAAA,EAAAA,KAACG,EAAAA,EAAK,CAACC,GAAG,SAAQc,SAAEE,EAAE,cACjC,QACI,OAAOpB,EAAAA,EAAAA,KAACG,EAAAA,EAAK,CAACC,GAAG,YAAWc,SAAE+B,GAAU,QAIpD,OAAIxB,GACOzB,EAAAA,EAAAA,KAAA,OAAAkB,SAAME,EAAE,4BAIf8B,EAAAA,EAAAA,MAACC,EAAAA,EAAS,CAAAjC,SAAA,EACNlB,EAAAA,EAAAA,KAAA,MAAIlB,UAAU,OAAMoC,SAAEE,EAAE,iBACxBpB,EAAAA,EAAAA,KAACvB,EAAAA,EAAG,CAAAyC,UACAlB,EAAAA,EAAAA,KAACoD,EAAAA,EAAG,CAAAlC,UACAlB,EAAAA,EAAAA,KAACqD,EAAAA,EAAI,CAAAnC,UACDlB,EAAAA,EAAAA,KAACqD,EAAAA,EAAKC,KAAI,CAAApC,UACNgC,EAAAA,EAAAA,MAAC1C,EAAAA,EAAK,CAACC,SAAO,EAACC,UAAQ,EAACE,OAAK,EAACG,YAAU,EAAAG,SAAA,EACpClB,EAAAA,EAAAA,KAAA,SAAAkB,UACIgC,EAAAA,EAAAA,MAAA,MAAAhC,SAAA,EACIlB,EAAAA,EAAAA,KAAA,MAAAkB,SAAKE,EAAE,eACPpB,EAAAA,EAAAA,KAAA,MAAAkB,SAAKE,EAAE,mBACPpB,EAAAA,EAAAA,KAAA,MAAAkB,SAAKE,EAAE,eACPpB,EAAAA,EAAAA,KAAA,MAAAkB,SAAKE,EAAE,aACPpB,EAAAA,EAAAA,KAAA,MAAAkB,SAAKE,EAAE,mBACPpB,EAAAA,EAAAA,KAAA,MAAAkB,SAAKE,EAAE,kBACPpB,EAAAA,EAAAA,KAAA,MAAAkB,SAAKE,EAAE,iBACPpB,EAAAA,EAAAA,KAAA,MAAAkB,SAAKE,EAAE,uBACPpB,EAAAA,EAAAA,KAAA,MAAAkB,SAAKE,EAAE,iBACPpB,EAAAA,EAAAA,KAAA,MAAAkB,SAAKE,EAAE,eACPpB,EAAAA,EAAAA,KAAA,MAAAkB,SAAKE,EAAE,oBACPpB,EAAAA,EAAAA,KAAA,MAAAkB,SAAKE,EAAE,sBAGfpB,EAAAA,EAAAA,KAAA,SAAAkB,SACuB,IAAlBI,EAAOiC,QACJvD,EAAAA,EAAAA,KAAA,MAAAkB,UACIlB,EAAAA,EAAAA,KAAA,MAAIwD,QAAQ,KAAK1E,UAAU,cAAaoC,SAAEE,EAAE,2BAGhDE,EAAOmC,IAAIZ,IAAK,IAAAa,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,OACZf,EAAAA,EAAAA,MAAA,MAAAhC,SAAA,EACIlB,EAAAA,EAAAA,KAAA,MAAAkB,SAAK2B,EAAMqB,KAAOrB,EAAMJ,MACxBzC,EAAAA,EAAAA,KAAA,MAAAkB,UAAmB,QAAdwC,EAAAb,EAAMsB,gBAAQ,IAAAT,OAAA,EAAdA,EAAgBU,OAAQ,OAC7BpE,EAAAA,EAAAA,KAAA,MAAAkB,UACIgC,EAAAA,EAAAA,MAAA,OAAAhC,SAAA,EACIlB,EAAAA,EAAAA,KAAA,OAAAkB,UAA6B,QAAvByC,EAAAd,EAAMwB,yBAAiB,IAAAV,OAAA,EAAvBA,EAAyBW,YAAa,OAC5CtE,EAAAA,EAAAA,KAAA,SAAOlB,UAAU,aAAYoC,UACD,QAAvB0C,EAAAf,EAAMwB,yBAAiB,IAAAT,GAAO,QAAPC,EAAvBD,EAAyBW,aAAK,IAAAV,OAAP,EAAvBA,EAAgCW,QAAS,YAItDxE,EAAAA,EAAAA,KAAA,MAAAkB,UAAiB,QAAZ4C,EAAAjB,EAAM4B,cAAM,IAAAX,OAAA,EAAZA,EAAcY,QAAQ,KAAM,UACjC1E,EAAAA,EAAAA,KAAA,MAAAkB,UAAuB,QAAlB6C,EAAAlB,EAAM8B,oBAAY,IAAAZ,OAAA,EAAlBA,EAAoBW,QAAQ,KAAM,cACvC1E,EAAAA,EAAAA,KAAA,MAAAkB,UAAsB,QAAjB8C,EAAAnB,EAAM+B,mBAAW,IAAAZ,OAAA,EAAjBA,EAAmBU,QAAQ,KAAM,cACtCxB,EAAAA,EAAAA,MAAA,MAAAhC,SAAA,EAAqB,QAAhB+C,EAAApB,EAAMgC,kBAAU,IAAAZ,OAAA,EAAhBA,EAAkBS,QAAQ,KAAM,SAAS,QAC9C1E,EAAAA,EAAAA,KAAA,MAAAkB,SACK2B,EAAMiC,cACH,GAAGjC,EAAMiC,cAAcJ,QAAQ,MAC/B,OAGR1E,EAAAA,EAAAA,KAAA,MAAAkB,SAAK2B,EAAMkC,UAAY,OACvB/E,EAAAA,EAAAA,KAAA,MAAAkB,SAAK2B,EAAMmC,QAAU,OACrBhF,EAAAA,EAAAA,KAAA,MAAAkB,SAAK8B,EAAeH,EAAMoC,kBAC1BjF,EAAAA,EAAAA,KAAA,MAAAkB,SAAK,IAAIgE,KAAKrC,EAAMsC,YAAYC,qBAxB3BvC,EAAMJ,sB,sFCnF3D,MAAMW,EAAmB1E,EAAAA,WAEzB,CAACO,EAAOL,KACN,OAAO,UACLE,KACGuG,IAEHtG,GAAIC,EAAY,MAAK,SACrBH,EAAQ,MACRyG,IAjDG,SAAe3G,GAKnB,IALoB,GACrBI,EAAE,SACFF,EAAQ,UACRC,KACGG,GACJN,EACCE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,OACxC,MAAMO,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChB+F,EAAQ,GACR7F,EAAU,GAqBhB,OApBAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAI4F,EACAC,EACA3C,SAHG5D,EAAMU,GAIY,kBAAdC,GAAuC,MAAbA,IAEjC2F,OACAC,SACA3C,SACEjD,GAEJ2F,EAAO3F,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GACxD4F,GAAMD,EAAMvF,MAAc,IAATwF,EAAgB,GAAG1G,IAAWiB,IAAU,GAAGjB,IAAWiB,KAASyF,KACvE,MAAT1C,GAAepD,EAAQM,KAAK,QAAQD,KAAS+C,KACnC,MAAV2C,GAAgB/F,EAAQM,KAAK,SAASD,KAAS0F,OAE9C,CAAC,IACHvG,EACHH,UAAWmB,IAAWnB,KAAcwG,KAAU7F,IAC7C,CACDV,KACAF,WACAyG,SAEJ,CAWOG,CAAOxG,GACZ,OAAoBe,EAAAA,EAAAA,KAAKhB,EAAW,IAC/BqG,EACHzG,IAAKA,EACLE,UAAWmB,IAAWnB,GAAYwG,EAAM/B,QAAU1E,OAGtDuE,EAAIlD,YAAc,MAClB,S,sFC1DA,MAAMwF,EAAwBhH,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPyG,EAASxF,YAAc,WACvB,UCdMyF,EAA0BjH,EAAAA,WAAiB,CAAAC,EAK9CC,KAAQ,IALuC,UAChDE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,gBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGP0G,EAAWzF,YAAc,aACzB,U,cCZA,MAAM0F,EAA0BlH,EAAAA,WAAiB,CAAAC,EAM9CC,KAAQ,IANuC,SAChDC,EAAQ,UACRC,EAEAC,GAAIC,EAAY,SACbC,GACJN,EACC,MAAM4B,GAASpB,EAAAA,EAAAA,IAAmBN,EAAU,eACtCgH,GAAeC,EAAAA,EAAAA,SAAQ,KAAM,CACjCC,mBAAoBxF,IAClB,CAACA,IACL,OAAoBP,EAAAA,EAAAA,KAAKgG,EAAAA,EAAkBC,SAAU,CACnDC,MAAOL,EACP3E,UAAuBlB,EAAAA,EAAAA,KAAKhB,EAAW,CACrCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWyB,SAIvCqF,EAAW1F,YAAc,aACzB,UCvBMiG,EAAuBzH,EAAAA,WAE7B,CAAAC,EAMGC,KAAQ,IANV,SACCC,EAAQ,UACRC,EAAS,QACTgC,EACA/B,GAAIC,EAAY,SACbC,GACJN,EACC,MAAM4B,GAASpB,EAAAA,EAAAA,IAAmBN,EAAU,YAC5C,OAAoBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWa,EAAU,GAAGP,KAAUO,IAAYP,EAAQzB,MAC9DG,MAGPkH,EAAQjG,YAAc,UACtB,UCjBMkG,EAA8B1H,EAAAA,WAAiB,CAAAC,EAKlDC,KAAQ,IAL2C,UACpDE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,qBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPmH,EAAelG,YAAc,iBAC7B,UCdMmG,EAAwB3H,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,OACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPoH,EAASnG,YAAc,WACvB,U,cCbA,MAAMoG,GAAgBC,EAAAA,EAAAA,GAAiB,MACjCC,EAA4B9H,EAAAA,WAAiB,CAAAC,EAKhDC,KAAQ,IALyC,UAClDE,EAAS,SACTD,EACAE,GAAIC,EAAYsH,KACbrH,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,kBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPuH,EAAatG,YAAc,eAC3B,UChBMuG,EAAwB/H,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,OACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPwH,EAASvG,YAAc,WACvB,UCbMwG,GAAgBH,EAAAA,EAAAA,GAAiB,MACjCI,EAAyBjI,EAAAA,WAAiB,CAAAC,EAK7CC,KAAQ,IALsC,UAC/CE,EAAS,SACTD,EACAE,GAAIC,EAAY0H,KACbzH,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,eACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGP0H,EAAUzG,YAAc,YACxB,UCPMmD,EAAoB3E,EAAAA,WAAiB,CAAAC,EAWxCC,KAAQ,IAXiC,SAC1CC,EAAQ,UACRC,EAAS,GACTsB,EAAE,KACFE,EAAI,OACJsG,EAAM,KACNC,GAAO,EAAK,SACZ3F,EAEAnC,GAAIC,EAAY,SACbC,GACJN,EACC,MAAM4B,GAASpB,EAAAA,EAAAA,IAAmBN,EAAU,QAC5C,OAAoBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWyB,EAAQH,GAAM,MAAMA,IAAME,GAAQ,QAAQA,IAAQsG,GAAU,UAAUA,KACvG1F,SAAU2F,GAAoB7G,EAAAA,EAAAA,KAAK0F,EAAU,CAC3CxE,SAAUA,IACPA,MAGTmC,EAAKnD,YAAc,OACnB,QAAe4G,OAAOC,OAAO1D,EAAM,CACjC2D,IAAKb,EACLc,MAAON,EACPO,SAAUV,EACVlD,KAAMoC,EACNyB,KAAMd,EACNe,KAAMX,EACNY,OAAQzB,EACR0B,OAAQ3B,EACR4B,WAAYnB,G", "sources": ["../node_modules/react-bootstrap/esm/Row.js", "../node_modules/react-bootstrap/esm/Badge.js", "../node_modules/react-bootstrap/esm/Table.js", "pages/agent/OrderReports.js", "../node_modules/react-bootstrap/esm/Col.js", "../node_modules/react-bootstrap/esm/CardBody.js", "../node_modules/react-bootstrap/esm/CardFooter.js", "../node_modules/react-bootstrap/esm/CardHeader.js", "../node_modules/react-bootstrap/esm/CardImg.js", "../node_modules/react-bootstrap/esm/CardImgOverlay.js", "../node_modules/react-bootstrap/esm/CardLink.js", "../node_modules/react-bootstrap/esm/CardSubtitle.js", "../node_modules/react-bootstrap/esm/CardText.js", "../node_modules/react-bootstrap/esm/CardTitle.js", "../node_modules/react-bootstrap/esm/Card.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Row = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'row');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const sizePrefix = `${decoratedBsPrefix}-cols`;\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let cols;\n    if (propValue != null && typeof propValue === 'object') {\n      ({\n        cols\n      } = propValue);\n    } else {\n      cols = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (cols != null) classes.push(`${sizePrefix}${infix}-${cols}`);\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, decoratedBsPrefix, ...classes)\n  });\n});\nRow.displayName = 'Row';\nexport default Row;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Badge = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  bg = 'primary',\n  pill = false,\n  text,\n  className,\n  as: Component = 'span',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'badge');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, pill && `rounded-pill`, text && `text-${text}`, bg && `bg-${bg}`)\n  });\n});\nBadge.displayName = 'Badge';\nexport default Badge;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Table = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  striped,\n  bordered,\n  borderless,\n  hover,\n  size,\n  variant,\n  responsive,\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'table');\n  const classes = classNames(className, decoratedBsPrefix, variant && `${decoratedBsPrefix}-${variant}`, size && `${decoratedBsPrefix}-${size}`, striped && `${decoratedBsPrefix}-${typeof striped === 'string' ? `striped-${striped}` : 'striped'}`, bordered && `${decoratedBsPrefix}-bordered`, borderless && `${decoratedBsPrefix}-borderless`, hover && `${decoratedBsPrefix}-hover`);\n  const table = /*#__PURE__*/_jsx(\"table\", {\n    ...props,\n    className: classes,\n    ref: ref\n  });\n  if (responsive) {\n    let responsiveClass = `${decoratedBsPrefix}-responsive`;\n    if (typeof responsive === 'string') {\n      responsiveClass = `${responsiveClass}-${responsive}`;\n    }\n    return /*#__PURE__*/_jsx(\"div\", {\n      className: responsiveClass,\n      children: table\n    });\n  }\n  return table;\n});\nTable.displayName = 'Table';\nexport default Table;", "import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst OrderReports = () => {\n    const { t } = useTranslation();\n    const [orders, setOrders] = useState([]);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        const fetchOrders = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setLoading(false);\n                return; // User not logged in\n            }\n\n            // First get the agent profile for the current user\n            const { data: agentProfile, error: agentError } = await supabase\n                .from('agent_profiles')\n                .select('user_id')\n                .eq('user_id', user.id)\n                .single();\n\n            if (agentError || !agentProfile) {\n                console.error('Error fetching agent profile:', agentError);\n                setLoading(false);\n                return;\n            }\n\n            // Fetch orders for this specific agent with related information\n            const { data, error } = await supabase\n                .from('orders')\n                .select(`\n                    id,\n                    cid,\n                    shares,\n                    proof_image_url,\n                    storage_cost,\n                    pledge_cost,\n                    total_rate,\n                    tech_fee_pct,\n                    sales_fee_pct,\n                    ops_fee_pct,\n                    start_at,\n                    end_at,\n                    review_status,\n                    created_at,\n                    updated_at,\n                    products (\n                        name,\n                        category,\n                        price\n                    ),\n                    customer_profiles (\n                        real_name,\n                        users (\n                            email\n                        )\n                    )\n                `)\n                .eq('agent_id', agentProfile.user_id)\n                .order('created_at', { ascending: false });\n\n            if (error) {\n                console.error('Error fetching orders:', error);\n            } else {\n                setOrders(data);\n            }\n            setLoading(false);\n        };\n\n        fetchOrders();\n    }, []);\n\n    const getStatusBadge = (status) => {\n        switch (status) {\n            case 'approved':\n                return <Badge bg=\"success\">{t('approved')}</Badge>;\n            case 'pending':\n                return <Badge bg=\"warning\">{t('pending_review')}</Badge>;\n            case 'rejected':\n                return <Badge bg=\"danger\">{t('rejected')}</Badge>;\n            default:\n                return <Badge bg=\"secondary\">{status || '-'}</Badge>;\n        }\n    };\n\n    if (loading) {\n        return <div>{t('loading_order_reports')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('order_list')}</h2>\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('order_id')}</th>\n                                        <th>{t('product_name')}</th>\n                                        <th>{t('customer')}</th>\n                                        <th>{t('shares')}</th>\n                                        <th>{t('storage_cost')}</th>\n                                        <th>{t('pledge_cost')}</th>\n                                        <th>{t('total_rate')}</th>\n                                        <th>{t('sales_commission')}</th>\n                                        <th>{t('start_date')}</th>\n                                        <th>{t('end_date')}</th>\n                                        <th>{t('review_status')}</th>\n                                        <th>{t('created_at')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {orders.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"12\" className=\"text-center\">{t('no_orders_available')}</td>\n                                        </tr>\n                                    ) : (\n                                        orders.map(order => (\n                                            <tr key={order.id}>\n                                                <td>{order.cid || order.id}</td>\n                                                <td>{order.products?.name || '-'}</td>\n                                                <td>\n                                                    <div>\n                                                        <div>{order.customer_profiles?.real_name || '-'}</div>\n                                                        <small className=\"text-muted\">\n                                                            {order.customer_profiles?.users?.email || '-'}\n                                                        </small>\n                                                    </div>\n                                                </td>\n                                                <td>{order.shares?.toFixed(2) || '0.00'}</td>\n                                                <td>{order.storage_cost?.toFixed(6) || '0.000000'}</td>\n                                                <td>{order.pledge_cost?.toFixed(6) || '0.000000'}</td>\n                                                <td>{order.total_rate?.toFixed(4) || '0.0000'}%</td>\n                                                <td>\n                                                    {order.sales_fee_pct ?\n                                                        `${order.sales_fee_pct.toFixed(2)}%` :\n                                                        '-'\n                                                    }\n                                                </td>\n                                                <td>{order.start_at || '-'}</td>\n                                                <td>{order.end_at || '-'}</td>\n                                                <td>{getStatusBadge(order.review_status)}</td>\n                                                <td>{new Date(order.created_at).toLocaleString()}</td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default OrderReports;\n", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function useCol({\n  as,\n  bsPrefix,\n  className,\n  ...props\n}) {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'col');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const spans = [];\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let span;\n    let offset;\n    let order;\n    if (typeof propValue === 'object' && propValue != null) {\n      ({\n        span,\n        offset,\n        order\n      } = propValue);\n    } else {\n      span = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (span) spans.push(span === true ? `${bsPrefix}${infix}` : `${bsPrefix}${infix}-${span}`);\n    if (order != null) classes.push(`order${infix}-${order}`);\n    if (offset != null) classes.push(`offset${infix}-${offset}`);\n  });\n  return [{\n    ...props,\n    className: classNames(className, ...spans, ...classes)\n  }, {\n    as,\n    bsPrefix,\n    spans\n  }];\n}\nconst Col = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n(props, ref) => {\n  const [{\n    className,\n    ...colProps\n  }, {\n    as: Component = 'div',\n    bsPrefix,\n    spans\n  }] = useCol(props);\n  return /*#__PURE__*/_jsx(Component, {\n    ...colProps,\n    ref: ref,\n    className: classNames(className, !spans.length && bsPrefix)\n  });\n});\nCol.displayName = 'Col';\nexport default Col;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});"], "names": ["Row", "React", "_ref", "ref", "bsPrefix", "className", "as", "Component", "props", "decoratedBsPrefix", "useBootstrapPrefix", "breakpoints", "useBootstrapBreakpoints", "minBreakpoint", "useBootstrapMinBreakpoint", "sizePrefix", "classes", "for<PERSON>ach", "brkPoint", "propValue", "cols", "infix", "push", "_jsx", "classNames", "displayName", "Badge", "bg", "pill", "text", "prefix", "Table", "striped", "bordered", "borderless", "hover", "size", "variant", "responsive", "table", "responsiveClass", "children", "OrderReports", "t", "useTranslation", "orders", "setOrders", "useState", "loading", "setLoading", "useEffect", "async", "supabase", "getSupabase", "data", "user", "auth", "getUser", "agentProfile", "error", "agent<PERSON><PERSON>r", "from", "select", "eq", "id", "single", "console", "user_id", "order", "ascending", "fetchOrders", "getStatusBadge", "status", "_jsxs", "Container", "Col", "Card", "Body", "length", "colSpan", "map", "_order$products", "_order$customer_profi", "_order$customer_profi2", "_order$customer_profi3", "_order$shares", "_order$storage_cost", "_order$pledge_cost", "_order$total_rate", "cid", "products", "name", "customer_profiles", "real_name", "users", "email", "shares", "toFixed", "storage_cost", "pledge_cost", "total_rate", "sales_fee_pct", "start_at", "end_at", "review_status", "Date", "created_at", "toLocaleString", "colProps", "spans", "span", "offset", "useCol", "CardBody", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "value", "CardImg", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "border", "body", "Object", "assign", "Img", "Title", "Subtitle", "Link", "Text", "Header", "Footer", "ImgOverlay"], "sourceRoot": ""}