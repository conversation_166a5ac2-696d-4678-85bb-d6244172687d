{"version": 3, "file": "static/js/820.1b8bd04a.chunk.js", "mappings": "wKAMA,MAAMA,EAAmBC,EAAAA,WAAiB,CAAAC,EAMvCC,KAAQ,IANgC,SACzCC,EAAQ,UACRC,EAEAC,GAAIC,EAAY,SACbC,GACJN,EACC,MAAMO,GAAoBC,EAAAA,EAAAA,IAAmBN,EAAU,OACjDO,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChBC,EAAa,GAAGN,SAChBO,EAAU,GAehB,OAdAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAIE,SADGZ,EAAMU,GAEI,MAAbC,GAA0C,kBAAdA,IAE5BC,QACED,GAEJC,EAAOD,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GAChD,MAARE,GAAcJ,EAAQM,KAAK,GAAGP,IAAaM,KAASD,QAEtCG,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWI,KAAsBO,OAG3DhB,EAAIyB,YAAc,MAClB,S,sFCjCA,MAAMC,EAAqBzB,EAAAA,WAAiB,CAAAC,EAQzCC,KAAQ,IARkC,SAC3CC,EAAQ,GACRuB,EAAK,UAAS,KACdC,GAAO,EAAK,KACZC,EAAI,UACJxB,EACAC,GAAIC,EAAY,UACbC,GACJN,EACC,MAAM4B,GAASpB,EAAAA,EAAAA,IAAmBN,EAAU,SAC5C,OAAoBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWyB,EAAQF,GAAQ,eAAgBC,GAAQ,QAAQA,IAAQF,GAAM,MAAMA,SAGzGD,EAAMD,YAAc,QACpB,S,sFCjBA,MAAMM,EAAqB9B,EAAAA,WAAiB,CAAAC,EAWzCC,KAAQ,IAXkC,SAC3CC,EAAQ,UACRC,EAAS,QACT2B,EAAO,SACPC,EAAQ,WACRC,EAAU,MACVC,EAAK,KACLC,EAAI,QACJC,EAAO,WACPC,KACG9B,GACJN,EACC,MAAMO,GAAoBC,EAAAA,EAAAA,IAAmBN,EAAU,SACjDY,EAAUQ,IAAWnB,EAAWI,EAAmB4B,GAAW,GAAG5B,KAAqB4B,IAAWD,GAAQ,GAAG3B,KAAqB2B,IAAQJ,GAAW,GAAGvB,KAAwC,kBAAZuB,EAAuB,WAAWA,IAAY,YAAaC,GAAY,GAAGxB,aAA8ByB,GAAc,GAAGzB,eAAgC0B,GAAS,GAAG1B,WACxV8B,GAAqBhB,EAAAA,EAAAA,KAAK,QAAS,IACpCf,EACHH,UAAWW,EACXb,IAAKA,IAEP,GAAImC,EAAY,CACd,IAAIE,EAAkB,GAAG/B,eAIzB,MAH0B,kBAAf6B,IACTE,EAAkB,GAAGA,KAAmBF,MAEtBf,EAAAA,EAAAA,KAAK,MAAO,CAC9BlB,UAAWmC,EACXC,SAAUF,GAEd,CACA,OAAOA,IAETR,EAAMN,YAAc,QACpB,S,6LChCA,MAAMiB,EAA8BzC,EAAAA,WAAiB,CAAAC,EAKlDC,KAAQ,IAL2C,UACpDE,EAAS,SACTD,EACAE,GAAIC,EAAY,UACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,qBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPkC,EAAejB,YAAc,iBAC7B,UCEMkB,EAA0B1C,EAAAA,WAAiB,CAAAC,EAQ9CC,KAAQ,IARuC,SAChDC,EAAQ,KACRgC,EAAI,cACJQ,EAAa,UACbvC,EAEAC,GAAIC,EAAY,SACbC,GACJN,EACCE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,eAIxC,MAAMyC,GAAeC,EAAAA,EAAAA,SAAQ,KAAM,CAAG,GAAG,IACzC,OAAoBvB,EAAAA,EAAAA,KAAKwB,EAAAA,EAAkBC,SAAU,CACnDC,MAAOJ,EACPJ,UAAuBlB,EAAAA,EAAAA,KAAKhB,EAAW,CACrCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWD,EAAUgC,GAAQ,GAAGhC,KAAYgC,IAAQQ,GAAiB,wBAIjGD,EAAWlB,YAAc,aACzB,QAAeyB,OAAOC,OAAOR,EAAY,CACvCS,KAAMV,EACNW,MAhCsB7C,IAAsBe,EAAAA,EAAAA,KAAKmB,EAAgB,CACjED,UAAuBlB,EAAAA,EAAAA,KAAK+B,EAAAA,EAAgB,CAC1CC,KAAM,WACH/C,MA8BLgD,SAvCyBhD,IAAsBe,EAAAA,EAAAA,KAAKmB,EAAgB,CACpED,UAAuBlB,EAAAA,EAAAA,KAAK+B,EAAAA,EAAgB,CAC1CC,KAAM,cACH/C,Q,4CCPP,MA4TA,EA5TgBiD,KACZ,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAASC,IAAcC,EAAAA,EAAAA,UAAS,KAChCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCG,EAAYC,IAAiBJ,EAAAA,EAAAA,UAAS,KACtCK,EAAcC,IAAmBN,EAAAA,EAAAA,UAAS,KAC1CO,EAAWC,IAAgBR,EAAAA,EAAAA,UAAS,KACpCS,EAASC,IAAcV,EAAAA,EAAAA,UAAS,KAChCW,EAAiBC,IAAsBZ,EAAAA,EAAAA,UAAS,KAEvDa,EAAAA,EAAAA,WAAU,KACmBC,WACjB,MAAMC,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EAAU,OAEfb,GAAW,GACX,MAAQe,MAAM,KAAEC,UAAiBH,EAASI,KAAKC,UAE/C,IAAKF,EAED,YADAhB,GAAW,GAKf,MAAQe,KAAMI,EAAWC,MAAOC,SAAuBR,EAClDS,KAAK,qBACLC,OAAO,qCACPC,GAAG,WAAYR,EAAKS,IACpBC,MAAM,aAAc,CAAEC,WAAW,IAEtC,GAAIN,IAAiBF,EAGjB,OAFAS,QAAQR,MAAM,oCAAqCC,QACnDrB,GAAW,GAKCmB,EAAUU,IAAIC,GAAKA,EAAEC,SAASC,OAAOC,SAArD,MAEQlB,KAAMmB,EAAcd,MAAOe,SAAoBtB,EAClDS,KAAK,SACLC,OAAO,yBAERY,GACAP,QAAQR,MAAM,wBAAyBe,GAI3C,MAAMC,EAAW,IAAIC,KAAKH,GAAgB,IAAIL,IAAIS,GAAK,CAACA,EAAEb,GAAIa,KAExDC,EAAkBpB,EAAUU,IAAIC,IAAC,IAChCA,EACHU,MAAOJ,EAASK,IAAIX,EAAEC,UAAY,CAAC,KAGvClC,EAAW0C,GACXvC,GAAW,IAGnB0C,IACD,KAGH/B,EAAAA,EAAAA,WAAU,KACN,IAAIgC,EAAW/C,EAGXK,IACA0C,EAAWA,EAASX,OAAOY,IAAM,IAAAC,EAAAC,EAAAC,EAAA,OACjB,QAAZF,EAAAD,EAAOJ,aAAK,IAAAK,GAAO,QAAPC,EAAZD,EAAcG,aAAK,IAAAF,OAAP,EAAZA,EAAqBG,cAAcC,SAASjD,EAAWgD,kBACvC,QADqDF,EACrEH,EAAOO,iBAAS,IAAAJ,OAAA,EAAhBA,EAAkBE,cAAcC,SAASjD,EAAWgD,mBAKxD9C,IACAwC,EAAWA,EAASX,OAAOY,GAAUA,EAAOQ,gBAAkBjD,IAI9DE,IACAsC,EAAWA,EAASX,OAAOY,IAAM,IAAAS,EAAA,OAC7B,IAAIC,KAAiB,QAAbD,EAACT,EAAOJ,aAAK,IAAAa,OAAA,EAAZA,EAAcE,aAAe,IAAID,KAAKjD,MAGnDE,IACAoC,EAAWA,EAASX,OAAOY,IAAM,IAAAY,EAAA,OAC7B,IAAIF,KAAiB,QAAbE,EAACZ,EAAOJ,aAAK,IAAAgB,OAAA,EAAZA,EAAcD,aAAe,IAAID,KAAK/C,MAIvDG,EAAmBiC,IACpB,CAAC/C,EAASK,EAAYE,EAAcE,EAAWE,IAElD,MAAMkD,EAAkBC,IACpB,OAAQA,GACJ,IAAK,WACD,OAAOnG,EAAAA,EAAAA,KAACG,EAAAA,EAAK,CAACC,GAAG,UAASc,SAAEiB,EAAE,cAClC,IAAK,UACD,OAAOnC,EAAAA,EAAAA,KAACG,EAAAA,EAAK,CAACC,GAAG,UAASc,SAAEiB,EAAE,oBAClC,IAAK,WACD,OAAOnC,EAAAA,EAAAA,KAACG,EAAAA,EAAK,CAACC,GAAG,SAAQc,SAAEiB,EAAE,cACjC,IAAK,eACD,OAAOnC,EAAAA,EAAAA,KAACG,EAAAA,EAAK,CAACC,GAAG,OAAMc,SAAEiB,EAAE,kBAC/B,QACI,OAAOnC,EAAAA,EAAAA,KAACG,EAAAA,EAAK,CAACC,GAAG,YAAWc,SAAEiF,GAAUhE,EAAE,qBA6BtD,OAAIK,GACOxC,EAAAA,EAAAA,KAAA,OAAAkB,SAAMiB,EAAE,sBAIfiE,EAAAA,EAAAA,MAACC,EAAAA,EAAS,CAAAnF,SAAA,EACNlB,EAAAA,EAAAA,KAAA,MAAIlB,UAAU,OAAMoC,SAAEiB,EAAE,kBAGxBnC,EAAAA,EAAAA,KAACvB,EAAAA,EAAG,CAACK,UAAU,OAAMoC,UACjBlB,EAAAA,EAAAA,KAACsG,EAAAA,EAAG,CAAApF,UACAlB,EAAAA,EAAAA,KAACuG,EAAAA,EAAI,CAAArF,UACDlB,EAAAA,EAAAA,KAACuG,EAAAA,EAAKC,KAAI,CAAAtF,UACNkF,EAAAA,EAAAA,MAAC3H,EAAAA,EAAG,CAACK,UAAU,kBAAiBoC,SAAA,EAC5BlB,EAAAA,EAAAA,KAACsG,EAAAA,EAAG,CAACG,GAAI,EAAEvF,UACPkF,EAAAA,EAAAA,MAACM,EAAAA,EAAM,CACH5F,QAAQ,UACR6F,QArCZC,KAEpBC,MAAM1E,EAAE,4BAoCwBrD,UAAU,OAAMoC,SAAA,EAEhBlB,EAAAA,EAAAA,KAAC8G,EAAAA,IAAM,CAAChI,UAAU,SACjBqD,EAAE,oBAGXnC,EAAAA,EAAAA,KAACsG,EAAAA,EAAG,CAACG,GAAI,EAAEvF,UACPkF,EAAAA,EAAAA,MAACW,EAAAA,EAAKC,MAAK,CAAA9F,SAAA,EACPlB,EAAAA,EAAAA,KAAC+G,EAAAA,EAAKE,MAAK,CAAA/F,SAAEiB,EAAE,sBACfnC,EAAAA,EAAAA,KAACoB,EAAU,CAAAF,UACPlB,EAAAA,EAAAA,KAAC+G,EAAAA,EAAKG,QAAO,CACTlF,KAAK,OACLmF,YAAahF,EAAE,yBACfT,MAAOgB,EACP0E,SAAWC,GAAM1E,EAAc0E,EAAEC,OAAO5F,iBAKxD1B,EAAAA,EAAAA,KAACsG,EAAAA,EAAG,CAACG,GAAI,EAAEvF,UACPkF,EAAAA,EAAAA,MAACW,EAAAA,EAAKC,MAAK,CAAA9F,SAAA,EACPlB,EAAAA,EAAAA,KAAC+G,EAAAA,EAAKE,MAAK,CAAA/F,SAAEiB,EAAE,oBACfiE,EAAAA,EAAAA,MAACW,EAAAA,EAAKQ,OAAM,CACR7F,MAAOkB,EACPwE,SAAWC,GAAMxE,EAAgBwE,EAAEC,OAAO5F,OAAOR,SAAA,EAEjDlB,EAAAA,EAAAA,KAAA,UAAQ0B,MAAM,GAAER,SAAEiB,EAAE,2BACpBnC,EAAAA,EAAAA,KAAA,UAAQ0B,MAAM,UAASR,SAAEiB,EAAE,qBAC3BnC,EAAAA,EAAAA,KAAA,UAAQ0B,MAAM,WAAUR,SAAEiB,EAAE,eAC5BnC,EAAAA,EAAAA,KAAA,UAAQ0B,MAAM,WAAUR,SAAEiB,EAAE,eAC5BnC,EAAAA,EAAAA,KAAA,UAAQ0B,MAAM,eAAcR,SAAEiB,EAAE,2BAI5CnC,EAAAA,EAAAA,KAACsG,EAAAA,EAAG,CAACG,GAAI,EAAEvF,UACPkF,EAAAA,EAAAA,MAACW,EAAAA,EAAKC,MAAK,CAAA9F,SAAA,EACPlB,EAAAA,EAAAA,KAAC+G,EAAAA,EAAKE,MAAK,CAAA/F,SAAEiB,EAAE,iBACfnC,EAAAA,EAAAA,KAAC+G,EAAAA,EAAKG,QAAO,CACTlF,KAAK,OACLN,MAAOoB,EACPsE,SAAWC,GAAMtE,EAAasE,EAAEC,OAAO5F,eAInD1B,EAAAA,EAAAA,KAACsG,EAAAA,EAAG,CAACG,GAAI,EAAEvF,UACPkF,EAAAA,EAAAA,MAACW,EAAAA,EAAKC,MAAK,CAAA9F,SAAA,EACPlB,EAAAA,EAAAA,KAAC+G,EAAAA,EAAKE,MAAK,CAAA/F,SAAEiB,EAAE,eACfnC,EAAAA,EAAAA,KAAC+G,EAAAA,EAAKG,QAAO,CACTlF,KAAK,OACLN,MAAOsB,EACPoE,SAAWC,GAAMpE,EAAWoE,EAAEC,OAAO5F,eAIjD1B,EAAAA,EAAAA,KAACsG,EAAAA,EAAG,CAACG,GAAI,EAAEvF,UACPlB,EAAAA,EAAAA,KAAC0G,EAAAA,EAAM,CACH5F,QAAQ,kBACR6F,QApGfa,KAEjBnD,QAAQoD,IAAI,qBAmGoB3I,UAAU,OAAMoC,UAEhBlB,EAAAA,EAAAA,KAAC0H,EAAAA,IAAQ,oBAUrC1H,EAAAA,EAAAA,KAACvB,EAAAA,EAAG,CAAAyC,UACAlB,EAAAA,EAAAA,KAACsG,EAAAA,EAAG,CAAApF,UACAlB,EAAAA,EAAAA,KAACuG,EAAAA,EAAI,CAAArF,UACDlB,EAAAA,EAAAA,KAACuG,EAAAA,EAAKC,KAAI,CAAAtF,UACNkF,EAAAA,EAAAA,MAAC5F,EAAAA,EAAK,CAACC,SAAO,EAACC,UAAQ,EAACE,OAAK,EAACG,YAAU,EAAAG,SAAA,EACpClB,EAAAA,EAAAA,KAAA,SAAAkB,UACIkF,EAAAA,EAAAA,MAAA,MAAAlF,SAAA,EACIlB,EAAAA,EAAAA,KAAA,MAAAkB,SAAKiB,EAAE,eACPnC,EAAAA,EAAAA,KAAA,MAAAkB,SAAKiB,EAAE,gBACPnC,EAAAA,EAAAA,KAAA,MAAAkB,SAAKiB,EAAE,gBACPnC,EAAAA,EAAAA,KAAA,MAAAkB,SAAKiB,EAAE,qBACPnC,EAAAA,EAAAA,KAAA,MAAAkB,SAAKiB,EAAE,oBACPnC,EAAAA,EAAAA,KAAA,MAAAkB,SAAKiB,EAAE,iBACPnC,EAAAA,EAAAA,KAAA,MAAAkB,SAAKiB,EAAE,aACPnC,EAAAA,EAAAA,KAAA,MAAAkB,SAAKiB,EAAE,wBACPnC,EAAAA,EAAAA,KAAA,MAAAkB,SAAKiB,EAAE,mBAGfnC,EAAAA,EAAAA,KAAA,SAAAkB,SACgC,IAA3BgC,EAAgByE,QACb3H,EAAAA,EAAAA,KAAA,MAAAkB,UACIlB,EAAAA,EAAAA,KAAA,MAAI4H,QAAQ,IAAI9I,UAAU,cAAaoC,SAAEiB,EAAE,wBAG/Ce,EAAgBoB,IAAIe,IAAM,IAAAwC,EAAAC,EAAAC,EAAAC,EAAA,OACtB5B,EAAAA,EAAAA,MAAA,MAAAlF,SAAA,EACIlB,EAAAA,EAAAA,KAAA,MAAAkB,UAAiB,QAAZ2G,EAAAxC,EAAOJ,aAAK,IAAA4C,OAAA,EAAZA,EAAcpC,QAAS,OAC5BzF,EAAAA,EAAAA,KAAA,MAAAkB,SAAKmE,EAAOO,WAAa,OACzB5F,EAAAA,EAAAA,KAAA,MAAAkB,SAAKmE,EAAO4C,WAAa,OACzBjI,EAAAA,EAAAA,KAAA,MAAAkB,SACKmE,EAAO6C,cACJlI,EAAAA,EAAAA,KAACG,EAAAA,EAAK,CAACC,GAAG,UAASc,SAAEiB,EAAE,eAEvBnC,EAAAA,EAAAA,KAACG,EAAAA,EAAK,CAACC,GAAG,YAAWc,SAAEiB,EAAE,qBAGjCnC,EAAAA,EAAAA,KAAA,MAAAkB,SACKmE,EAAO8C,aACJnI,EAAAA,EAAAA,KAACG,EAAAA,EAAK,CAACC,GAAG,UAASc,SAAEiB,EAAE,eAEvBnC,EAAAA,EAAAA,KAACG,EAAAA,EAAK,CAACC,GAAG,YAAWc,SAAEiB,EAAE,qBAGjCnC,EAAAA,EAAAA,KAAA,MAAAkB,UACIkF,EAAAA,EAAAA,MAAA,OAAAlF,SAAA,EACIlB,EAAAA,EAAAA,KAAA,OAAAkB,UAAuB,QAAjB4G,EAAAzC,EAAO+C,kBAAU,IAAAN,OAAA,EAAjBA,EAAmBO,aAAc,OACvCrI,EAAAA,EAAAA,KAAA,SAAOlB,UAAU,aAAYoC,UACP,QAAjB6G,EAAA1C,EAAO+C,kBAAU,IAAAL,OAAA,EAAjBA,EAAmBtC,QAAS,YAIzCzF,EAAAA,EAAAA,KAAA,MAAAkB,SAAKgF,EAAeb,EAAOQ,kBAC3B7F,EAAAA,EAAAA,KAAA,MAAAkB,SAAiB,QAAZ8G,EAAA3C,EAAOJ,aAAK,IAAA+C,GAAZA,EAAchC,WAAa,IAAID,KAAKV,EAAOJ,MAAMe,YAAYsC,iBAAmB,OACrFtI,EAAAA,EAAAA,KAAA,MAAAkB,UACIkF,EAAAA,EAAAA,MAAA,OAAKtH,UAAU,eAAcoC,SAAA,EACzBlB,EAAAA,EAAAA,KAAC0G,EAAAA,EAAM,CACH7F,KAAK,KACLC,QAAQ,kBACR6F,QAASA,KAAsBtB,EAAOb,aA/J1FqC,MAAM1E,EAAE,4BAgK4CoG,MAAOpG,EAAE,cAAcjB,UAEvBlB,EAAAA,EAAAA,KAACwI,EAAAA,IAAW,OAEhBxI,EAAAA,EAAAA,KAAC0G,EAAAA,EAAM,CACH7F,KAAK,KACLC,QAAQ,kBACR6F,QAASA,KAAwBtB,EAAOb,aAlK5FqC,MAAM1E,EAAE,8BAmK4CoG,MAAOpG,EAAE,gBAAgBjB,UAEzBlB,EAAAA,EAAAA,KAACyI,EAAAA,IAAa,OAElBzI,EAAAA,EAAAA,KAAC0G,EAAAA,EAAM,CACH7F,KAAK,KACLC,QAAQ,eACR6F,QAASA,KAAwBtB,EAAOb,aArK5FqC,MAAM1E,EAAE,8BAsK4CoG,MAAOpG,EAAE,gBAAgBjB,UAEzBlB,EAAAA,EAAAA,KAAC0I,EAAAA,IAAK,aApDbrD,EAAOb,2B", "sources": ["../node_modules/react-bootstrap/esm/Row.js", "../node_modules/react-bootstrap/esm/Badge.js", "../node_modules/react-bootstrap/esm/Table.js", "../node_modules/react-bootstrap/esm/InputGroupText.js", "../node_modules/react-bootstrap/esm/InputGroup.js", "pages/agent/Members.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Row = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'row');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const sizePrefix = `${decoratedBsPrefix}-cols`;\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let cols;\n    if (propValue != null && typeof propValue === 'object') {\n      ({\n        cols\n      } = propValue);\n    } else {\n      cols = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (cols != null) classes.push(`${sizePrefix}${infix}-${cols}`);\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, decoratedBsPrefix, ...classes)\n  });\n});\nRow.displayName = 'Row';\nexport default Row;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Badge = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  bg = 'primary',\n  pill = false,\n  text,\n  className,\n  as: Component = 'span',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'badge');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, pill && `rounded-pill`, text && `text-${text}`, bg && `bg-${bg}`)\n  });\n});\nBadge.displayName = 'Badge';\nexport default Badge;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Table = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  striped,\n  bordered,\n  borderless,\n  hover,\n  size,\n  variant,\n  responsive,\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'table');\n  const classes = classNames(className, decoratedBsPrefix, variant && `${decoratedBsPrefix}-${variant}`, size && `${decoratedBsPrefix}-${size}`, striped && `${decoratedBsPrefix}-${typeof striped === 'string' ? `striped-${striped}` : 'striped'}`, bordered && `${decoratedBsPrefix}-bordered`, borderless && `${decoratedBsPrefix}-borderless`, hover && `${decoratedBsPrefix}-hover`);\n  const table = /*#__PURE__*/_jsx(\"table\", {\n    ...props,\n    className: classes,\n    ref: ref\n  });\n  if (responsive) {\n    let responsiveClass = `${decoratedBsPrefix}-responsive`;\n    if (typeof responsive === 'string') {\n      responsiveClass = `${responsiveClass}-${responsive}`;\n    }\n    return /*#__PURE__*/_jsx(\"div\", {\n      className: responsiveClass,\n      children: table\n    });\n  }\n  return table;\n});\nTable.displayName = 'Table';\nexport default Table;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst InputGroupText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'span',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'input-group-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nInputGroupText.displayName = 'InputGroupText';\nexport default InputGroupText;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport FormCheckInput from './FormCheckInput';\nimport InputGroupContext from './InputGroupContext';\nimport InputGroupText from './InputGroupText';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst InputGroupCheckbox = props => /*#__PURE__*/_jsx(InputGroupText, {\n  children: /*#__PURE__*/_jsx(FormCheckInput, {\n    type: \"checkbox\",\n    ...props\n  })\n});\nconst InputGroupRadio = props => /*#__PURE__*/_jsx(InputGroupText, {\n  children: /*#__PURE__*/_jsx(FormCheckInput, {\n    type: \"radio\",\n    ...props\n  })\n});\nconst InputGroup = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  size,\n  hasValidation,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'input-group');\n\n  // Intentionally an empty object. Used in detecting if a dropdown\n  // exists under an input group.\n  const contextValue = useMemo(() => ({}), []);\n  return /*#__PURE__*/_jsx(InputGroupContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, bsPrefix, size && `${bsPrefix}-${size}`, hasValidation && 'has-validation')\n    })\n  });\n});\nInputGroup.displayName = 'InputGroup';\nexport default Object.assign(InputGroup, {\n  Text: InputGroupText,\n  Radio: InputGroupRadio,\n  Checkbox: InputGroupCheckbox\n});", "import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Badge, Button, Form, InputGroup, Dropdown } from 'react-bootstrap';\nimport { FaSearch, FaPlus, FaEye, FaUserCheck, FaExchangeAlt } from 'react-icons/fa';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst Members = () => {\n    const { t } = useTranslation();\n    const [members, setMembers] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [searchTerm, setSearchTerm] = useState('');\n    const [statusFilter, setStatusFilter] = useState('');\n    const [startDate, setStartDate] = useState('');\n    const [endDate, setEndDate] = useState('');\n    const [filteredMembers, setFilteredMembers] = useState([]);\n\n    useEffect(() => {\n            const fetchMembers = async () => {\n                const supabase = getSupabase();\n                if (!supabase) return;\n    \n                setLoading(true);\n                const { data: { user } } = await supabase.auth.getUser();\n    \n                if (!user) {\n                    setLoading(false);\n                    return;\n                }\n    \n                // Step 1: 查询 customer_profiles\n                const { data: customers, error: profileError } = await supabase\n                    .from('customer_profiles')\n                    .select('user_id, real_name, verify_status')\n                    .eq('agent_id', user.id)\n                    .order('created_at', { ascending: false });\n    \n                if (profileError || !customers) {\n                    console.error('Error fetching customer_profiles:', profileError);\n                    setLoading(false);\n                    return;\n                }\n    \n                // Step 2: 查询 users 表\n                const userIds = customers.map(c => c.user_id).filter(Boolean);\n    \n                const { data: userInfoList, error: userError } = await supabase\n                    .from('users')\n                    .select('id, email, created_at')\n\n                if (userError) {\n                    console.error('Error fetching users:', userError);\n                }\n    \n                // Step 3: 合并结果\n                const usersMap = new Map((userInfoList || []).map(u => [u.id, u]));\n    \n                const enrichedMembers = customers.map(c => ({\n                    ...c,\n                    users: usersMap.get(c.user_id) || {}\n                }));\n    \n                setMembers(enrichedMembers);\n                setLoading(false);\n            };\n\n        fetchMembers();\n    }, []);\n\n    // Filter members based on search criteria\n    useEffect(() => {\n        let filtered = members;\n\n        // Search by username (email)\n        if (searchTerm) {\n            filtered = filtered.filter(member =>\n                member.users?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                member.real_name?.toLowerCase().includes(searchTerm.toLowerCase())\n            );\n        }\n\n        // Filter by status\n        if (statusFilter) {\n            filtered = filtered.filter(member => member.verify_status === statusFilter);\n        }\n\n        // Filter by date range\n        if (startDate) {\n            filtered = filtered.filter(member => \n                new Date(member.users?.created_at) >= new Date(startDate)\n            );\n        }\n        if (endDate) {\n            filtered = filtered.filter(member => \n                new Date(member.users?.created_at) <= new Date(endDate)\n            );\n        }\n\n        setFilteredMembers(filtered);\n    }, [members, searchTerm, statusFilter, startDate, endDate]);\n\n    const getStatusBadge = (status) => {\n        switch (status) {\n            case 'approved':\n                return <Badge bg=\"success\">{t('approved')}</Badge>;\n            case 'pending':\n                return <Badge bg=\"warning\">{t('pending_review')}</Badge>;\n            case 'rejected':\n                return <Badge bg=\"danger\">{t('rejected')}</Badge>;\n            case 'under_review':\n                return <Badge bg=\"info\">{t('under_review')}</Badge>;\n            default:\n                return <Badge bg=\"secondary\">{status || t('not_submitted')}</Badge>;\n        }\n    };\n\n    const handleSearch = () => {\n        // Search is handled by useEffect, this function can be used for additional logic if needed\n        console.log('Search triggered');\n    };\n\n    const handleAddMember = () => {\n        // TODO: Implement add member functionality\n        alert(t('add_member_coming_soon'));\n    };\n\n    const handleKycReview = (memberId) => {\n        // TODO: Implement KYC review functionality\n        alert(t('kyc_review_coming_soon'));\n    };\n\n    const handleChangeAgent = (memberId) => {\n        // TODO: Implement change agent functionality\n        alert(t('change_agent_coming_soon'));\n    };\n\n    const handleViewDetails = (memberId) => {\n        // TODO: Implement view details functionality\n        alert(t('view_details_coming_soon'));\n    };\n\n    if (loading) {\n        return <div>{t('loading_members')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('member_list')}</h2>\n            \n            {/* Top Operation Bar */}\n            <Row className=\"mb-4\">\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Row className=\"align-items-end\">\n                                <Col md={2}>\n                                    <Button \n                                        variant=\"primary\" \n                                        onClick={handleAddMember}\n                                        className=\"mb-2\"\n                                    >\n                                        <FaPlus className=\"me-1\" />\n                                        {t('add_member')}\n                                    </Button>\n                                </Col>\n                                <Col md={3}>\n                                    <Form.Group>\n                                        <Form.Label>{t('search_username')}</Form.Label>\n                                        <InputGroup>\n                                            <Form.Control\n                                                type=\"text\"\n                                                placeholder={t('please_enter_username')}\n                                                value={searchTerm}\n                                                onChange={(e) => setSearchTerm(e.target.value)}\n                                            />\n                                        </InputGroup>\n                                    </Form.Group>\n                                </Col>\n                                <Col md={2}>\n                                    <Form.Group>\n                                        <Form.Label>{t('status_filter')}</Form.Label>\n                                        <Form.Select\n                                            value={statusFilter}\n                                            onChange={(e) => setStatusFilter(e.target.value)}\n                                        >\n                                            <option value=\"\">{t('please_select_status')}</option>\n                                            <option value=\"pending\">{t('pending_review')}</option>\n                                            <option value=\"approved\">{t('approved')}</option>\n                                            <option value=\"rejected\">{t('rejected')}</option>\n                                            <option value=\"under_review\">{t('under_review')}</option>\n                                        </Form.Select>\n                                    </Form.Group>\n                                </Col>\n                                <Col md={2}>\n                                    <Form.Group>\n                                        <Form.Label>{t('start_date')}</Form.Label>\n                                        <Form.Control\n                                            type=\"date\"\n                                            value={startDate}\n                                            onChange={(e) => setStartDate(e.target.value)}\n                                        />\n                                    </Form.Group>\n                                </Col>\n                                <Col md={2}>\n                                    <Form.Group>\n                                        <Form.Label>{t('end_date')}</Form.Label>\n                                        <Form.Control\n                                            type=\"date\"\n                                            value={endDate}\n                                            onChange={(e) => setEndDate(e.target.value)}\n                                        />\n                                    </Form.Group>\n                                </Col>\n                                <Col md={1}>\n                                    <Button \n                                        variant=\"outline-primary\" \n                                        onClick={handleSearch}\n                                        className=\"mb-2\"\n                                    >\n                                        <FaSearch />\n                                    </Button>\n                                </Col>\n                            </Row>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            {/* Members Table */}\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('username')}</th>\n                                        <th>{t('real_name')}</th>\n                                        <th>{t('id_number')}</th>\n                                        <th>{t('id_front_image')}</th>\n                                        <th>{t('id_back_image')}</th>\n                                        <th>{t('agent_name')}</th>\n                                        <th>{t('status')}</th>\n                                        <th>{t('registration_time')}</th>\n                                        <th>{t('actions')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {filteredMembers.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"9\" className=\"text-center\">{t('no_members_found')}</td>\n                                        </tr>\n                                    ) : (\n                                        filteredMembers.map(member => (\n                                            <tr key={member.user_id}>\n                                                <td>{member.users?.email || '-'}</td>\n                                                <td>{member.real_name || '-'}</td>\n                                                <td>{member.id_number || '-'}</td>\n                                                <td>\n                                                    {member.id_img_front ? (\n                                                        <Badge bg=\"success\">{t('uploaded')}</Badge>\n                                                    ) : (\n                                                        <Badge bg=\"secondary\">{t('not_uploaded')}</Badge>\n                                                    )}\n                                                </td>\n                                                <td>\n                                                    {member.id_img_back ? (\n                                                        <Badge bg=\"success\">{t('uploaded')}</Badge>\n                                                    ) : (\n                                                        <Badge bg=\"secondary\">{t('not_uploaded')}</Badge>\n                                                    )}\n                                                </td>\n                                                <td>\n                                                    <div>\n                                                        <div>{member.agent_info?.brand_name || '-'}</div>\n                                                        <small className=\"text-muted\">\n                                                            {member.agent_info?.email || '-'}\n                                                        </small>\n                                                    </div>\n                                                </td>\n                                                <td>{getStatusBadge(member.verify_status)}</td>\n                                                <td>{member.users?.created_at ? new Date(member.users.created_at).toLocaleString() : '-'}</td>\n                                                <td>\n                                                    <div className=\"d-flex gap-1\">\n                                                        <Button\n                                                            size=\"sm\"\n                                                            variant=\"outline-primary\"\n                                                            onClick={() => handleKycReview(member.user_id)}\n                                                            title={t('kyc_review')}\n                                                        >\n                                                            <FaUserCheck />\n                                                        </Button>\n                                                        <Button\n                                                            size=\"sm\"\n                                                            variant=\"outline-warning\"\n                                                            onClick={() => handleChangeAgent(member.user_id)}\n                                                            title={t('change_agent')}\n                                                        >\n                                                            <FaExchangeAlt />\n                                                        </Button>\n                                                        <Button\n                                                            size=\"sm\"\n                                                            variant=\"outline-info\"\n                                                            onClick={() => handleViewDetails(member.user_id)}\n                                                            title={t('view_details')}\n                                                        >\n                                                            <FaEye />\n                                                        </Button>\n                                                    </div>\n                                                </td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default Members;\n"], "names": ["Row", "React", "_ref", "ref", "bsPrefix", "className", "as", "Component", "props", "decoratedBsPrefix", "useBootstrapPrefix", "breakpoints", "useBootstrapBreakpoints", "minBreakpoint", "useBootstrapMinBreakpoint", "sizePrefix", "classes", "for<PERSON>ach", "brkPoint", "propValue", "cols", "infix", "push", "_jsx", "classNames", "displayName", "Badge", "bg", "pill", "text", "prefix", "Table", "striped", "bordered", "borderless", "hover", "size", "variant", "responsive", "table", "responsiveClass", "children", "InputGroupText", "InputGroup", "hasValidation", "contextValue", "useMemo", "InputGroupContext", "Provider", "value", "Object", "assign", "Text", "Radio", "FormCheckInput", "type", "Checkbox", "Members", "t", "useTranslation", "members", "setMembers", "useState", "loading", "setLoading", "searchTerm", "setSearchTerm", "statusFilter", "setStatus<PERSON>ilter", "startDate", "setStartDate", "endDate", "setEndDate", "filteredMembers", "setFilteredMembers", "useEffect", "async", "supabase", "getSupabase", "data", "user", "auth", "getUser", "customers", "error", "profileError", "from", "select", "eq", "id", "order", "ascending", "console", "map", "c", "user_id", "filter", "Boolean", "userInfoList", "userError", "usersMap", "Map", "u", "enrichedMembers", "users", "get", "fetchMembers", "filtered", "member", "_member$users", "_member$users$email", "_member$real_name", "email", "toLowerCase", "includes", "real_name", "verify_status", "_member$users2", "Date", "created_at", "_member$users3", "getStatusBadge", "status", "_jsxs", "Container", "Col", "Card", "Body", "md", "<PERSON><PERSON>", "onClick", "handleAddMember", "alert", "FaPlus", "Form", "Group", "Label", "Control", "placeholder", "onChange", "e", "target", "Select", "handleSearch", "log", "FaSearch", "length", "colSpan", "_member$users4", "_member$agent_info", "_member$agent_info2", "_member$users5", "id_number", "id_img_front", "id_img_back", "agent_info", "brand_name", "toLocaleString", "title", "FaUserCheck", "FaExchangeAlt", "FaEye"], "sourceRoot": ""}