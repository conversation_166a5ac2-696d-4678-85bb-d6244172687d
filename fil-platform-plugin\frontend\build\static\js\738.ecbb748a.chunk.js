"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[738],{1719:(e,t,a)=>{a.d(t,{A:()=>y});var s=a(8139),r=a.n(s),i=a(5043),n=a(1969),l=a(6618),o=a(7852),d=a(4488),c=a(579);const u=(0,d.A)("h4");u.displayName="DivStyledAsH4";const m=i.forwardRef((e,t)=>{let{className:a,bsPrefix:s,as:i=u,...n}=e;return s=(0,o.oU)(s,"alert-heading"),(0,c.jsx)(i,{ref:t,className:r()(a,s),...n})});m.displayName="AlertHeading";const p=m;var f=a(7071);const g=i.forwardRef((e,t)=>{let{className:a,bsPrefix:s,as:i=f.A,...n}=e;return s=(0,o.oU)(s,"alert-link"),(0,c.jsx)(i,{ref:t,className:r()(a,s),...n})});g.displayName="AlertLink";const _=g;var b=a(8072),h=a(5632);const x=i.forwardRef((e,t)=>{const{bsPrefix:a,show:s=!0,closeLabel:i="Close alert",closeVariant:d,className:u,children:m,variant:p="primary",onClose:f,dismissible:g,transition:_=b.A,...x}=(0,n.Zw)(e,{show:"onClose"}),y=(0,o.oU)(a,"alert"),v=(0,l.A)(e=>{f&&f(!1,e)}),j=!0===_?b.A:_,A=(0,c.jsxs)("div",{role:"alert",...j?void 0:x,ref:t,className:r()(u,y,p&&`${y}-${p}`,g&&`${y}-dismissible`),children:[g&&(0,c.jsx)(h.A,{onClick:v,"aria-label":i,variant:d}),m]});return j?(0,c.jsx)(j,{unmountOnExit:!0,...x,ref:void 0,in:s,children:A}):s?A:null});x.displayName="Alert";const y=Object.assign(x,{Link:_,Heading:p})},2738:(e,t,a)=>{a.r(t),a.d(t,{default:()=>m});var s=a(5043),r=a(3519),i=a(8628),n=a(1719),l=a(3722),o=a(4282),d=a(4312),c=a(4117),u=a(579);const m=()=>{var e,t;const{t:a}=(0,c.Bd)(),[m,p]=(0,s.useState)(""),[f,g]=(0,s.useState)(""),[_,b]=(0,s.useState)(null),[h,x]=(0,s.useState)(null),[y,v]=(0,s.useState)("pending"),[j,A]=(0,s.useState)(!0),[k,w]=(0,s.useState)(!1),[C,N]=(0,s.useState)({type:"",text:""});(0,s.useEffect)(()=>{(async()=>{const e=(0,d.b)();if(!e)return;const{data:{user:t}}=await e.auth.getUser();if(!t)return void A(!1);const{data:s,error:r}=await e.from("customer_profiles").select("real_name, id_number, id_img_front, id_img_back, verify_status").eq("user_id",t.id).single();r&&"PGRST116"!==r.code?(console.error("Error fetching KYC status:",r),N({type:"danger",text:a("failed_to_load_kyc_status")})):s&&(p(s.real_name||""),g(s.id_number||""),b(s.id_img_front||null),x(s.id_img_back||null),v(s.verify_status||"pending")),A(!1)})()},[]);const S=(e,t)=>{e.target.files&&e.target.files[0]&&t(e.target.files[0])};return j?(0,u.jsx)("div",{children:a("loading_kyc_status")}):(0,u.jsxs)(r.A,{children:[(0,u.jsx)("h2",{className:"mb-4",children:a("kyc_verification")}),(0,u.jsx)(i.A,{children:(0,u.jsxs)(i.A.Body,{children:[C.text&&(0,u.jsx)(n.A,{variant:C.type,children:C.text}),"approved"===y&&(0,u.jsx)(n.A,{variant:"success",children:a("kyc_approved")}),"pending"===y&&(0,u.jsx)(n.A,{variant:"info",children:a("kyc_pending_review")}),"rejected"===y&&(0,u.jsx)(n.A,{variant:"danger",children:a("kyc_rejected")}),(0,u.jsxs)(l.A,{onSubmit:async e=>{e.preventDefault(),w(!0),N({type:"",text:""});const t=(0,d.b)();if(!t)return;const{data:{user:s}}=await t.auth.getUser();if(!s)return N({type:"danger",text:a("user_not_logged_in")}),void w(!1);try{let e=_,r=h;if(_ instanceof File){const{data:a,error:r}=await t.storage.from("kyc-documents").upload(`${s.id}/front_${Date.now()}`,_,{cacheControl:"3600",upsert:!1});if(r)throw r;e=a.path}if(h instanceof File){const{data:e,error:a}=await t.storage.from("kyc-documents").upload(`${s.id}/back_${Date.now()}`,h,{cacheControl:"3600",upsert:!1});if(a)throw a;r=e.path}const{error:i}=await t.from("customer_profiles").upsert({user_id:s.id,real_name:m,id_number:f,id_img_front:e,id_img_back:r,verify_status:"pending"},{onConflict:"user_id"});if(i)throw i;N({type:"success",text:a("kyc_submit_success")}),v("pending")}catch(r){console.error("KYC submission error:",r),N({type:"danger",text:a("failed_to_submit_kyc")+": "+r.message})}w(!1)},children:[(0,u.jsxs)(l.A.Group,{className:"mb-3",children:[(0,u.jsx)(l.A.Label,{children:a("real_name")}),(0,u.jsx)(l.A.Control,{type:"text",value:m,onChange:e=>p(e.target.value),required:!0,disabled:"pending"===y||"approved"===y})]}),(0,u.jsxs)(l.A.Group,{className:"mb-3",children:[(0,u.jsx)(l.A.Label,{children:a("id_number")}),(0,u.jsx)(l.A.Control,{type:"text",value:f,onChange:e=>g(e.target.value),required:!0,disabled:"pending"===y||"approved"===y})]}),(0,u.jsxs)(l.A.Group,{className:"mb-3",children:[(0,u.jsx)(l.A.Label,{children:a("id_front")}),(0,u.jsx)(l.A.Control,{type:"file",onChange:e=>S(e,b),accept:"image/*",disabled:"pending"===y||"approved"===y}),_&&"string"===typeof _&&(0,u.jsx)("img",{src:null===(e=(0,d.b)())||void 0===e?void 0:e.storage.from("kyc-documents").getPublicUrl(_).data.publicUrl,alt:"ID Front",className:"img-thumbnail mt-2",style:{maxWidth:"200px"}})]}),(0,u.jsxs)(l.A.Group,{className:"mb-3",children:[(0,u.jsx)(l.A.Label,{children:a("id_back")}),(0,u.jsx)(l.A.Control,{type:"file",onChange:e=>S(e,x),accept:"image/*",disabled:"pending"===y||"approved"===y}),h&&"string"===typeof h&&(0,u.jsx)("img",{src:null===(t=(0,d.b)())||void 0===t?void 0:t.storage.from("kyc-documents").getPublicUrl(h).data.publicUrl,alt:"ID Back",className:"img-thumbnail mt-2",style:{maxWidth:"200px"}})]}),(0,u.jsx)(o.A,{variant:"primary",type:"submit",disabled:k||"pending"===y||"approved"===y,children:a(k?"submitting":"submit_review")})]})]})})]})}}}]);
//# sourceMappingURL=738.ecbb748a.chunk.js.map