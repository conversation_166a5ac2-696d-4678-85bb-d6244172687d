import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const MakerOrderListPage = () => {
    const { t } = useTranslation();
    const [orders, setOrders] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchOrders = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // Fetch orders associated with products from this maker
            const { data, error } = await supabase
                .from('orders')
                .select(`
                    id,
                    shares,
                    storage_cost,
                    pledge_cost,
                    total_rate,
                    start_at,
                    end_at,
                    review_status,
                    created_at,
                    products ( name, maker_id ),
                    customer_profiles ( real_name ),
                    agent_profiles ( brand_name )
                `)
                .filter('products.maker_id', 'eq', user.id) // Filter by maker_id from products table
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Error fetching orders:', error);
            } else {
                setOrders(data);
            }
            setLoading(false);
        };

        fetchOrders();
    }, []);

    if (loading) {
        return <div>{t('loading_orders_text')}</div>;
    }

    return (
        <Container>
                <h2 className="mb-4">{t('all_orders')}</h2>
                <Row>
                    <Col>
                        <Card>
                            <Card.Body>
                                <Table striped bordered hover responsive>
                                    <thead>
                                        <tr>
                                            <th>{t('order_id')}</th>
                                            <th>{t('product_name')}</th>
                                            <th>{t('customer')}</th>
                                            <th>{t('agent')}</th>
                                            <th>{t('shares')}</th>
                                            <th>{t('storage_cost')}</th>
                                            <th>{t('pledge_cost')}</th>
                                            <th>{t('status')}</th>
                                            <th>{t('created_at')}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {orders.length === 0 ? (
                                            <tr>
                                                <td colSpan="9" className="text-center">{t('no_orders_available')}</td>
                                            </tr>
                                        ) : (
                                            orders.map(order => (
                                                <tr key={order.id}>
                                                    <td>{order.id.substring(0, 8)}...</td>
                                                    <td>{order.products?.name || 'N/A'}</td>
                                                    <td>{order.customer_profiles?.real_name || 'N/A'}</td>
                                                    <td>{order.agent_profiles?.brand_name || 'N/A'}</td>
                                                    <td>{order.shares}</td>
                                                    <td>{order.storage_cost}</td>
                                                    <td>{order.pledge_cost}</td>
                                                    <td><Badge bg={order.review_status === 'approved' ? 'success' : 'warning'}>{order.review_status}</Badge></td>
                                                    <td>{new Date(order.created_at).toLocaleString()}</td>
                                                </tr>
                                            ))
                                        )}
                                    </tbody>
                                </Table>
                            </Card.Body>
                        </Card>
                    </Col>
                </Row>
        </Container>
    );
};

export default MakerOrderListPage;
