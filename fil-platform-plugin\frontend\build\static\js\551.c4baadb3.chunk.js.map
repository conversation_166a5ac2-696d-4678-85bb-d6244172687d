{"version": 3, "file": "static/js/551.c4baadb3.chunk.js", "mappings": "wKAMA,MAAMA,EAAmBC,EAAAA,WAAiB,CAAAC,EAMvCC,KAAQ,IANgC,SACzCC,EAAQ,UACRC,EAEAC,GAAIC,EAAY,SACbC,GACJN,EACC,MAAMO,GAAoBC,EAAAA,EAAAA,IAAmBN,EAAU,OACjDO,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChBC,EAAa,GAAGN,SAChBO,EAAU,GAehB,OAdAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAIE,SADGZ,EAAMU,GAEI,MAAbC,GAA0C,kBAAdA,IAE5BC,QACED,GAEJC,EAAOD,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GAChD,MAARE,GAAcJ,EAAQM,KAAK,GAAGP,IAAaM,KAASD,QAEtCG,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWI,KAAsBO,OAG3DhB,EAAIyB,YAAc,MAClB,S,sJClCA,MA6JA,EA7JuBC,KACnB,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,KAClCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAEvCG,EAAAA,EAAAA,WAAU,KACgBC,WAClB,MAAMC,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EAAU,OAEfH,GAAW,GACX,MAAQK,MAAM,KAAEC,UAAiBH,EAASI,KAAKC,UAE/C,IAAKF,EAED,YADAN,GAAW,GAKf,MAAM,KAAEK,EAAI,MAAEI,SAAgBN,EACzBO,KAAK,mBACLC,OAAO,otBAwBPC,MAAM,aAAc,CAAEC,WAAW,IAElCJ,EACAK,QAAQL,MAAM,kCAAmCA,GAEjDZ,EAAYQ,GAEhBL,GAAW,IAGfe,IACD,IAEH,MAAMC,EAAuBC,IACzB,OAAQA,GACJ,IAAK,UACD,OAAO3B,EAAAA,EAAAA,KAAC4B,EAAAA,EAAK,CAACC,GAAG,UAASC,SAAE1B,EAAE,aAClC,IAAK,aACD,OAAOJ,EAAAA,EAAAA,KAAC4B,EAAAA,EAAK,CAACC,GAAG,SAAQC,SAAE1B,EAAE,gBACjC,IAAK,aACD,OAAOJ,EAAAA,EAAAA,KAAC4B,EAAAA,EAAK,CAACC,GAAG,UAASC,SAAE1B,EAAE,gBAClC,IAAK,QACD,OAAOJ,EAAAA,EAAAA,KAAC4B,EAAAA,EAAK,CAACC,GAAG,OAAMC,SAAE1B,EAAE,WAC/B,IAAK,UACD,OAAOJ,EAAAA,EAAAA,KAAC4B,EAAAA,EAAK,CAACC,GAAG,OAAMC,SAAE1B,EAAE,aAC/B,QACI,OAAOJ,EAAAA,EAAAA,KAAC4B,EAAAA,EAAK,CAACC,GAAG,YAAWC,SAAEH,GAAQ,QASlD,OAAIlB,GACOT,EAAAA,EAAAA,KAAA,OAAA8B,SAAM1B,EAAE,8BAIf2B,EAAAA,EAAAA,MAACC,EAAAA,EAAS,CAAAF,SAAA,EACN9B,EAAAA,EAAAA,KAAA,MAAIlB,UAAU,OAAMgD,SAAE1B,EAAE,qBACxBJ,EAAAA,EAAAA,KAACvB,EAAAA,EAAG,CAAAqD,UACA9B,EAAAA,EAAAA,KAACiC,EAAAA,EAAG,CAAAH,UACA9B,EAAAA,EAAAA,KAACkC,EAAAA,EAAI,CAAAJ,UACD9B,EAAAA,EAAAA,KAACkC,EAAAA,EAAKC,KAAI,CAAAL,UACNC,EAAAA,EAAAA,MAACK,EAAAA,EAAK,CAACC,SAAO,EAACC,UAAQ,EAACC,OAAK,EAACC,YAAU,EAAAV,SAAA,EACpC9B,EAAAA,EAAAA,KAAA,SAAA8B,UACIC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACI9B,EAAAA,EAAAA,KAAA,MAAA8B,SAAK1B,EAAE,iBACPJ,EAAAA,EAAAA,KAAA,MAAA8B,SAAK1B,EAAE,YACPJ,EAAAA,EAAAA,KAAA,MAAA8B,SAAK1B,EAAE,eACPJ,EAAAA,EAAAA,KAAA,MAAA8B,SAAK1B,EAAE,oBACPJ,EAAAA,EAAAA,KAAA,MAAA8B,SAAK1B,EAAE,aACPJ,EAAAA,EAAAA,KAAA,MAAA8B,SAAK1B,EAAE,mBACPJ,EAAAA,EAAAA,KAAA,MAAA8B,SAAK1B,EAAE,aACPJ,EAAAA,EAAAA,KAAA,MAAA8B,SAAK1B,EAAE,sBAGfJ,EAAAA,EAAAA,KAAA,SAAA8B,SACyB,IAApBxB,EAASmC,QACNzC,EAAAA,EAAAA,KAAA,MAAA8B,UACI9B,EAAAA,EAAAA,KAAA,MAAI0C,QAAQ,IAAI5D,UAAU,cAAagD,SAAE1B,EAAE,oCAG/CE,EAASqC,IAAIC,IAAO,IAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAnClCC,EAmCkC,OAChBrB,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACI9B,EAAAA,EAAAA,KAAA,MAAA8B,SAAKc,EAAQS,MACbrD,EAAAA,EAAAA,KAAA,MAAA8B,UACIC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACI9B,EAAAA,EAAAA,KAAA,OAAA8B,UAA4B,QAAtBe,EAAAD,EAAQU,sBAAc,IAAAT,OAAA,EAAtBA,EAAwBU,SAAU,OACxCvD,EAAAA,EAAAA,KAAA,SAAOlB,UAAU,aAAYgD,UACF,QAAtBgB,EAAAF,EAAQU,sBAAc,IAAAR,GAAO,QAAPC,EAAtBD,EAAwBU,aAAK,IAAAT,OAAP,EAAtBA,EAA+BU,QAAS,YAIrDzD,EAAAA,EAAAA,KAAA,MAAA8B,UACIC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACI9B,EAAAA,EAAAA,KAAA,OAAA8B,UAA+B,QAAzBkB,EAAAJ,EAAQc,yBAAiB,IAAAV,OAAA,EAAzBA,EAA2BW,YAAa,OAC9C3D,EAAAA,EAAAA,KAAA,SAAOlB,UAAU,aAAYgD,UACC,QAAzBmB,EAAAL,EAAQc,yBAAiB,IAAAT,GAAO,QAAPC,EAAzBD,EAA2BO,aAAK,IAAAN,OAAP,EAAzBA,EAAkCO,QAAS,YAIxDzD,EAAAA,EAAAA,KAAA,MAAA8B,UACI9B,EAAAA,EAAAA,KAAC4B,EAAAA,EAAK,CAACC,GAAG,UAASC,UACI,QAAlBqB,EAAAP,EAAQgB,kBAAU,IAAAT,OAAA,EAAlBA,EAAoBU,OAAQ,SAGrC9B,EAAAA,EAAAA,MAAA,MAAIjD,UAAW8D,EAAQQ,QAAU,EAAI,eAAiB,cAActB,SAAA,CAC/Dc,EAAQQ,QAAU,EAAI,IAAM,IA5DvDA,EA4DwER,EAAQQ,OA3D7FA,EACEU,WAAWV,GAAQW,QAAQ,GADd,gBA6DoB/D,EAAAA,EAAAA,KAAA,MAAA8B,SAAKJ,EAAoBkB,EAAQoB,iBACjChE,EAAAA,EAAAA,KAAA,MAAA8B,UACI9B,EAAAA,EAAAA,KAAA,OAAKiE,MAAO,CAAEC,SAAU,QAASC,SAAU,cAAerC,SACrDc,EAAQwB,QAAU,SAG3BpE,EAAAA,EAAAA,KAAA,MAAA8B,SAAK,IAAIuC,KAAKzB,EAAQ0B,YAAYC,qBAhC7B3B,EAAQS,sB,sFC9G7D,MAAMzB,EAAqBlD,EAAAA,WAAiB,CAAAC,EAQzCC,KAAQ,IARkC,SAC3CC,EAAQ,GACRgD,EAAK,UAAS,KACd2C,GAAO,EAAK,KACZC,EAAI,UACJ3F,EACAC,GAAIC,EAAY,UACbC,GACJN,EACC,MAAM+F,GAASvF,EAAAA,EAAAA,IAAmBN,EAAU,SAC5C,OAAoBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAW4F,EAAQF,GAAQ,eAAgBC,GAAQ,QAAQA,IAAQ5C,GAAM,MAAMA,SAGzGD,EAAM1B,YAAc,QACpB,S,sFCjBA,MAAMkC,EAAqB1D,EAAAA,WAAiB,CAAAC,EAWzCC,KAAQ,IAXkC,SAC3CC,EAAQ,UACRC,EAAS,QACTuD,EAAO,SACPC,EAAQ,WACRqC,EAAU,MACVpC,EAAK,KACLqC,EAAI,QACJC,EAAO,WACPrC,KACGvD,GACJN,EACC,MAAMO,GAAoBC,EAAAA,EAAAA,IAAmBN,EAAU,SACjDY,EAAUQ,IAAWnB,EAAWI,EAAmB2F,GAAW,GAAG3F,KAAqB2F,IAAWD,GAAQ,GAAG1F,KAAqB0F,IAAQvC,GAAW,GAAGnD,KAAwC,kBAAZmD,EAAuB,WAAWA,IAAY,YAAaC,GAAY,GAAGpD,aAA8ByF,GAAc,GAAGzF,eAAgCqD,GAAS,GAAGrD,WACxV4F,GAAqB9E,EAAAA,EAAAA,KAAK,QAAS,IACpCf,EACHH,UAAWW,EACXb,IAAKA,IAEP,GAAI4D,EAAY,CACd,IAAIuC,EAAkB,GAAG7F,eAIzB,MAH0B,kBAAfsD,IACTuC,EAAkB,GAAGA,KAAmBvC,MAEtBxC,EAAAA,EAAAA,KAAK,MAAO,CAC9BlB,UAAWiG,EACXjD,SAAUgD,GAEd,CACA,OAAOA,IAET1C,EAAMlC,YAAc,QACpB,S,sFCQA,MAAM+B,EAAmBvD,EAAAA,WAEzB,CAACO,EAAOL,KACN,OAAO,UACLE,KACGkG,IAEHjG,GAAIC,EAAY,MAAK,SACrBH,EAAQ,MACRoG,IAjDG,SAAetG,GAKnB,IALoB,GACrBI,EAAE,SACFF,EAAQ,UACRC,KACGG,GACJN,EACCE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,OACxC,MAAMO,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChB0F,EAAQ,GACRxF,EAAU,GAqBhB,OApBAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAIuF,EACAC,EACA7D,SAHGrC,EAAMU,GAIY,kBAAdC,GAAuC,MAAbA,IAEjCsF,OACAC,SACA7D,SACE1B,GAEJsF,EAAOtF,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GACxDuF,GAAMD,EAAMlF,MAAc,IAATmF,EAAgB,GAAGrG,IAAWiB,IAAU,GAAGjB,IAAWiB,KAASoF,KACvE,MAAT5D,GAAe7B,EAAQM,KAAK,QAAQD,KAASwB,KACnC,MAAV6D,GAAgB1F,EAAQM,KAAK,SAASD,KAASqF,OAE9C,CAAC,IACHlG,EACHH,UAAWmB,IAAWnB,KAAcmG,KAAUxF,IAC7C,CACDV,KACAF,WACAoG,SAEJ,CAWOG,CAAOnG,GACZ,OAAoBe,EAAAA,EAAAA,KAAKhB,EAAW,IAC/BgG,EACHpG,IAAKA,EACLE,UAAWmB,IAAWnB,GAAYmG,EAAMxC,QAAU5D,OAGtDoD,EAAI/B,YAAc,MAClB,S,sFC1DA,MAAMmF,EAAwB3G,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPoG,EAASnF,YAAc,WACvB,UCdMoF,EAA0B5G,EAAAA,WAAiB,CAAAC,EAK9CC,KAAQ,IALuC,UAChDE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,gBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPqG,EAAWpF,YAAc,aACzB,U,cCZA,MAAMqF,EAA0B7G,EAAAA,WAAiB,CAAAC,EAM9CC,KAAQ,IANuC,SAChDC,EAAQ,UACRC,EAEAC,GAAIC,EAAY,SACbC,GACJN,EACC,MAAM+F,GAASvF,EAAAA,EAAAA,IAAmBN,EAAU,eACtC2G,GAAeC,EAAAA,EAAAA,SAAQ,KAAM,CACjCC,mBAAoBhB,IAClB,CAACA,IACL,OAAoB1E,EAAAA,EAAAA,KAAK2F,EAAAA,EAAkBC,SAAU,CACnDC,MAAOL,EACP1D,UAAuB9B,EAAAA,EAAAA,KAAKhB,EAAW,CACrCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAW4F,SAIvCa,EAAWrF,YAAc,aACzB,UCvBM4F,EAAuBpH,EAAAA,WAE7B,CAAAC,EAMGC,KAAQ,IANV,SACCC,EAAQ,UACRC,EAAS,QACT+F,EACA9F,GAAIC,EAAY,SACbC,GACJN,EACC,MAAM+F,GAASvF,EAAAA,EAAAA,IAAmBN,EAAU,YAC5C,OAAoBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAW4E,EAAU,GAAGH,KAAUG,IAAYH,EAAQ5F,MAC9DG,MAGP6G,EAAQ5F,YAAc,UACtB,UCjBM6F,EAA8BrH,EAAAA,WAAiB,CAAAC,EAKlDC,KAAQ,IAL2C,UACpDE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,qBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGP8G,EAAe7F,YAAc,iBAC7B,UCdM8F,EAAwBtH,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,OACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGP+G,EAAS9F,YAAc,WACvB,U,cCbA,MAAM+F,GAAgBC,EAAAA,EAAAA,GAAiB,MACjCC,EAA4BzH,EAAAA,WAAiB,CAAAC,EAKhDC,KAAQ,IALyC,UAClDE,EAAS,SACTD,EACAE,GAAIC,EAAYiH,KACbhH,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,kBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPkH,EAAajG,YAAc,eAC3B,UChBMkG,EAAwB1H,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,OACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPmH,EAASlG,YAAc,WACvB,UCbMmG,GAAgBH,EAAAA,EAAAA,GAAiB,MACjCI,EAAyB5H,EAAAA,WAAiB,CAAAC,EAK7CC,KAAQ,IALsC,UAC/CE,EAAS,SACTD,EACAE,GAAIC,EAAYqH,KACbpH,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,eACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPqH,EAAUpG,YAAc,YACxB,UCPMgC,EAAoBxD,EAAAA,WAAiB,CAAAC,EAWxCC,KAAQ,IAXiC,SAC1CC,EAAQ,UACRC,EAAS,GACT+C,EAAE,KACF4C,EAAI,OACJ8B,EAAM,KACNC,GAAO,EAAK,SACZ1E,EAEA/C,GAAIC,EAAY,SACbC,GACJN,EACC,MAAM+F,GAASvF,EAAAA,EAAAA,IAAmBN,EAAU,QAC5C,OAAoBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAW4F,EAAQ7C,GAAM,MAAMA,IAAM4C,GAAQ,QAAQA,IAAQ8B,GAAU,UAAUA,KACvGzE,SAAU0E,GAAoBxG,EAAAA,EAAAA,KAAKqF,EAAU,CAC3CvD,SAAUA,IACPA,MAGTI,EAAKhC,YAAc,OACnB,QAAeuG,OAAOC,OAAOxE,EAAM,CACjCyE,IAAKb,EACLc,MAAON,EACPO,SAAUV,EACVhE,KAAMkD,EACNyB,KAAMd,EACNe,KAAMX,EACNY,OAAQzB,EACR0B,OAAQ3B,EACR4B,WAAYnB,G", "sources": ["../node_modules/react-bootstrap/esm/Row.js", "pages/maker/ManualDeposits.js", "../node_modules/react-bootstrap/esm/Badge.js", "../node_modules/react-bootstrap/esm/Table.js", "../node_modules/react-bootstrap/esm/Col.js", "../node_modules/react-bootstrap/esm/CardBody.js", "../node_modules/react-bootstrap/esm/CardFooter.js", "../node_modules/react-bootstrap/esm/CardHeader.js", "../node_modules/react-bootstrap/esm/CardImg.js", "../node_modules/react-bootstrap/esm/CardImgOverlay.js", "../node_modules/react-bootstrap/esm/CardLink.js", "../node_modules/react-bootstrap/esm/CardSubtitle.js", "../node_modules/react-bootstrap/esm/CardText.js", "../node_modules/react-bootstrap/esm/CardTitle.js", "../node_modules/react-bootstrap/esm/Card.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Row = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'row');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const sizePrefix = `${decoratedBsPrefix}-cols`;\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let cols;\n    if (propValue != null && typeof propValue === 'object') {\n      ({\n        cols\n      } = propValue);\n    } else {\n      cols = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (cols != null) classes.push(`${sizePrefix}${infix}-${cols}`);\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, decoratedBsPrefix, ...classes)\n  });\n});\nRow.displayName = 'Row';\nexport default Row;", "import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst ManualDeposits = () => {\n    const { t } = useTranslation();\n    const [journals, setJournals] = useState([]);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        const fetchJournals = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setLoading(false);\n                return; // User not logged in\n            }\n\n            // Fetch manual journals with maker, customer and currency information\n            const { data, error } = await supabase\n                .from('manual_journals')\n                .select(`\n                    id,\n                    amount,\n                    journal_type,\n                    remark,\n                    created_at,\n                    maker_profiles (\n                        domain,\n                        users (\n                            email\n                        )\n                    ),\n                    customer_profiles (\n                        real_name,\n                        users (\n                            email\n                        )\n                    ),\n                    currencies (\n                        code,\n                        total_supply,\n                        withdrawable\n                    )\n                `)\n                .order('created_at', { ascending: false });\n\n            if (error) {\n                console.error('Error fetching manual journals:', error);\n            } else {\n                setJournals(data);\n            }\n            setLoading(false);\n        };\n\n        fetchJournals();\n    }, []);\n\n    const getJournalTypeBadge = (type) => {\n        switch (type) {\n            case 'deposit':\n                return <Badge bg=\"success\">{t('deposit')}</Badge>;\n            case 'withdrawal':\n                return <Badge bg=\"danger\">{t('withdrawal')}</Badge>;\n            case 'adjustment':\n                return <Badge bg=\"warning\">{t('adjustment')}</Badge>;\n            case 'bonus':\n                return <Badge bg=\"info\">{t('bonus')}</Badge>;\n            case 'penalty':\n                return <Badge bg=\"dark\">{t('penalty')}</Badge>;\n            default:\n                return <Badge bg=\"secondary\">{type || '-'}</Badge>;\n        }\n    };\n\n    const formatAmount = (amount) => {\n        if (!amount) return '0.000000';\n        return parseFloat(amount).toFixed(6);\n    };\n\n    if (loading) {\n        return <div>{t('loading_manual_deposits')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('manual_deposit')}</h2>\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('journal_id')}</th>\n                                        <th>{t('maker')}</th>\n                                        <th>{t('customer')}</th>\n                                        <th>{t('currency_code')}</th>\n                                        <th>{t('amount')}</th>\n                                        <th>{t('journal_type')}</th>\n                                        <th>{t('remark')}</th>\n                                        <th>{t('created_at')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {journals.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"8\" className=\"text-center\">{t('no_manual_deposits_available')}</td>\n                                        </tr>\n                                    ) : (\n                                        journals.map(journal => (\n                                            <tr key={journal.id}>\n                                                <td>{journal.id}</td>\n                                                <td>\n                                                    <div>\n                                                        <div>{journal.maker_profiles?.domain || '-'}</div>\n                                                        <small className=\"text-muted\">\n                                                            {journal.maker_profiles?.users?.email || '-'}\n                                                        </small>\n                                                    </div>\n                                                </td>\n                                                <td>\n                                                    <div>\n                                                        <div>{journal.customer_profiles?.real_name || '-'}</div>\n                                                        <small className=\"text-muted\">\n                                                            {journal.customer_profiles?.users?.email || '-'}\n                                                        </small>\n                                                    </div>\n                                                </td>\n                                                <td>\n                                                    <Badge bg=\"primary\">\n                                                        {journal.currencies?.code || '-'}\n                                                    </Badge>\n                                                </td>\n                                                <td className={journal.amount >= 0 ? 'text-success' : 'text-danger'}>\n                                                    {journal.amount >= 0 ? '+' : ''}{formatAmount(journal.amount)}\n                                                </td>\n                                                <td>{getJournalTypeBadge(journal.journal_type)}</td>\n                                                <td>\n                                                    <div style={{ maxWidth: '200px', wordWrap: 'break-word' }}>\n                                                        {journal.remark || '-'}\n                                                    </div>\n                                                </td>\n                                                <td>{new Date(journal.created_at).toLocaleString()}</td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default ManualDeposits;\n", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Badge = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  bg = 'primary',\n  pill = false,\n  text,\n  className,\n  as: Component = 'span',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'badge');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, pill && `rounded-pill`, text && `text-${text}`, bg && `bg-${bg}`)\n  });\n});\nBadge.displayName = 'Badge';\nexport default Badge;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Table = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  striped,\n  bordered,\n  borderless,\n  hover,\n  size,\n  variant,\n  responsive,\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'table');\n  const classes = classNames(className, decoratedBsPrefix, variant && `${decoratedBsPrefix}-${variant}`, size && `${decoratedBsPrefix}-${size}`, striped && `${decoratedBsPrefix}-${typeof striped === 'string' ? `striped-${striped}` : 'striped'}`, bordered && `${decoratedBsPrefix}-bordered`, borderless && `${decoratedBsPrefix}-borderless`, hover && `${decoratedBsPrefix}-hover`);\n  const table = /*#__PURE__*/_jsx(\"table\", {\n    ...props,\n    className: classes,\n    ref: ref\n  });\n  if (responsive) {\n    let responsiveClass = `${decoratedBsPrefix}-responsive`;\n    if (typeof responsive === 'string') {\n      responsiveClass = `${responsiveClass}-${responsive}`;\n    }\n    return /*#__PURE__*/_jsx(\"div\", {\n      className: responsiveClass,\n      children: table\n    });\n  }\n  return table;\n});\nTable.displayName = 'Table';\nexport default Table;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function useCol({\n  as,\n  bsPrefix,\n  className,\n  ...props\n}) {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'col');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const spans = [];\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let span;\n    let offset;\n    let order;\n    if (typeof propValue === 'object' && propValue != null) {\n      ({\n        span,\n        offset,\n        order\n      } = propValue);\n    } else {\n      span = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (span) spans.push(span === true ? `${bsPrefix}${infix}` : `${bsPrefix}${infix}-${span}`);\n    if (order != null) classes.push(`order${infix}-${order}`);\n    if (offset != null) classes.push(`offset${infix}-${offset}`);\n  });\n  return [{\n    ...props,\n    className: classNames(className, ...spans, ...classes)\n  }, {\n    as,\n    bsPrefix,\n    spans\n  }];\n}\nconst Col = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n(props, ref) => {\n  const [{\n    className,\n    ...colProps\n  }, {\n    as: Component = 'div',\n    bsPrefix,\n    spans\n  }] = useCol(props);\n  return /*#__PURE__*/_jsx(Component, {\n    ...colProps,\n    ref: ref,\n    className: classNames(className, !spans.length && bsPrefix)\n  });\n});\nCol.displayName = 'Col';\nexport default Col;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});"], "names": ["Row", "React", "_ref", "ref", "bsPrefix", "className", "as", "Component", "props", "decoratedBsPrefix", "useBootstrapPrefix", "breakpoints", "useBootstrapBreakpoints", "minBreakpoint", "useBootstrapMinBreakpoint", "sizePrefix", "classes", "for<PERSON>ach", "brkPoint", "propValue", "cols", "infix", "push", "_jsx", "classNames", "displayName", "ManualDeposits", "t", "useTranslation", "journals", "setJournals", "useState", "loading", "setLoading", "useEffect", "async", "supabase", "getSupabase", "data", "user", "auth", "getUser", "error", "from", "select", "order", "ascending", "console", "fetchJournals", "getJournalTypeBadge", "type", "Badge", "bg", "children", "_jsxs", "Container", "Col", "Card", "Body", "Table", "striped", "bordered", "hover", "responsive", "length", "colSpan", "map", "journal", "_journal$maker_profil", "_journal$maker_profil2", "_journal$maker_profil3", "_journal$customer_pro", "_journal$customer_pro2", "_journal$customer_pro3", "_journal$currencies", "amount", "id", "maker_profiles", "domain", "users", "email", "customer_profiles", "real_name", "currencies", "code", "parseFloat", "toFixed", "journal_type", "style", "max<PERSON><PERSON><PERSON>", "wordWrap", "remark", "Date", "created_at", "toLocaleString", "pill", "text", "prefix", "borderless", "size", "variant", "table", "responsiveClass", "colProps", "spans", "span", "offset", "useCol", "CardBody", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "value", "CardImg", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "border", "body", "Object", "assign", "Img", "Title", "Subtitle", "Link", "Text", "Header", "Footer", "ImgOverlay"], "sourceRoot": ""}