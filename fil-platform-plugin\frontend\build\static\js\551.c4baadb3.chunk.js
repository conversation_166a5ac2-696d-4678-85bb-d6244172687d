"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[551],{1072:(e,s,r)=>{r.d(s,{A:()=>o});var a=r(8139),t=r.n(a),n=r(5043),d=r(7852),l=r(579);const c=n.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:n="div",...c}=e;const o=(0,d.oU)(r,"row"),i=(0,d.gy)(),m=(0,d.Jm)(),u=`${o}-cols`,x=[];return i.forEach(e=>{const s=c[e];let r;delete c[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const a=e!==m?`-${e}`:"";null!=r&&x.push(`${u}${a}-${r}`)}),(0,l.jsx)(n,{ref:s,...c,className:t()(a,o,...x)})});c.displayName="Row";const o=c},3551:(e,s,r)=>{r.r(s),r.d(s,{default:()=>x});var a=r(5043),t=r(4063),n=r(3519),d=r(1072),l=r(8602),c=r(8628),o=r(4196),i=r(4312),m=r(4117),u=r(579);const x=()=>{const{t:e}=(0,m.Bd)(),[s,r]=(0,a.useState)([]),[x,f]=(0,a.useState)(!0);(0,a.useEffect)(()=>{(async()=>{const e=(0,i.b)();if(!e)return;f(!0);const{data:{user:s}}=await e.auth.getUser();if(!s)return void f(!1);const{data:a,error:t}=await e.from("manual_journals").select("\n                    id,\n                    amount,\n                    journal_type,\n                    remark,\n                    created_at,\n                    maker_profiles (\n                        domain,\n                        users (\n                            email\n                        )\n                    ),\n                    customer_profiles (\n                        real_name,\n                        users (\n                            email\n                        )\n                    ),\n                    currencies (\n                        code,\n                        total_supply,\n                        withdrawable\n                    )\n                ").order("created_at",{ascending:!1});t?console.error("Error fetching manual journals:",t):r(a),f(!1)})()},[]);const h=s=>{switch(s){case"deposit":return(0,u.jsx)(t.A,{bg:"success",children:e("deposit")});case"withdrawal":return(0,u.jsx)(t.A,{bg:"danger",children:e("withdrawal")});case"adjustment":return(0,u.jsx)(t.A,{bg:"warning",children:e("adjustment")});case"bonus":return(0,u.jsx)(t.A,{bg:"info",children:e("bonus")});case"penalty":return(0,u.jsx)(t.A,{bg:"dark",children:e("penalty")});default:return(0,u.jsx)(t.A,{bg:"secondary",children:s||"-"})}};return x?(0,u.jsx)("div",{children:e("loading_manual_deposits")}):(0,u.jsxs)(n.A,{children:[(0,u.jsx)("h2",{className:"mb-4",children:e("manual_deposit")}),(0,u.jsx)(d.A,{children:(0,u.jsx)(l.A,{children:(0,u.jsx)(c.A,{children:(0,u.jsx)(c.A.Body,{children:(0,u.jsxs)(o.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,u.jsx)("thead",{children:(0,u.jsxs)("tr",{children:[(0,u.jsx)("th",{children:e("journal_id")}),(0,u.jsx)("th",{children:e("maker")}),(0,u.jsx)("th",{children:e("customer")}),(0,u.jsx)("th",{children:e("currency_code")}),(0,u.jsx)("th",{children:e("amount")}),(0,u.jsx)("th",{children:e("journal_type")}),(0,u.jsx)("th",{children:e("remark")}),(0,u.jsx)("th",{children:e("created_at")})]})}),(0,u.jsx)("tbody",{children:0===s.length?(0,u.jsx)("tr",{children:(0,u.jsx)("td",{colSpan:"8",className:"text-center",children:e("no_manual_deposits_available")})}):s.map(e=>{var s,r,a,n,d,l,c,o;return(0,u.jsxs)("tr",{children:[(0,u.jsx)("td",{children:e.id}),(0,u.jsx)("td",{children:(0,u.jsxs)("div",{children:[(0,u.jsx)("div",{children:(null===(s=e.maker_profiles)||void 0===s?void 0:s.domain)||"-"}),(0,u.jsx)("small",{className:"text-muted",children:(null===(r=e.maker_profiles)||void 0===r||null===(a=r.users)||void 0===a?void 0:a.email)||"-"})]})}),(0,u.jsx)("td",{children:(0,u.jsxs)("div",{children:[(0,u.jsx)("div",{children:(null===(n=e.customer_profiles)||void 0===n?void 0:n.real_name)||"-"}),(0,u.jsx)("small",{className:"text-muted",children:(null===(d=e.customer_profiles)||void 0===d||null===(l=d.users)||void 0===l?void 0:l.email)||"-"})]})}),(0,u.jsx)("td",{children:(0,u.jsx)(t.A,{bg:"primary",children:(null===(c=e.currencies)||void 0===c?void 0:c.code)||"-"})}),(0,u.jsxs)("td",{className:e.amount>=0?"text-success":"text-danger",children:[e.amount>=0?"+":"",(o=e.amount,o?parseFloat(o).toFixed(6):"0.000000")]}),(0,u.jsx)("td",{children:h(e.journal_type)}),(0,u.jsx)("td",{children:(0,u.jsx)("div",{style:{maxWidth:"200px",wordWrap:"break-word"},children:e.remark||"-"})}),(0,u.jsx)("td",{children:new Date(e.created_at).toLocaleString()})]},e.id)})})]})})})})})]})}},4063:(e,s,r)=>{r.d(s,{A:()=>o});var a=r(8139),t=r.n(a),n=r(5043),d=r(7852),l=r(579);const c=n.forwardRef((e,s)=>{let{bsPrefix:r,bg:a="primary",pill:n=!1,text:c,className:o,as:i="span",...m}=e;const u=(0,d.oU)(r,"badge");return(0,l.jsx)(i,{ref:s,...m,className:t()(o,u,n&&"rounded-pill",c&&`text-${c}`,a&&`bg-${a}`)})});c.displayName="Badge";const o=c},4196:(e,s,r)=>{r.d(s,{A:()=>o});var a=r(8139),t=r.n(a),n=r(5043),d=r(7852),l=r(579);const c=n.forwardRef((e,s)=>{let{bsPrefix:r,className:a,striped:n,bordered:c,borderless:o,hover:i,size:m,variant:u,responsive:x,...f}=e;const h=(0,d.oU)(r,"table"),j=t()(a,h,u&&`${h}-${u}`,m&&`${h}-${m}`,n&&`${h}-${"string"===typeof n?`striped-${n}`:"striped"}`,c&&`${h}-bordered`,o&&`${h}-borderless`,i&&`${h}-hover`),p=(0,l.jsx)("table",{...f,className:j,ref:s});if(x){let e=`${h}-responsive`;return"string"===typeof x&&(e=`${e}-${x}`),(0,l.jsx)("div",{className:e,children:p})}return p});c.displayName="Table";const o=c},8602:(e,s,r)=>{r.d(s,{A:()=>o});var a=r(8139),t=r.n(a),n=r(5043),d=r(7852),l=r(579);const c=n.forwardRef((e,s)=>{const[{className:r,...a},{as:n="div",bsPrefix:c,spans:o}]=function(e){let{as:s,bsPrefix:r,className:a,...n}=e;r=(0,d.oU)(r,"col");const l=(0,d.gy)(),c=(0,d.Jm)(),o=[],i=[];return l.forEach(e=>{const s=n[e];let a,t,d;delete n[e],"object"===typeof s&&null!=s?({span:a,offset:t,order:d}=s):a=s;const l=e!==c?`-${e}`:"";a&&o.push(!0===a?`${r}${l}`:`${r}${l}-${a}`),null!=d&&i.push(`order${l}-${d}`),null!=t&&i.push(`offset${l}-${t}`)}),[{...n,className:t()(a,...o,...i)},{as:s,bsPrefix:r,spans:o}]}(e);return(0,l.jsx)(n,{...a,ref:s,className:t()(r,!o.length&&c)})});c.displayName="Col";const o=c},8628:(e,s,r)=>{r.d(s,{A:()=>C});var a=r(8139),t=r.n(a),n=r(5043),d=r(7852),l=r(579);const c=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n="div",...c}=e;return a=(0,d.oU)(a,"card-body"),(0,l.jsx)(n,{ref:s,className:t()(r,a),...c})});c.displayName="CardBody";const o=c,i=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n="div",...c}=e;return a=(0,d.oU)(a,"card-footer"),(0,l.jsx)(n,{ref:s,className:t()(r,a),...c})});i.displayName="CardFooter";const m=i;var u=r(1778);const x=n.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:c="div",...o}=e;const i=(0,d.oU)(r,"card-header"),m=(0,n.useMemo)(()=>({cardHeaderBsPrefix:i}),[i]);return(0,l.jsx)(u.A.Provider,{value:m,children:(0,l.jsx)(c,{ref:s,...o,className:t()(a,i)})})});x.displayName="CardHeader";const f=x,h=n.forwardRef((e,s)=>{let{bsPrefix:r,className:a,variant:n,as:c="img",...o}=e;const i=(0,d.oU)(r,"card-img");return(0,l.jsx)(c,{ref:s,className:t()(n?`${i}-${n}`:i,a),...o})});h.displayName="CardImg";const j=h,p=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n="div",...c}=e;return a=(0,d.oU)(a,"card-img-overlay"),(0,l.jsx)(n,{ref:s,className:t()(r,a),...c})});p.displayName="CardImgOverlay";const b=p,v=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n="a",...c}=e;return a=(0,d.oU)(a,"card-link"),(0,l.jsx)(n,{ref:s,className:t()(r,a),...c})});v.displayName="CardLink";const N=v;var y=r(4488);const g=(0,y.A)("h6"),$=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n=g,...c}=e;return a=(0,d.oU)(a,"card-subtitle"),(0,l.jsx)(n,{ref:s,className:t()(r,a),...c})});$.displayName="CardSubtitle";const w=$,_=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n="p",...c}=e;return a=(0,d.oU)(a,"card-text"),(0,l.jsx)(n,{ref:s,className:t()(r,a),...c})});_.displayName="CardText";const A=_,P=(0,y.A)("h5"),k=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n=P,...c}=e;return a=(0,d.oU)(a,"card-title"),(0,l.jsx)(n,{ref:s,className:t()(r,a),...c})});k.displayName="CardTitle";const R=k,U=n.forwardRef((e,s)=>{let{bsPrefix:r,className:a,bg:n,text:c,border:i,body:m=!1,children:u,as:x="div",...f}=e;const h=(0,d.oU)(r,"card");return(0,l.jsx)(x,{ref:s,...f,className:t()(a,h,n&&`bg-${n}`,c&&`text-${c}`,i&&`border-${i}`),children:m?(0,l.jsx)(o,{children:u}):u})});U.displayName="Card";const C=Object.assign(U,{Img:j,Title:R,Subtitle:w,Body:o,Link:N,Text:A,Header:f,Footer:m,ImgOverlay:b})}}]);
//# sourceMappingURL=551.c4baadb3.chunk.js.map