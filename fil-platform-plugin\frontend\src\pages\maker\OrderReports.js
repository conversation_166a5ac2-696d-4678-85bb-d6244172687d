import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const OrderReports = () => {
    const { t } = useTranslation();
    const [orders, setOrders] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchOrders = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // Fetch orders with related information
            const { data, error } = await supabase
                .from('orders')
                .select(`
                    id,
                    cid,
                    shares,
                    proof_image_url,
                    storage_cost,
                    pledge_cost,
                    total_rate,
                    tech_fee_pct,
                    sales_fee_pct,
                    ops_fee_pct,
                    start_at,
                    end_at,
                    review_status,
                    created_at,
                    updated_at,
                    products (
                        name,
                        category,
                        price
                    ),
                    agent_profiles (
                        brand_name,
                        users (
                            email
                        )
                    ),
                    customer_profiles (
                        real_name,
                        users (
                            email
                        )
                    )
                `)
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Error fetching orders:', error);
            } else {
                setOrders(data);
            }
            setLoading(false);
        };

        fetchOrders();
    }, []);

    const getStatusBadge = (status) => {
        switch (status) {
            case 'approved':
                return <Badge bg="success">{t('approved')}</Badge>;
            case 'pending':
                return <Badge bg="warning">{t('pending_review')}</Badge>;
            case 'rejected':
                return <Badge bg="danger">{t('rejected')}</Badge>;
            default:
                return <Badge bg="secondary">{status || '-'}</Badge>;
        }
    };

    if (loading) {
        return <div>{t('loading_order_reports')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('order_reports')}</h2>
            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('order_id')}</th>
                                        <th>{t('product_name')}</th>
                                        <th>{t('customer')}</th>
                                        <th>{t('agent')}</th>
                                        <th>{t('shares')}</th>
                                        <th>{t('storage_cost')}</th>
                                        <th>{t('pledge_cost')}</th>
                                        <th>{t('total_rate')}</th>
                                        <th>{t('start_date')}</th>
                                        <th>{t('end_date')}</th>
                                        <th>{t('review_status')}</th>
                                        <th>{t('created_at')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {orders.length === 0 ? (
                                        <tr>
                                            <td colSpan="12" className="text-center">{t('no_order_reports_available')}</td>
                                        </tr>
                                    ) : (
                                        orders.map(order => (
                                            <tr key={order.id}>
                                                <td>{order.cid || order.id}</td>
                                                <td>{order.products?.name || '-'}</td>
                                                <td>
                                                    <div>
                                                        <div>{order.customer_profiles?.real_name || '-'}</div>
                                                        <small className="text-muted">
                                                            {order.customer_profiles?.users?.email || '-'}
                                                        </small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div>
                                                        <div>{order.agent_profiles?.brand_name || '-'}</div>
                                                        <small className="text-muted">
                                                            {order.agent_profiles?.users?.email || '-'}
                                                        </small>
                                                    </div>
                                                </td>
                                                <td>{order.shares?.toFixed(2) || '0.00'}</td>
                                                <td>{order.storage_cost?.toFixed(6) || '0.000000'}</td>
                                                <td>{order.pledge_cost?.toFixed(6) || '0.000000'}</td>
                                                <td>{order.total_rate?.toFixed(4) || '0.0000'}%</td>
                                                <td>{order.start_at || '-'}</td>
                                                <td>{order.end_at || '-'}</td>
                                                <td>{getStatusBadge(order.review_status)}</td>
                                                <td>{new Date(order.created_at).toLocaleString()}</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default OrderReports;
