"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[748],{1072:(e,s,r)=>{r.d(s,{A:()=>c});var a=r(8139),t=r.n(a),d=r(5043),l=r(7852),n=r(579);const o=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:d="div",...o}=e;const c=(0,l.oU)(r,"row"),i=(0,l.gy)(),f=(0,l.Jm)(),m=`${c}-cols`,x=[];return i.forEach(e=>{const s=o[e];let r;delete o[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const a=e!==f?`-${e}`:"";null!=r&&x.push(`${m}${a}-${r}`)}),(0,n.jsx)(d,{ref:s,...o,className:t()(a,c,...x)})});o.displayName="Row";const c=o},4196:(e,s,r)=>{r.d(s,{A:()=>c});var a=r(8139),t=r.n(a),d=r(5043),l=r(7852),n=r(579);const o=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,striped:d,bordered:o,borderless:c,hover:i,size:f,variant:m,responsive:x,...h}=e;const b=(0,l.oU)(r,"table"),N=t()(a,b,m&&`${b}-${m}`,f&&`${b}-${f}`,d&&`${b}-${"string"===typeof d?`striped-${d}`:"striped"}`,o&&`${b}-bordered`,c&&`${b}-borderless`,i&&`${b}-hover`),u=(0,n.jsx)("table",{...h,className:N,ref:s});if(x){let e=`${b}-responsive`;return"string"===typeof x&&(e=`${e}-${x}`),(0,n.jsx)("div",{className:e,children:u})}return u});o.displayName="Table";const c=o},4748:(e,s,r)=>{r.r(s),r.d(s,{default:()=>m});var a=r(5043),t=r(3519),d=r(1072),l=r(8602),n=r(8628),o=r(4196),c=r(4312),i=r(4117),f=r(579);const m=()=>{const{t:e}=(0,i.Bd)(),[s,r]=(0,a.useState)([]),[m,x]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{(async()=>{const e=(0,c.b)();if(!e)return;x(!0);const{data:{user:s}}=await e.auth.getUser();if(!s)return void x(!1);const{data:a,error:t}=await e.from("network_stats").select("\n                    stat_date,\n                    blockchain_height,\n                    fil_per_tib\n                ").order("stat_date",{ascending:!1});t?console.error("Error fetching network stats:",t):r(a),x(!1)})()},[]),m?(0,f.jsx)("div",{children:e("loading_network_stats")}):(0,f.jsxs)(t.A,{children:[(0,f.jsx)("h2",{className:"mb-4",children:e("network_stats")}),(0,f.jsx)(d.A,{children:(0,f.jsx)(l.A,{children:(0,f.jsx)(n.A,{children:(0,f.jsx)(n.A.Body,{children:(0,f.jsxs)(o.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,f.jsx)("thead",{children:(0,f.jsxs)("tr",{children:[(0,f.jsx)("th",{children:e("stat_date")}),(0,f.jsx)("th",{children:e("fil_per_tib")})]})}),(0,f.jsx)("tbody",{children:0===s.length?(0,f.jsx)("tr",{children:(0,f.jsx)("td",{colSpan:"3",className:"text-center",children:e("no_network_stats_available")})}):s.map((e,s)=>(0,f.jsxs)("tr",{children:[(0,f.jsx)("td",{children:new Date(e.stat_date).toLocaleDateString()}),(0,f.jsxs)("td",{children:[e.fil_per_tib?Number(e.fil_per_tib).toFixed(4):"0"," FIL/TiB"]})]},`${e.stat_date}`))})]})})})})})]})}},8602:(e,s,r)=>{r.d(s,{A:()=>c});var a=r(8139),t=r.n(a),d=r(5043),l=r(7852),n=r(579);const o=d.forwardRef((e,s)=>{const[{className:r,...a},{as:d="div",bsPrefix:o,spans:c}]=function(e){let{as:s,bsPrefix:r,className:a,...d}=e;r=(0,l.oU)(r,"col");const n=(0,l.gy)(),o=(0,l.Jm)(),c=[],i=[];return n.forEach(e=>{const s=d[e];let a,t,l;delete d[e],"object"===typeof s&&null!=s?({span:a,offset:t,order:l}=s):a=s;const n=e!==o?`-${e}`:"";a&&c.push(!0===a?`${r}${n}`:`${r}${n}-${a}`),null!=l&&i.push(`order${n}-${l}`),null!=t&&i.push(`offset${n}-${t}`)}),[{...d,className:t()(a,...c,...i)},{as:s,bsPrefix:r,spans:c}]}(e);return(0,n.jsx)(d,{...a,ref:s,className:t()(r,!c.length&&o)})});o.displayName="Col";const c=o},8628:(e,s,r)=>{r.d(s,{A:()=>C});var a=r(8139),t=r.n(a),d=r(5043),l=r(7852),n=r(579);const o=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...o}=e;return a=(0,l.oU)(a,"card-body"),(0,n.jsx)(d,{ref:s,className:t()(r,a),...o})});o.displayName="CardBody";const c=o,i=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...o}=e;return a=(0,l.oU)(a,"card-footer"),(0,n.jsx)(d,{ref:s,className:t()(r,a),...o})});i.displayName="CardFooter";const f=i;var m=r(1778);const x=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:o="div",...c}=e;const i=(0,l.oU)(r,"card-header"),f=(0,d.useMemo)(()=>({cardHeaderBsPrefix:i}),[i]);return(0,n.jsx)(m.A.Provider,{value:f,children:(0,n.jsx)(o,{ref:s,...c,className:t()(a,i)})})});x.displayName="CardHeader";const h=x,b=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,variant:d,as:o="img",...c}=e;const i=(0,l.oU)(r,"card-img");return(0,n.jsx)(o,{ref:s,className:t()(d?`${i}-${d}`:i,a),...c})});b.displayName="CardImg";const N=b,u=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...o}=e;return a=(0,l.oU)(a,"card-img-overlay"),(0,n.jsx)(d,{ref:s,className:t()(r,a),...o})});u.displayName="CardImgOverlay";const p=u,j=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="a",...o}=e;return a=(0,l.oU)(a,"card-link"),(0,n.jsx)(d,{ref:s,className:t()(r,a),...o})});j.displayName="CardLink";const $=j;var v=r(4488);const y=(0,v.A)("h6"),w=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d=y,...o}=e;return a=(0,l.oU)(a,"card-subtitle"),(0,n.jsx)(d,{ref:s,className:t()(r,a),...o})});w.displayName="CardSubtitle";const g=w,_=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="p",...o}=e;return a=(0,l.oU)(a,"card-text"),(0,n.jsx)(d,{ref:s,className:t()(r,a),...o})});_.displayName="CardText";const P=_,R=(0,v.A)("h5"),U=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d=R,...o}=e;return a=(0,l.oU)(a,"card-title"),(0,n.jsx)(d,{ref:s,className:t()(r,a),...o})});U.displayName="CardTitle";const k=U,A=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,bg:d,text:o,border:i,body:f=!1,children:m,as:x="div",...h}=e;const b=(0,l.oU)(r,"card");return(0,n.jsx)(x,{ref:s,...h,className:t()(a,b,d&&`bg-${d}`,o&&`text-${o}`,i&&`border-${i}`),children:f?(0,n.jsx)(c,{children:m}):m})});A.displayName="Card";const C=Object.assign(A,{Img:N,Title:k,Subtitle:g,Body:c,Link:$,Text:P,Header:h,Footer:f,ImgOverlay:p})}}]);
//# sourceMappingURL=748.0351e7a5.chunk.js.map