{"version": 3, "file": "static/js/738.ecbb748a.chunk.js", "mappings": "sMAOA,MAAMA,GAAgBC,EAAAA,EAAAA,GAAiB,MACvCD,EAAcE,YAAc,gBAC5B,MAAMC,EAA4BC,EAAAA,WAAiB,CAAAC,EAKhDC,KAAQ,IALyC,UAClDC,EAAS,SACTC,EACAC,GAAIC,EAAYV,KACbW,GACJN,EAEC,OADAG,GAAWI,EAAAA,EAAAA,IAAmBJ,EAAU,kBACpBK,EAAAA,EAAAA,KAAKH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,MAC9BG,MAGPR,EAAaD,YAAc,eAC3B,U,cChBA,MAAMa,EAAyBX,EAAAA,WAAiB,CAAAC,EAK7CC,KAAQ,IALsC,UAC/CC,EAAS,SACTC,EACAC,GAAIC,EAAYM,EAAAA,KACbL,GACJN,EAEC,OADAG,GAAWI,EAAAA,EAAAA,IAAmBJ,EAAU,eACpBK,EAAAA,EAAAA,KAAKH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,MAC9BG,MAGPI,EAAUb,YAAc,YACxB,U,wBCRA,MAAMe,EAAqBb,EAAAA,WAAiB,CAACc,EAAmBZ,KAC9D,MAAM,SACJE,EAAQ,KACRW,GAAO,EAAI,WACXC,EAAa,cAAa,aAC1BC,EAAY,UACZd,EAAS,SACTe,EAAQ,QACRC,EAAU,UAAS,QACnBC,EAAO,YACPC,EAAW,WACXC,EAAaC,EAAAA,KACVhB,IACDiB,EAAAA,EAAAA,IAAgBV,EAAmB,CACrCC,KAAM,YAEFU,GAASjB,EAAAA,EAAAA,IAAmBJ,EAAU,SACtCsB,GAAcC,EAAAA,EAAAA,GAAiBC,IAC/BR,GACFA,GAAQ,EAAOQ,KAGbC,GAA4B,IAAfP,EAAsBC,EAAAA,EAAOD,EAC1CQ,GAAqBC,EAAAA,EAAAA,MAAM,MAAO,CACtCC,KAAM,WACDH,OAAqBI,EAAR1B,EAClBL,IAAKA,EACLC,UAAWO,IAAWP,EAAWsB,EAAQN,GAAW,GAAGM,KAAUN,IAAWE,GAAe,GAAGI,iBAC9FP,SAAU,CAACG,IAA4BZ,EAAAA,EAAAA,KAAKyB,EAAAA,EAAa,CACvDC,QAAST,EACT,aAAcV,EACdG,QAASF,IACPC,KAEN,OAAKW,GACepB,EAAAA,EAAAA,KAAKoB,EAAY,CACnCO,eAAe,KACZ7B,EACHL,SAAK+B,EACLI,GAAItB,EACJG,SAAUY,IANYf,EAAOe,EAAQ,OASzCjB,EAAMf,YAAc,QACpB,QAAewC,OAAOC,OAAO1B,EAAO,CAClC2B,KAAM7B,EACN8B,QAAS1C,G,4ICrDX,MA+LA,EA/LgB2C,KAAO,IAADC,EAAAC,EAClB,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,KAClCC,EAAUC,IAAeF,EAAAA,EAAAA,UAAS,KAClCG,EAAYC,IAAiBJ,EAAAA,EAAAA,UAAS,OACtCK,EAAWC,IAAgBN,EAAAA,EAAAA,UAAS,OACpCO,EAAcC,IAAmBR,EAAAA,EAAAA,UAAS,YAC1CS,EAASC,IAAcV,EAAAA,EAAAA,WAAS,IAChCW,EAAYC,IAAiBZ,EAAAA,EAAAA,WAAS,IACtCa,EAASC,IAAcd,EAAAA,EAAAA,UAAS,CAAEe,KAAM,GAAIC,KAAM,MAEzDC,EAAAA,EAAAA,WAAU,KACiBC,WACnB,MAAMC,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EAAU,OAEf,MAAQE,MAAM,KAAEC,UAAiBH,EAASI,KAAKC,UAC/C,IAAKF,EAED,YADAZ,GAAW,GAIf,MAAM,KAAEW,EAAI,MAAEI,SAAgBN,EACzBO,KAAK,qBACLC,OAAO,kEACPC,GAAG,UAAWN,EAAKO,IACnBC,SAEDL,GAAwB,aAAfA,EAAMM,MACfC,QAAQP,MAAM,6BAA8BA,GAC5CX,EAAW,CAAEC,KAAM,SAAUC,KAAMpB,EAAE,gCAC9ByB,IACPtB,EAAYsB,EAAKY,WAAa,IAC9B/B,EAAYmB,EAAKa,WAAa,IAC9B9B,EAAciB,EAAKc,cAAgB,MACnC7B,EAAae,EAAKe,aAAe,MACjC5B,EAAgBa,EAAKgB,eAAiB,YAE1C3B,GAAW,IAGf4B,IACD,IAEH,MAAMC,EAAmBA,CAAC5D,EAAG6D,KACrB7D,EAAE8D,OAAOC,OAAS/D,EAAE8D,OAAOC,MAAM,IACjCF,EAAS7D,EAAE8D,OAAOC,MAAM,KAgEhC,OAAIjC,GACOjD,EAAAA,EAAAA,KAAA,OAAAS,SAAM2B,EAAE,yBAIfd,EAAAA,EAAAA,MAAC6D,EAAAA,EAAS,CAAA1E,SAAA,EACNT,EAAAA,EAAAA,KAAA,MAAIN,UAAU,OAAMe,SAAE2B,EAAE,uBACxBpC,EAAAA,EAAAA,KAACoF,EAAAA,EAAI,CAAA3E,UACDa,EAAAA,EAAAA,MAAC8D,EAAAA,EAAKC,KAAI,CAAA5E,SAAA,CACL4C,EAAQG,OAAQxD,EAAAA,EAAAA,KAACI,EAAAA,EAAK,CAACM,QAAS2C,EAAQE,KAAK9C,SAAE4C,EAAQG,OAEtC,aAAjBT,IACG/C,EAAAA,EAAAA,KAACI,EAAAA,EAAK,CAACM,QAAQ,UAASD,SAAE2B,EAAE,kBAEd,YAAjBW,IACG/C,EAAAA,EAAAA,KAACI,EAAAA,EAAK,CAACM,QAAQ,OAAMD,SAAE2B,EAAE,wBAEX,aAAjBW,IACG/C,EAAAA,EAAAA,KAACI,EAAAA,EAAK,CAACM,QAAQ,SAAQD,SAAE2B,EAAE,mBAG/Bd,EAAAA,EAAAA,MAACgE,EAAAA,EAAI,CAACC,SAjFD7B,UACjBvC,EAAEqE,iBACFpC,GAAc,GACdE,EAAW,CAAEC,KAAM,GAAIC,KAAM,KAE7B,MAAMG,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EAAU,OAEf,MAAQE,MAAM,KAAEC,UAAiBH,EAASI,KAAKC,UAC/C,IAAKF,EAGD,OAFAR,EAAW,CAAEC,KAAM,SAAUC,KAAMpB,EAAE,6BACrCgB,GAAc,GAIlB,IACI,IAAIqC,EAAgB9C,EAChB+C,EAAe7C,EAGnB,GAAIF,aAAsBgD,KAAM,CAC5B,MAAQ9B,KAAM+B,EAAiB3B,MAAO4B,SAA2BlC,EAASmC,QACrE5B,KAAK,iBACL6B,OAAO,GAAGjC,EAAKO,YAAY2B,KAAKC,QAAStD,EAAY,CAAEuD,aAAc,OAAQC,QAAQ,IAC1F,GAAIN,EAAkB,MAAMA,EAC5BJ,EAAgBG,EAAgBQ,IACpC,CAEA,GAAIvD,aAAqB8C,KAAM,CAC3B,MAAQ9B,KAAMwC,EAAgBpC,MAAOqC,SAA0B3C,EAASmC,QACnE5B,KAAK,iBACL6B,OAAO,GAAGjC,EAAKO,WAAW2B,KAAKC,QAASpD,EAAW,CAAEqD,aAAc,OAAQC,QAAQ,IACxF,GAAIG,EAAiB,MAAMA,EAC3BZ,EAAeW,EAAeD,IAClC,CAEA,MAAM,MAAEnC,SAAgBN,EACnBO,KAAK,qBACLiC,OAAO,CACJI,QAASzC,EAAKO,GACdI,UAAWnC,EACXoC,UAAWjC,EACXkC,aAAcc,EACdb,YAAac,EACbb,cAAe,WAChB,CAAE2B,WAAY,YAErB,GAAIvC,EAAO,MAAMA,EAEjBX,EAAW,CAAEC,KAAM,UAAWC,KAAMpB,EAAE,wBACtCY,EAAgB,UAEpB,CAAE,MAAOiB,GACLO,QAAQP,MAAM,wBAAyBA,GACvCX,EAAW,CAAEC,KAAM,SAAUC,KAAMpB,EAAE,wBAA0B,KAAO6B,EAAMZ,SAChF,CAEAD,GAAc,IAwB2B3C,SAAA,EACzBa,EAAAA,EAAAA,MAACgE,EAAAA,EAAKmB,MAAK,CAAC/G,UAAU,OAAMe,SAAA,EACxBT,EAAAA,EAAAA,KAACsF,EAAAA,EAAKoB,MAAK,CAAAjG,SAAE2B,EAAE,gBACfpC,EAAAA,EAAAA,KAACsF,EAAAA,EAAKqB,QAAO,CACTpD,KAAK,OACLqD,MAAOtE,EACPuE,SAAW1F,GAAMoB,EAAYpB,EAAE8D,OAAO2B,OACtCE,UAAQ,EACRC,SAA2B,YAAjBhE,GAA+C,aAAjBA,QAGhDzB,EAAAA,EAAAA,MAACgE,EAAAA,EAAKmB,MAAK,CAAC/G,UAAU,OAAMe,SAAA,EACxBT,EAAAA,EAAAA,KAACsF,EAAAA,EAAKoB,MAAK,CAAAjG,SAAE2B,EAAE,gBACfpC,EAAAA,EAAAA,KAACsF,EAAAA,EAAKqB,QAAO,CACTpD,KAAK,OACLqD,MAAOnE,EACPoE,SAAW1F,GAAMuB,EAAYvB,EAAE8D,OAAO2B,OACtCE,UAAQ,EACRC,SAA2B,YAAjBhE,GAA+C,aAAjBA,QAGhDzB,EAAAA,EAAAA,MAACgE,EAAAA,EAAKmB,MAAK,CAAC/G,UAAU,OAAMe,SAAA,EACxBT,EAAAA,EAAAA,KAACsF,EAAAA,EAAKoB,MAAK,CAAAjG,SAAE2B,EAAE,eACfpC,EAAAA,EAAAA,KAACsF,EAAAA,EAAKqB,QAAO,CACTpD,KAAK,OACLsD,SAAW1F,GAAM4D,EAAiB5D,EAAGyB,GACrCoE,OAAO,UACPD,SAA2B,YAAjBhE,GAA+C,aAAjBA,IAE3CJ,GAAoC,kBAAfA,IAClB3C,EAAAA,EAAAA,KAAA,OAAKiH,IAAkB,QAAf/E,GAAE0B,EAAAA,EAAAA,YAAa,IAAA1B,OAAA,EAAbA,EAAe4D,QAAQ5B,KAAK,iBAAiBgD,aAAavE,GAAYkB,KAAKsD,UAAWC,IAAI,WAAW1H,UAAU,qBAAqB2H,MAAO,CAAEC,SAAU,eAGzKhG,EAAAA,EAAAA,MAACgE,EAAAA,EAAKmB,MAAK,CAAC/G,UAAU,OAAMe,SAAA,EACxBT,EAAAA,EAAAA,KAACsF,EAAAA,EAAKoB,MAAK,CAAAjG,SAAE2B,EAAE,cACfpC,EAAAA,EAAAA,KAACsF,EAAAA,EAAKqB,QAAO,CACTpD,KAAK,OACLsD,SAAW1F,GAAM4D,EAAiB5D,EAAG2B,GACrCkE,OAAO,UACPD,SAA2B,YAAjBhE,GAA+C,aAAjBA,IAE3CF,GAAkC,kBAAdA,IACjB7C,EAAAA,EAAAA,KAAA,OAAKiH,IAAkB,QAAf9E,GAAEyB,EAAAA,EAAAA,YAAa,IAAAzB,OAAA,EAAbA,EAAe2D,QAAQ5B,KAAK,iBAAiBgD,aAAarE,GAAWgB,KAAKsD,UAAWC,IAAI,UAAU1H,UAAU,qBAAqB2H,MAAO,CAAEC,SAAU,eAIvKtH,EAAAA,EAAAA,KAACuH,EAAAA,EAAM,CACH7G,QAAQ,UACR6C,KAAK,SACLwD,SAAU5D,GAA+B,YAAjBJ,GAA+C,aAAjBA,EAA4BtC,SAEpE2B,EAAbe,EAAe,aAAkB,8B", "sources": ["../node_modules/react-bootstrap/esm/AlertHeading.js", "../node_modules/react-bootstrap/esm/AlertLink.js", "../node_modules/react-bootstrap/esm/Alert.js", "pages/customer/KycPage.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH4 = divWithClassName('h4');\nDivStyledAsH4.displayName = 'DivStyledAsH4';\nconst AlertHeading = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH4,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'alert-heading');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nAlertHeading.displayName = 'AlertHeading';\nexport default AlertHeading;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Anchor from '@restart/ui/Anchor';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst AlertLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = Anchor,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'alert-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nAlertLink.displayName = 'AlertLink';\nexport default AlertLink;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useUncontrolled } from 'uncontrollable';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AlertHeading from './AlertHeading';\nimport AlertLink from './AlertLink';\nimport Fade from './Fade';\nimport CloseButton from './CloseButton';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst Alert = /*#__PURE__*/React.forwardRef((uncontrolledProps, ref) => {\n  const {\n    bsPrefix,\n    show = true,\n    closeLabel = 'Close alert',\n    closeVariant,\n    className,\n    children,\n    variant = 'primary',\n    onClose,\n    dismissible,\n    transition = Fade,\n    ...props\n  } = useUncontrolled(uncontrolledProps, {\n    show: 'onClose'\n  });\n  const prefix = useBootstrapPrefix(bsPrefix, 'alert');\n  const handleClose = useEventCallback(e => {\n    if (onClose) {\n      onClose(false, e);\n    }\n  });\n  const Transition = transition === true ? Fade : transition;\n  const alert = /*#__PURE__*/_jsxs(\"div\", {\n    role: \"alert\",\n    ...(!Transition ? props : undefined),\n    ref: ref,\n    className: classNames(className, prefix, variant && `${prefix}-${variant}`, dismissible && `${prefix}-dismissible`),\n    children: [dismissible && /*#__PURE__*/_jsx(CloseButton, {\n      onClick: handleClose,\n      \"aria-label\": closeLabel,\n      variant: closeVariant\n    }), children]\n  });\n  if (!Transition) return show ? alert : null;\n  return /*#__PURE__*/_jsx(Transition, {\n    unmountOnExit: true,\n    ...props,\n    ref: undefined,\n    in: show,\n    children: alert\n  });\n});\nAlert.displayName = 'Alert';\nexport default Object.assign(Alert, {\n  Link: AlertLink,\n  Heading: AlertHeading\n});", "\nimport React, { useState, useEffect } from 'react';\nimport { Container, Form, Button, Card, Alert } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst KycPage = () => {\n    const { t } = useTranslation();\n    const [realName, setRealName] = useState('');\n    const [idNumber, setIdNumber] = useState('');\n    const [idImgFront, setIdImgFront] = useState(null);\n    const [idImgBack, setIdImgBack] = useState(null);\n    const [verifyStatus, setVerifyStatus] = useState('pending'); // or 'approved', 'rejected'\n    const [loading, setLoading] = useState(true);\n    const [submitting, setSubmitting] = useState(false);\n    const [message, setMessage] = useState({ type: '', text: '' });\n\n    useEffect(() => {\n        const fetchKycStatus = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            const { data: { user } } = await supabase.auth.getUser();\n            if (!user) {\n                setLoading(false);\n                return;\n            }\n\n            const { data, error } = await supabase\n                .from('customer_profiles')\n                .select('real_name, id_number, id_img_front, id_img_back, verify_status')\n                .eq('user_id', user.id)\n                .single();\n\n            if (error && error.code !== 'PGRST116') { // PGRST116 means no rows found\n                console.error('Error fetching KYC status:', error);\n                setMessage({ type: 'danger', text: t('failed_to_load_kyc_status') });\n            } else if (data) {\n                setRealName(data.real_name || '');\n                setIdNumber(data.id_number || '');\n                setIdImgFront(data.id_img_front || null);\n                setIdImgBack(data.id_img_back || null);\n                setVerifyStatus(data.verify_status || 'pending');\n            }\n            setLoading(false);\n        };\n\n        fetchKycStatus();\n    }, []);\n\n    const handleFileChange = (e, setImage) => {\n        if (e.target.files && e.target.files[0]) {\n            setImage(e.target.files[0]);\n        }\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        setSubmitting(true);\n        setMessage({ type: '', text: '' });\n\n        const supabase = getSupabase();\n        if (!supabase) return;\n\n        const { data: { user } } = await supabase.auth.getUser();\n        if (!user) {\n            setMessage({ type: 'danger', text: t('user_not_logged_in') });\n            setSubmitting(false);\n            return;\n        }\n\n        try {\n            let frontImageUrl = idImgFront;\n            let backImageUrl = idImgBack;\n\n            // Upload images if they are new File objects\n            if (idImgFront instanceof File) {\n                const { data: uploadDataFront, error: uploadErrorFront } = await supabase.storage\n                    .from('kyc-documents')\n                    .upload(`${user.id}/front_${Date.now()}`, idImgFront, { cacheControl: '3600', upsert: false });\n                if (uploadErrorFront) throw uploadErrorFront;\n                frontImageUrl = uploadDataFront.path;\n            }\n\n            if (idImgBack instanceof File) {\n                const { data: uploadDataBack, error: uploadErrorBack } = await supabase.storage\n                    .from('kyc-documents')\n                    .upload(`${user.id}/back_${Date.now()}`, idImgBack, { cacheControl: '3600', upsert: false });\n                if (uploadErrorBack) throw uploadErrorBack;\n                backImageUrl = uploadDataBack.path;\n            }\n\n            const { error } = await supabase\n                .from('customer_profiles')\n                .upsert({\n                    user_id: user.id,\n                    real_name: realName,\n                    id_number: idNumber,\n                    id_img_front: frontImageUrl,\n                    id_img_back: backImageUrl,\n                    verify_status: 'pending' // Always set to pending on submission\n                }, { onConflict: 'user_id' });\n\n            if (error) throw error;\n\n            setMessage({ type: 'success', text: t('kyc_submit_success') });\n            setVerifyStatus('pending');\n\n        } catch (error) {\n            console.error('KYC submission error:', error);\n            setMessage({ type: 'danger', text: t('failed_to_submit_kyc') + ': ' + error.message });\n        }\n\n        setSubmitting(false);\n    };\n\n    if (loading) {\n        return <div>{t('loading_kyc_status')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('kyc_verification')}</h2>\n            <Card>\n                <Card.Body>\n                    {message.text && <Alert variant={message.type}>{message.text}</Alert>}\n                    \n                    {verifyStatus === 'approved' && (\n                        <Alert variant=\"success\">{t('kyc_approved')}</Alert>\n                    )}\n                    {verifyStatus === 'pending' && (\n                        <Alert variant=\"info\">{t('kyc_pending_review')}</Alert>\n                    )}\n                    {verifyStatus === 'rejected' && (\n                        <Alert variant=\"danger\">{t('kyc_rejected')}</Alert>\n                    )}\n\n                    <Form onSubmit={handleSubmit}>\n                        <Form.Group className=\"mb-3\">\n                            <Form.Label>{t('real_name')}</Form.Label>\n                            <Form.Control \n                                type=\"text\" \n                                value={realName} \n                                onChange={(e) => setRealName(e.target.value)} \n                                required \n                                disabled={verifyStatus === 'pending' || verifyStatus === 'approved'}\n                            />\n                        </Form.Group>\n                        <Form.Group className=\"mb-3\">\n                            <Form.Label>{t('id_number')}</Form.Label>\n                            <Form.Control \n                                type=\"text\" \n                                value={idNumber} \n                                onChange={(e) => setIdNumber(e.target.value)} \n                                required \n                                disabled={verifyStatus === 'pending' || verifyStatus === 'approved'}\n                            />\n                        </Form.Group>\n                        <Form.Group className=\"mb-3\">\n                            <Form.Label>{t('id_front')}</Form.Label>\n                            <Form.Control \n                                type=\"file\" \n                                onChange={(e) => handleFileChange(e, setIdImgFront)} \n                                accept=\"image/*\" \n                                disabled={verifyStatus === 'pending' || verifyStatus === 'approved'}\n                            />\n                            {idImgFront && typeof idImgFront === 'string' && (\n                                <img src={getSupabase()?.storage.from('kyc-documents').getPublicUrl(idImgFront).data.publicUrl} alt=\"ID Front\" className=\"img-thumbnail mt-2\" style={{ maxWidth: '200px' }} />\n                            )}\n                        </Form.Group>\n                        <Form.Group className=\"mb-3\">\n                            <Form.Label>{t('id_back')}</Form.Label>\n                            <Form.Control \n                                type=\"file\" \n                                onChange={(e) => handleFileChange(e, setIdImgBack)} \n                                accept=\"image/*\" \n                                disabled={verifyStatus === 'pending' || verifyStatus === 'approved'}\n                            />\n                            {idImgBack && typeof idImgBack === 'string' && (\n                                <img src={getSupabase()?.storage.from('kyc-documents').getPublicUrl(idImgBack).data.publicUrl} alt=\"ID Back\" className=\"img-thumbnail mt-2\" style={{ maxWidth: '200px' }} />\n                            )}\n                        </Form.Group>\n                        \n                        <Button \n                            variant=\"primary\" \n                            type=\"submit\" \n                            disabled={submitting || verifyStatus === 'pending' || verifyStatus === 'approved'}\n                        >\n                            {submitting ? t('submitting') : t('submit_review')}\n                        </Button>\n                    </Form>\n                </Card.Body>\n            </Card>\n        </Container>\n    );\n};\n\nexport default KycPage;\n"], "names": ["DivStyledAsH4", "divWithClassName", "displayName", "AlertHeading", "React", "_ref", "ref", "className", "bsPrefix", "as", "Component", "props", "useBootstrapPrefix", "_jsx", "classNames", "AlertLink", "<PERSON><PERSON>", "<PERSON><PERSON>", "uncontrolledProps", "show", "<PERSON><PERSON><PERSON><PERSON>", "closeVariant", "children", "variant", "onClose", "dismissible", "transition", "Fade", "useUncontrolled", "prefix", "handleClose", "useEventCallback", "e", "Transition", "alert", "_jsxs", "role", "undefined", "CloseButton", "onClick", "unmountOnExit", "in", "Object", "assign", "Link", "Heading", "KycPage", "_get<PERSON><PERSON><PERSON>se", "_getSupabase2", "t", "useTranslation", "realName", "setRealName", "useState", "idNumber", "setIdNumber", "idImgFront", "setIdImgFront", "idImgBack", "setIdImgBack", "verifyStatus", "setVerifyStatus", "loading", "setLoading", "submitting", "setSubmitting", "message", "setMessage", "type", "text", "useEffect", "async", "supabase", "getSupabase", "data", "user", "auth", "getUser", "error", "from", "select", "eq", "id", "single", "code", "console", "real_name", "id_number", "id_img_front", "id_img_back", "verify_status", "fetchKycStatus", "handleFileChange", "setImage", "target", "files", "Container", "Card", "Body", "Form", "onSubmit", "preventDefault", "frontImageUrl", "backImageUrl", "File", "uploadDataFront", "uploadErrorFront", "storage", "upload", "Date", "now", "cacheControl", "upsert", "path", "uploadDataBack", "uploadErrorBack", "user_id", "onConflict", "Group", "Label", "Control", "value", "onChange", "required", "disabled", "accept", "src", "getPublicUrl", "publicUrl", "alt", "style", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "sourceRoot": ""}