import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const CoinBatches = () => {
    const { t } = useTranslation();
    const [batches, setBatches] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchBatches = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // Fetch distribution batches with related information
            const { data, error } = await supabase
                .from('distribution_batches')
                .select(`
                    id,
                    maker_id,
                    agent_id,
                    currency_code,
                    product_id,
                    shares,
                    batch_amount,
                    per_share_amount,
                    status,
                    created_at,
                    distributed_at,
                    maker:maker_profiles!maker_id (
                        user_id,
                        users (
                            email
                        )
                    ),
                    agent:agent_profiles!agent_id (
                        user_id,
                        users (
                            email
                        )
                    ),
                    product:products!product_id (
                        name,
                        category
                    ),
                    currency:currencies!currency_code (
                        code
                    )
                `)
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Error fetching distribution batches:', error);
            } else {
                setBatches(data);
            }
            setLoading(false);
        };

        fetchBatches();
    }, []);

    const getStatusColor = (status) => {
        switch (status) {
            case 'pending':
                return 'warning';
            case 'distributed':
                return 'success';
            case 'failed':
                return 'danger';
            case 'cancelled':
                return 'secondary';
            default:
                return 'primary';
        }
    };

    if (loading) {
        return <div>{t('loading_batches')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('coin_batches')}</h2>
            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('batch_id')}</th>
                                        <th>{t('maker')}</th>
                                        <th>{t('agent')}</th>
                                        <th>{t('product_name')}</th>
                                        <th>{t('currency')}</th>
                                        <th>{t('shares')}</th>
                                        <th>{t('batch_amount')}</th>
                                        <th>{t('per_share_amount')}</th>
                                        <th>{t('status')}</th>
                                        <th>{t('created_at')}</th>
                                        <th>{t('distributed_at')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {batches.length === 0 ? (
                                        <tr>
                                            <td colSpan="11" className="text-center">{t('no_batches_available')}</td>
                                        </tr>
                                    ) : (
                                        batches.map((batch) => (
                                            <tr key={batch.id}>
                                                <td>{batch.id.substring(0, 8)}...</td>
                                                <td>{batch.maker?.users?.email || '-'}</td>
                                                <td>{batch.agent?.users?.email || '-'}</td>
                                                <td>{batch.product?.name || '-'}</td>
                                                <td>{batch.currency_code || '-'}</td>
                                                <td>{batch.shares ? Number(batch.shares).toFixed(2) : '0'}</td>
                                                <td>{batch.batch_amount ? Number(batch.batch_amount).toFixed(6) : '0'} {batch.currency_code}</td>
                                                <td>{batch.per_share_amount ? Number(batch.per_share_amount).toFixed(6) : '0'} {batch.currency_code}</td>
                                                <td>
                                                    <Badge bg={getStatusColor(batch.status)}>
                                                        {batch.status || 'unknown'}
                                                    </Badge>
                                                </td>
                                                <td>{new Date(batch.created_at).toLocaleString()}</td>
                                                <td>{batch.distributed_at ? new Date(batch.distributed_at).toLocaleString() : '-'}</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default CoinBatches;
