{"name": "filfox-scraper", "version": "1.0.0", "description": "Scraper for filfox.info data to store in network_stats table", "main": "filfox-scraper.js", "scripts": {"start": "node filfox-scraper.js", "test": "node test-scraper.js", "schedule": "node scheduler.js", "monitor": "node monitor.js", "install-deps": "npm install"}, "dependencies": {"@supabase/supabase-js": "^2.38.0", "puppeteer": "^21.5.0", "dotenv": "^16.3.1", "node-cron": "^3.0.2"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}, "keywords": ["filfox", "scraper", "filecoin", "blockchain", "network-stats"], "author": "FIL Platform", "license": "MIT"}