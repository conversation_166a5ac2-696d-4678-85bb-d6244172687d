[Unit]
Description=Filfox Data Scraper Service
Documentation=https://github.com/your-repo/filfox-scraper
After=network.target
Wants=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/path/to/your/scripts
ExecStart=/usr/bin/node scheduler.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=TZ=Asia/Tokyo
EnvironmentFile=/path/to/your/scripts/.env

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=filfox-scraper

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/path/to/your/scripts/logs

[Install]
WantedBy=multi-user.target
