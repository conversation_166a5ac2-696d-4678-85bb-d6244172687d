import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { getSupabase } from '../../supabaseClient';

const AgentDashboard = () => {
    const { t } = useTranslation();
    const [agentProfile, setAgentProfile] = useState(null);
    const [loading, setLoading] = useState(true);
    const [dashboardStats, setDashboardStats] = useState({
        totalTechCommission: 0,
        totalOpsCommission: 0,
        memberCount: 0,
        totalStorage: 0,
        soldStorage: 0,
        remainingStorage: 0
    });

    const fetchDashboardStats = async (supabase, agentUserId, makerId) => {
        try {
            // 1. 获取会员总数 (customers under this agent)
            const { data: memberData, error: memberError } = await supabase
                .from('customer_profiles')
                .select('user_id', { count: 'exact' })
                .eq('agent_id', agentUserId);

            // 2. 获取该agent的所有订单及相关产品信息
            const { data: orderData, error: orderError } = await supabase
                .from('orders')
                .select(`
                    id,
                    shares,
                    storage_cost,
                    pledge_cost,
                    tech_fee_pct,
                    ops_fee_pct,
                    products (
                        total_shares,
                        sold_shares,
                        tech_commission_pct,
                        ops_commission_pct,
                        maker_id
                    )
                `)
                .eq('agent_id', agentUserId);

            // 3. 获取该maker的所有产品信息（用于计算总存储）
            const { data: productData, error: productError } = await supabase
                .from('products')
                .select('total_shares, sold_shares')
                .eq('maker_id', makerId);

            let stats = {
                totalTechCommission: 0,
                totalOpsCommission: 0,
                memberCount: memberData ? memberData.length : 0,
                totalStorage: 0,
                soldStorage: 0,
                remainingStorage: 0
            };

            // 计算技术佣金和运营佣金
            if (orderData && !orderError) {
                orderData.forEach(order => {
                    const techCommission = (order.storage_cost || 0) * (order.tech_fee_pct || 0) / 100;
                    const opsCommission = (order.storage_cost || 0) * (order.ops_fee_pct || 0) / 100;
                    stats.totalTechCommission += techCommission;
                    stats.totalOpsCommission += opsCommission;
                });
            }

            // 计算存储统计
            if (productData && !productError) {
                productData.forEach(product => {
                    stats.totalStorage += product.total_shares || 0;
                    stats.soldStorage += product.sold_shares || 0;
                });
                stats.remainingStorage = stats.totalStorage - stats.soldStorage;
            }

            setDashboardStats(stats);

        } catch (error) {
            console.error('Error fetching dashboard stats:', error);
        }
    };

    useEffect(() => {
        const fetchAgentData = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                console.log('No user found');
                setLoading(false);
                return; // User not logged in
            }

            console.log('Current user:', user.id);
            console.log('User role from localStorage:', localStorage.getItem('user_role'));

            // Fetch agent profile
            const { data: profileData, error: profileError } = await supabase
                .from('agent_profiles')
                .select('*')
                .eq('user_id', user.id)
                .single();

            if (profileError) {
                console.error('Error fetching agent profile:', profileError);
                console.error('User ID:', user.id);
                console.error('Error details:', profileError);
                setLoading(false);
                return;
            }

            console.log('Agent profile data:', profileData);
            setAgentProfile(profileData);

            // Fetch dashboard statistics
            await fetchDashboardStats(supabase, user.id, profileData.maker_id);
            setLoading(false);
        };

        fetchAgentData();
    }, []);

    if (loading) {
        return <div>{t('loading_agent_dashboard')}</div>;
    }

    if (!agentProfile) {
        return <div className="alert alert-warning">{t('not_agent')}</div>;
    }

    return (
        <Container fluid>
            <Row className="mb-3">
                <Col>
                    <h2>{t('agent_dashboard')}</h2>
                </Col>
            </Row>

            {/* 第一行：基本信息 */}
            <Row>
                <Col md={4}>
                    <Card className="bg-primary text-white mb-3">
                        <Card.Body>
                            <Card.Title>{t('brand_name')}</Card.Title>
                            <h3>{agentProfile.brand_name || 'N/A'}</h3>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={4}>
                    <Card className="bg-success text-white mb-3">
                        <Card.Body>
                            <Card.Title>{t('commission_rate')}</Card.Title>
                            <h3>{agentProfile.commission_pct ? `${agentProfile.commission_pct * 100}%` : 'N/A'}</h3>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={4}>
                    <Card className="bg-info text-white mb-3">
                        <Card.Body>
                            <Card.Title>{t('kyc_status')}</Card.Title>
                            <h3>{agentProfile.kyc_status || 'N/A'}</h3>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* 第二行：佣金统计 */}
            <Row>
                <Col md={6}>
                    <Card className="bg-warning text-white mb-3">
                        <Card.Body>
                            <Card.Title>{t('total_tech_commission')}</Card.Title>
                            <h3>{dashboardStats.totalTechCommission.toFixed(6)} FIL</h3>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={6}>
                    <Card className="bg-danger text-white mb-3">
                        <Card.Body>
                            <Card.Title>{t('total_ops_commission')}</Card.Title>
                            <h3>{dashboardStats.totalOpsCommission.toFixed(6)} FIL</h3>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* 第三行：会员和存储统计 */}
            <Row>
                <Col md={3}>
                    <Card className="bg-secondary text-white mb-3">
                        <Card.Body>
                            <Card.Title>{t('member_count')}</Card.Title>
                            <h3>{dashboardStats.memberCount}</h3>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="bg-dark text-white mb-3">
                        <Card.Body>
                            <Card.Title>{t('total_storage')}</Card.Title>
                            <h3>{dashboardStats.totalStorage.toFixed(2)} TiB</h3>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="bg-success text-white mb-3">
                        <Card.Body>
                            <Card.Title>{t('sold_storage')}</Card.Title>
                            <h3>{dashboardStats.soldStorage.toFixed(2)} TiB</h3>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="bg-light text-dark mb-3">
                        <Card.Body>
                            <Card.Title>{t('remaining_storage')}</Card.Title>
                            <h3>{dashboardStats.remainingStorage.toFixed(2)} TiB</h3>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row className="mt-4">
                <Col md={6} className="text-center">
                    <Card>
                        <Card.Body>
                            <h4>{t('member_management')}</h4>
                            <p>{t('my_subordinate_members')}</p>
                            <Button as={Link} to="/agent/member-list" variant="primary">{t('enter_member_list')}</Button>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={6} className="text-center">
                    <Card>
                        <Card.Body>
                            <h4>{t('product_management')}</h4>
                            <p>{t('products_on_sale')}</p>
                            <Button as={Link} to="/agent/products" variant="success">{t('browse_agent_products')}</Button>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default AgentDashboard;