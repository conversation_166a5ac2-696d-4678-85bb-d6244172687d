{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Container,Row,Col,Card,Button}from'react-bootstrap';import{useTranslation}from'react-i18next';import{Link}from'react-router-dom';import{getSupabase}from'../../supabaseClient';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AgentDashboard=()=>{const{t}=useTranslation();const[agentProfile,setAgentProfile]=useState(null);const[loading,setLoading]=useState(true);const[dashboardStats,setDashboardStats]=useState({totalTechCommission:0,totalOpsCommission:0,memberCount:0,totalStorage:0,soldStorage:0,remainingStorage:0});const fetchDashboardStats=async(supabase,agentUserId,makerId)=>{try{// 1. 获取会员总数 (customers under this agent)\nconst{data:memberData,error:memberError}=await supabase.from('customer_profiles').select('user_id',{count:'exact'}).eq('agent_id',agentUserId);// 2. 获取该agent的所有订单及相关产品信息\nconst{data:orderData,error:orderError}=await supabase.from('orders').select(`\n                    id,\n                    shares,\n                    storage_cost,\n                    pledge_cost,\n                    tech_fee_pct,\n                    ops_fee_pct,\n                    products (\n                        total_shares,\n                        sold_shares,\n                        tech_commission_pct,\n                        ops_commission_pct,\n                        maker_id\n                    )\n                `).eq('agent_id',agentUserId);// 3. 获取该maker的所有产品信息（用于计算总存储）\nconst{data:productData,error:productError}=await supabase.from('products').select('total_shares, sold_shares').eq('maker_id',makerId);let stats={totalTechCommission:0,totalOpsCommission:0,memberCount:memberData?memberData.length:0,totalStorage:0,soldStorage:0,remainingStorage:0};// 计算技术佣金和运营佣金\nif(orderData&&!orderError){orderData.forEach(order=>{const techCommission=(order.storage_cost||0)*(order.tech_fee_pct||0)/100;const opsCommission=(order.storage_cost||0)*(order.ops_fee_pct||0)/100;stats.totalTechCommission+=techCommission;stats.totalOpsCommission+=opsCommission;});}// 计算存储统计\nif(productData&&!productError){productData.forEach(product=>{stats.totalStorage+=product.total_shares||0;stats.soldStorage+=product.sold_shares||0;});stats.remainingStorage=stats.totalStorage-stats.soldStorage;}setDashboardStats(stats);}catch(error){console.error('Error fetching dashboard stats:',error);}};useEffect(()=>{const fetchAgentData=async()=>{const supabase=getSupabase();if(!supabase)return;setLoading(true);const{data:{user}}=await supabase.auth.getUser();if(!user){console.log('No user found');setLoading(false);return;// User not logged in\n}console.log('Current user:',user.id);console.log('User role from localStorage:',localStorage.getItem('user_role'));// Fetch agent profile\nconst{data:profileData,error:profileError}=await supabase.from('agent_profiles').select('*').eq('user_id',user.id).single();if(profileError){console.error('Error fetching agent profile:',profileError);console.error('User ID:',user.id);console.error('Error details:',profileError);setLoading(false);return;}console.log('Agent profile data:',profileData);setAgentProfile(profileData);// Fetch dashboard statistics\nawait fetchDashboardStats(supabase,user.id,profileData.maker_id);setLoading(false);};fetchAgentData();},[]);if(loading){return/*#__PURE__*/_jsx(\"div\",{children:t('loading_agent_dashboard')});}if(!agentProfile){return/*#__PURE__*/_jsx(\"div\",{className:\"alert alert-warning\",children:t('not_agent')});}return/*#__PURE__*/_jsxs(Container,{fluid:true,children:[/*#__PURE__*/_jsx(Row,{className:\"mb-3\",children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(\"h2\",{children:t('agent_dashboard')})})}),/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsx(Card,{className:\"text-white mb-3\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:t('brand_name')}),/*#__PURE__*/_jsx(\"h3\",{children:agentProfile.brand_name||'N/A'})]})})}),/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsx(Card,{className:\"text-white mb-3\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:t('commission_rate')}),/*#__PURE__*/_jsx(\"h3\",{children:agentProfile.commission_pct?`${agentProfile.commission_pct*100}%`:'N/A'})]})})}),/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsx(Card,{className:\"text-white mb-3\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:t('kyc_status')}),/*#__PURE__*/_jsx(\"h3\",{children:agentProfile.kyc_status||'N/A'})]})})})]}),/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsx(Card,{className:\"text-white mb-3\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:t('total_tech_commission')}),/*#__PURE__*/_jsxs(\"h3\",{children:[dashboardStats.totalTechCommission.toFixed(6),\" FIL\"]})]})})}),/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsx(Card,{className:\"text-white mb-3\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:t('total_ops_commission')}),/*#__PURE__*/_jsxs(\"h3\",{children:[dashboardStats.totalOpsCommission.toFixed(6),\" FIL\"]})]})})})]}),/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(Card,{className:\"text-white mb-3\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:t('member_count')}),/*#__PURE__*/_jsx(\"h3\",{children:dashboardStats.memberCount})]})})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(Card,{className:\"text-white mb-3\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:t('total_storage')}),/*#__PURE__*/_jsxs(\"h3\",{children:[dashboardStats.totalStorage.toFixed(2),\" TiB\"]})]})})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(Card,{className:\"text-white mb-3\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:t('sold_storage')}),/*#__PURE__*/_jsxs(\"h3\",{children:[dashboardStats.soldStorage.toFixed(2),\" TiB\"]})]})})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(Card,{className:\"text-dark mb-3\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:t('remaining_storage')}),/*#__PURE__*/_jsxs(\"h3\",{children:[dashboardStats.remainingStorage.toFixed(2),\" TiB\"]})]})})})]}),/*#__PURE__*/_jsxs(Row,{className:\"mt-4\",children:[/*#__PURE__*/_jsx(Col,{md:6,className:\"text-center\",children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"h4\",{children:t('member_management')}),/*#__PURE__*/_jsx(\"p\",{children:t('my_subordinate_members')}),/*#__PURE__*/_jsx(Button,{as:Link,to:\"/agent/member-list\",children:t('enter_member_list')})]})})}),/*#__PURE__*/_jsx(Col,{md:6,className:\"text-center\",children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"h4\",{children:t('product_management')}),/*#__PURE__*/_jsx(\"p\",{children:t('products_on_sale')}),/*#__PURE__*/_jsx(Button,{as:Link,to:\"/agent/products\",children:t('browse_agent_products')})]})})})]})]});};export default AgentDashboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "useTranslation", "Link", "getSupabase", "jsx", "_jsx", "jsxs", "_jsxs", "AgentDashboard", "t", "agentProfile", "setAgentProfile", "loading", "setLoading", "dashboardStats", "setDashboardStats", "totalTechCommission", "totalOpsCommission", "memberCount", "totalStorage", "soldStorage", "remainingStorage", "fetchDashboardStats", "supabase", "agentUserId", "makerId", "data", "memberData", "error", "member<PERSON><PERSON><PERSON>", "from", "select", "count", "eq", "orderData", "orderError", "productData", "productError", "stats", "length", "for<PERSON>ach", "order", "techCommission", "storage_cost", "tech_fee_pct", "opsCommission", "ops_fee_pct", "product", "total_shares", "sold_shares", "console", "fetchAgentData", "user", "auth", "getUser", "log", "id", "localStorage", "getItem", "profileData", "profileError", "single", "maker_id", "children", "className", "fluid", "md", "Body", "Title", "brand_name", "commission_pct", "kyc_status", "toFixed", "as", "to"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/agent/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button } from 'react-bootstrap';\nimport { useTranslation } from 'react-i18next';\nimport { Link } from 'react-router-dom';\nimport { getSupabase } from '../../supabaseClient';\n\nconst AgentDashboard = () => {\n    const { t } = useTranslation();\n    const [agentProfile, setAgentProfile] = useState(null);\n    const [loading, setLoading] = useState(true);\n    const [dashboardStats, setDashboardStats] = useState({\n        totalTechCommission: 0,\n        totalOpsCommission: 0,\n        memberCount: 0,\n        totalStorage: 0,\n        soldStorage: 0,\n        remainingStorage: 0\n    });\n\n    const fetchDashboardStats = async (supabase, agentUserId, makerId) => {\n        try {\n            // 1. 获取会员总数 (customers under this agent)\n            const { data: memberData, error: memberError } = await supabase\n                .from('customer_profiles')\n                .select('user_id', { count: 'exact' })\n                .eq('agent_id', agentUserId);\n\n            // 2. 获取该agent的所有订单及相关产品信息\n            const { data: orderData, error: orderError } = await supabase\n                .from('orders')\n                .select(`\n                    id,\n                    shares,\n                    storage_cost,\n                    pledge_cost,\n                    tech_fee_pct,\n                    ops_fee_pct,\n                    products (\n                        total_shares,\n                        sold_shares,\n                        tech_commission_pct,\n                        ops_commission_pct,\n                        maker_id\n                    )\n                `)\n                .eq('agent_id', agentUserId);\n\n            // 3. 获取该maker的所有产品信息（用于计算总存储）\n            const { data: productData, error: productError } = await supabase\n                .from('products')\n                .select('total_shares, sold_shares')\n                .eq('maker_id', makerId);\n\n            let stats = {\n                totalTechCommission: 0,\n                totalOpsCommission: 0,\n                memberCount: memberData ? memberData.length : 0,\n                totalStorage: 0,\n                soldStorage: 0,\n                remainingStorage: 0\n            };\n\n            // 计算技术佣金和运营佣金\n            if (orderData && !orderError) {\n                orderData.forEach(order => {\n                    const techCommission = (order.storage_cost || 0) * (order.tech_fee_pct || 0) / 100;\n                    const opsCommission = (order.storage_cost || 0) * (order.ops_fee_pct || 0) / 100;\n                    stats.totalTechCommission += techCommission;\n                    stats.totalOpsCommission += opsCommission;\n                });\n            }\n\n            // 计算存储统计\n            if (productData && !productError) {\n                productData.forEach(product => {\n                    stats.totalStorage += product.total_shares || 0;\n                    stats.soldStorage += product.sold_shares || 0;\n                });\n                stats.remainingStorage = stats.totalStorage - stats.soldStorage;\n            }\n\n            setDashboardStats(stats);\n\n        } catch (error) {\n            console.error('Error fetching dashboard stats:', error);\n        }\n    };\n\n    useEffect(() => {\n        const fetchAgentData = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                console.log('No user found');\n                setLoading(false);\n                return; // User not logged in\n            }\n\n            console.log('Current user:', user.id);\n            console.log('User role from localStorage:', localStorage.getItem('user_role'));\n\n            // Fetch agent profile\n            const { data: profileData, error: profileError } = await supabase\n                .from('agent_profiles')\n                .select('*')\n                .eq('user_id', user.id)\n                .single();\n\n            if (profileError) {\n                console.error('Error fetching agent profile:', profileError);\n                console.error('User ID:', user.id);\n                console.error('Error details:', profileError);\n                setLoading(false);\n                return;\n            }\n\n            console.log('Agent profile data:', profileData);\n            setAgentProfile(profileData);\n\n            // Fetch dashboard statistics\n            await fetchDashboardStats(supabase, user.id, profileData.maker_id);\n            setLoading(false);\n        };\n\n        fetchAgentData();\n    }, []);\n\n    if (loading) {\n        return <div>{t('loading_agent_dashboard')}</div>;\n    }\n\n    if (!agentProfile) {\n        return <div className=\"alert alert-warning\">{t('not_agent')}</div>;\n    }\n\n    return (\n        <Container fluid>\n            <Row className=\"mb-3\">\n                <Col>\n                    <h2>{t('agent_dashboard')}</h2>\n                </Col>\n            </Row>\n\n            {/* 第一行：基本信息 */}\n            <Row>\n                <Col md={4}>\n                    <Card className=\"text-white mb-3\">\n                        <Card.Body>\n                            <Card.Title>{t('brand_name')}</Card.Title>\n                            <h3>{agentProfile.brand_name || 'N/A'}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={4}>\n                    <Card className=\"text-white mb-3\">\n                        <Card.Body>\n                            <Card.Title>{t('commission_rate')}</Card.Title>\n                            <h3>{agentProfile.commission_pct ? `${agentProfile.commission_pct * 100}%` : 'N/A'}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={4}>\n                    <Card className=\"text-white mb-3\">\n                        <Card.Body>\n                            <Card.Title>{t('kyc_status')}</Card.Title>\n                            <h3>{agentProfile.kyc_status || 'N/A'}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            {/* 第二行：佣金统计 */}\n            <Row>\n                <Col md={6}>\n                    <Card className=\"text-white mb-3\">\n                        <Card.Body>\n                            <Card.Title>{t('total_tech_commission')}</Card.Title>\n                            <h3>{dashboardStats.totalTechCommission.toFixed(6)} FIL</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={6}>\n                    <Card className=\"text-white mb-3\">\n                        <Card.Body>\n                            <Card.Title>{t('total_ops_commission')}</Card.Title>\n                            <h3>{dashboardStats.totalOpsCommission.toFixed(6)} FIL</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            {/* 第三行：会员和存储统计 */}\n            <Row>\n                <Col md={3}>\n                    <Card className=\"text-white mb-3\">\n                        <Card.Body>\n                            <Card.Title>{t('member_count')}</Card.Title>\n                            <h3>{dashboardStats.memberCount}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={3}>\n                    <Card className=\"text-white mb-3\">\n                        <Card.Body>\n                            <Card.Title>{t('total_storage')}</Card.Title>\n                            <h3>{dashboardStats.totalStorage.toFixed(2)} TiB</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={3}>\n                    <Card className=\"text-white mb-3\">\n                        <Card.Body>\n                            <Card.Title>{t('sold_storage')}</Card.Title>\n                            <h3>{dashboardStats.soldStorage.toFixed(2)} TiB</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={3}>\n                    <Card className=\"text-dark mb-3\">\n                        <Card.Body>\n                            <Card.Title>{t('remaining_storage')}</Card.Title>\n                            <h3>{dashboardStats.remainingStorage.toFixed(2)} TiB</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            <Row className=\"mt-4\">\n                <Col md={6} className=\"text-center\">\n                    <Card>\n                        <Card.Body>\n                            <h4>{t('member_management')}</h4>\n                            <p>{t('my_subordinate_members')}</p>\n                            <Button as={Link} to=\"/agent/member-list\">{t('enter_member_list')}</Button>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={6} className=\"text-center\">\n                    <Card>\n                        <Card.Body>\n                            <h4>{t('product_management')}</h4>\n                            <p>{t('products_on_sale')}</p>\n                            <Button as={Link} to=\"/agent/products\">{t('browse_agent_products')}</Button>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default AgentDashboard;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,MAAM,KAAQ,iBAAiB,CACnE,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OAASC,WAAW,KAAQ,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnD,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAEC,CAAE,CAAC,CAAGR,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACS,YAAY,CAAEC,eAAe,CAAC,CAAGjB,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAACkB,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACoB,cAAc,CAAEC,iBAAiB,CAAC,CAAGrB,QAAQ,CAAC,CACjDsB,mBAAmB,CAAE,CAAC,CACtBC,kBAAkB,CAAE,CAAC,CACrBC,WAAW,CAAE,CAAC,CACdC,YAAY,CAAE,CAAC,CACfC,WAAW,CAAE,CAAC,CACdC,gBAAgB,CAAE,CACtB,CAAC,CAAC,CAEF,KAAM,CAAAC,mBAAmB,CAAG,KAAAA,CAAOC,QAAQ,CAAEC,WAAW,CAAEC,OAAO,GAAK,CAClE,GAAI,CACA;AACA,KAAM,CAAEC,IAAI,CAAEC,UAAU,CAAEC,KAAK,CAAEC,WAAY,CAAC,CAAG,KAAM,CAAAN,QAAQ,CAC1DO,IAAI,CAAC,mBAAmB,CAAC,CACzBC,MAAM,CAAC,SAAS,CAAE,CAAEC,KAAK,CAAE,OAAQ,CAAC,CAAC,CACrCC,EAAE,CAAC,UAAU,CAAET,WAAW,CAAC,CAEhC;AACA,KAAM,CAAEE,IAAI,CAAEQ,SAAS,CAAEN,KAAK,CAAEO,UAAW,CAAC,CAAG,KAAM,CAAAZ,QAAQ,CACxDO,IAAI,CAAC,QAAQ,CAAC,CACdC,MAAM,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,CAAC,CACDE,EAAE,CAAC,UAAU,CAAET,WAAW,CAAC,CAEhC;AACA,KAAM,CAAEE,IAAI,CAAEU,WAAW,CAAER,KAAK,CAAES,YAAa,CAAC,CAAG,KAAM,CAAAd,QAAQ,CAC5DO,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,2BAA2B,CAAC,CACnCE,EAAE,CAAC,UAAU,CAAER,OAAO,CAAC,CAE5B,GAAI,CAAAa,KAAK,CAAG,CACRtB,mBAAmB,CAAE,CAAC,CACtBC,kBAAkB,CAAE,CAAC,CACrBC,WAAW,CAAES,UAAU,CAAGA,UAAU,CAACY,MAAM,CAAG,CAAC,CAC/CpB,YAAY,CAAE,CAAC,CACfC,WAAW,CAAE,CAAC,CACdC,gBAAgB,CAAE,CACtB,CAAC,CAED;AACA,GAAIa,SAAS,EAAI,CAACC,UAAU,CAAE,CAC1BD,SAAS,CAACM,OAAO,CAACC,KAAK,EAAI,CACvB,KAAM,CAAAC,cAAc,CAAG,CAACD,KAAK,CAACE,YAAY,EAAI,CAAC,GAAKF,KAAK,CAACG,YAAY,EAAI,CAAC,CAAC,CAAG,GAAG,CAClF,KAAM,CAAAC,aAAa,CAAG,CAACJ,KAAK,CAACE,YAAY,EAAI,CAAC,GAAKF,KAAK,CAACK,WAAW,EAAI,CAAC,CAAC,CAAG,GAAG,CAChFR,KAAK,CAACtB,mBAAmB,EAAI0B,cAAc,CAC3CJ,KAAK,CAACrB,kBAAkB,EAAI4B,aAAa,CAC7C,CAAC,CAAC,CACN,CAEA;AACA,GAAIT,WAAW,EAAI,CAACC,YAAY,CAAE,CAC9BD,WAAW,CAACI,OAAO,CAACO,OAAO,EAAI,CAC3BT,KAAK,CAACnB,YAAY,EAAI4B,OAAO,CAACC,YAAY,EAAI,CAAC,CAC/CV,KAAK,CAAClB,WAAW,EAAI2B,OAAO,CAACE,WAAW,EAAI,CAAC,CACjD,CAAC,CAAC,CACFX,KAAK,CAACjB,gBAAgB,CAAGiB,KAAK,CAACnB,YAAY,CAAGmB,KAAK,CAAClB,WAAW,CACnE,CAEAL,iBAAiB,CAACuB,KAAK,CAAC,CAE5B,CAAE,MAAOV,KAAK,CAAE,CACZsB,OAAO,CAACtB,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CAC3D,CACJ,CAAC,CAEDjC,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAwD,cAAc,CAAG,KAAAA,CAAA,GAAY,CAC/B,KAAM,CAAA5B,QAAQ,CAAGpB,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACoB,QAAQ,CAAE,OAEfV,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEa,IAAI,CAAE,CAAE0B,IAAK,CAAE,CAAC,CAAG,KAAM,CAAA7B,QAAQ,CAAC8B,IAAI,CAACC,OAAO,CAAC,CAAC,CAExD,GAAI,CAACF,IAAI,CAAE,CACPF,OAAO,CAACK,GAAG,CAAC,eAAe,CAAC,CAC5B1C,UAAU,CAAC,KAAK,CAAC,CACjB,OAAQ;AACZ,CAEAqC,OAAO,CAACK,GAAG,CAAC,eAAe,CAAEH,IAAI,CAACI,EAAE,CAAC,CACrCN,OAAO,CAACK,GAAG,CAAC,8BAA8B,CAAEE,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC,CAE9E;AACA,KAAM,CAAEhC,IAAI,CAAEiC,WAAW,CAAE/B,KAAK,CAAEgC,YAAa,CAAC,CAAG,KAAM,CAAArC,QAAQ,CAC5DO,IAAI,CAAC,gBAAgB,CAAC,CACtBC,MAAM,CAAC,GAAG,CAAC,CACXE,EAAE,CAAC,SAAS,CAAEmB,IAAI,CAACI,EAAE,CAAC,CACtBK,MAAM,CAAC,CAAC,CAEb,GAAID,YAAY,CAAE,CACdV,OAAO,CAACtB,KAAK,CAAC,+BAA+B,CAAEgC,YAAY,CAAC,CAC5DV,OAAO,CAACtB,KAAK,CAAC,UAAU,CAAEwB,IAAI,CAACI,EAAE,CAAC,CAClCN,OAAO,CAACtB,KAAK,CAAC,gBAAgB,CAAEgC,YAAY,CAAC,CAC7C/C,UAAU,CAAC,KAAK,CAAC,CACjB,OACJ,CAEAqC,OAAO,CAACK,GAAG,CAAC,qBAAqB,CAAEI,WAAW,CAAC,CAC/ChD,eAAe,CAACgD,WAAW,CAAC,CAE5B;AACA,KAAM,CAAArC,mBAAmB,CAACC,QAAQ,CAAE6B,IAAI,CAACI,EAAE,CAAEG,WAAW,CAACG,QAAQ,CAAC,CAClEjD,UAAU,CAAC,KAAK,CAAC,CACrB,CAAC,CAEDsC,cAAc,CAAC,CAAC,CACpB,CAAC,CAAE,EAAE,CAAC,CAEN,GAAIvC,OAAO,CAAE,CACT,mBAAOP,IAAA,QAAA0D,QAAA,CAAMtD,CAAC,CAAC,yBAAyB,CAAC,CAAM,CAAC,CACpD,CAEA,GAAI,CAACC,YAAY,CAAE,CACf,mBAAOL,IAAA,QAAK2D,SAAS,CAAC,qBAAqB,CAAAD,QAAA,CAAEtD,CAAC,CAAC,WAAW,CAAC,CAAM,CAAC,CACtE,CAEA,mBACIF,KAAA,CAACX,SAAS,EAACqE,KAAK,MAAAF,QAAA,eACZ1D,IAAA,CAACR,GAAG,EAACmE,SAAS,CAAC,MAAM,CAAAD,QAAA,cACjB1D,IAAA,CAACP,GAAG,EAAAiE,QAAA,cACA1D,IAAA,OAAA0D,QAAA,CAAKtD,CAAC,CAAC,iBAAiB,CAAC,CAAK,CAAC,CAC9B,CAAC,CACL,CAAC,cAGNF,KAAA,CAACV,GAAG,EAAAkE,QAAA,eACA1D,IAAA,CAACP,GAAG,EAACoE,EAAE,CAAE,CAAE,CAAAH,QAAA,cACP1D,IAAA,CAACN,IAAI,EAACiE,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC7BxD,KAAA,CAACR,IAAI,CAACoE,IAAI,EAAAJ,QAAA,eACN1D,IAAA,CAACN,IAAI,CAACqE,KAAK,EAAAL,QAAA,CAAEtD,CAAC,CAAC,YAAY,CAAC,CAAa,CAAC,cAC1CJ,IAAA,OAAA0D,QAAA,CAAKrD,YAAY,CAAC2D,UAAU,EAAI,KAAK,CAAK,CAAC,EACpC,CAAC,CACV,CAAC,CACN,CAAC,cACNhE,IAAA,CAACP,GAAG,EAACoE,EAAE,CAAE,CAAE,CAAAH,QAAA,cACP1D,IAAA,CAACN,IAAI,EAACiE,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC7BxD,KAAA,CAACR,IAAI,CAACoE,IAAI,EAAAJ,QAAA,eACN1D,IAAA,CAACN,IAAI,CAACqE,KAAK,EAAAL,QAAA,CAAEtD,CAAC,CAAC,iBAAiB,CAAC,CAAa,CAAC,cAC/CJ,IAAA,OAAA0D,QAAA,CAAKrD,YAAY,CAAC4D,cAAc,CAAG,GAAG5D,YAAY,CAAC4D,cAAc,CAAG,GAAG,GAAG,CAAG,KAAK,CAAK,CAAC,EACjF,CAAC,CACV,CAAC,CACN,CAAC,cACNjE,IAAA,CAACP,GAAG,EAACoE,EAAE,CAAE,CAAE,CAAAH,QAAA,cACP1D,IAAA,CAACN,IAAI,EAACiE,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC7BxD,KAAA,CAACR,IAAI,CAACoE,IAAI,EAAAJ,QAAA,eACN1D,IAAA,CAACN,IAAI,CAACqE,KAAK,EAAAL,QAAA,CAAEtD,CAAC,CAAC,YAAY,CAAC,CAAa,CAAC,cAC1CJ,IAAA,OAAA0D,QAAA,CAAKrD,YAAY,CAAC6D,UAAU,EAAI,KAAK,CAAK,CAAC,EACpC,CAAC,CACV,CAAC,CACN,CAAC,EACL,CAAC,cAGNhE,KAAA,CAACV,GAAG,EAAAkE,QAAA,eACA1D,IAAA,CAACP,GAAG,EAACoE,EAAE,CAAE,CAAE,CAAAH,QAAA,cACP1D,IAAA,CAACN,IAAI,EAACiE,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC7BxD,KAAA,CAACR,IAAI,CAACoE,IAAI,EAAAJ,QAAA,eACN1D,IAAA,CAACN,IAAI,CAACqE,KAAK,EAAAL,QAAA,CAAEtD,CAAC,CAAC,uBAAuB,CAAC,CAAa,CAAC,cACrDF,KAAA,OAAAwD,QAAA,EAAKjD,cAAc,CAACE,mBAAmB,CAACwD,OAAO,CAAC,CAAC,CAAC,CAAC,MAAI,EAAI,CAAC,EACrD,CAAC,CACV,CAAC,CACN,CAAC,cACNnE,IAAA,CAACP,GAAG,EAACoE,EAAE,CAAE,CAAE,CAAAH,QAAA,cACP1D,IAAA,CAACN,IAAI,EAACiE,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC7BxD,KAAA,CAACR,IAAI,CAACoE,IAAI,EAAAJ,QAAA,eACN1D,IAAA,CAACN,IAAI,CAACqE,KAAK,EAAAL,QAAA,CAAEtD,CAAC,CAAC,sBAAsB,CAAC,CAAa,CAAC,cACpDF,KAAA,OAAAwD,QAAA,EAAKjD,cAAc,CAACG,kBAAkB,CAACuD,OAAO,CAAC,CAAC,CAAC,CAAC,MAAI,EAAI,CAAC,EACpD,CAAC,CACV,CAAC,CACN,CAAC,EACL,CAAC,cAGNjE,KAAA,CAACV,GAAG,EAAAkE,QAAA,eACA1D,IAAA,CAACP,GAAG,EAACoE,EAAE,CAAE,CAAE,CAAAH,QAAA,cACP1D,IAAA,CAACN,IAAI,EAACiE,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC7BxD,KAAA,CAACR,IAAI,CAACoE,IAAI,EAAAJ,QAAA,eACN1D,IAAA,CAACN,IAAI,CAACqE,KAAK,EAAAL,QAAA,CAAEtD,CAAC,CAAC,cAAc,CAAC,CAAa,CAAC,cAC5CJ,IAAA,OAAA0D,QAAA,CAAKjD,cAAc,CAACI,WAAW,CAAK,CAAC,EAC9B,CAAC,CACV,CAAC,CACN,CAAC,cACNb,IAAA,CAACP,GAAG,EAACoE,EAAE,CAAE,CAAE,CAAAH,QAAA,cACP1D,IAAA,CAACN,IAAI,EAACiE,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC7BxD,KAAA,CAACR,IAAI,CAACoE,IAAI,EAAAJ,QAAA,eACN1D,IAAA,CAACN,IAAI,CAACqE,KAAK,EAAAL,QAAA,CAAEtD,CAAC,CAAC,eAAe,CAAC,CAAa,CAAC,cAC7CF,KAAA,OAAAwD,QAAA,EAAKjD,cAAc,CAACK,YAAY,CAACqD,OAAO,CAAC,CAAC,CAAC,CAAC,MAAI,EAAI,CAAC,EAC9C,CAAC,CACV,CAAC,CACN,CAAC,cACNnE,IAAA,CAACP,GAAG,EAACoE,EAAE,CAAE,CAAE,CAAAH,QAAA,cACP1D,IAAA,CAACN,IAAI,EAACiE,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC7BxD,KAAA,CAACR,IAAI,CAACoE,IAAI,EAAAJ,QAAA,eACN1D,IAAA,CAACN,IAAI,CAACqE,KAAK,EAAAL,QAAA,CAAEtD,CAAC,CAAC,cAAc,CAAC,CAAa,CAAC,cAC5CF,KAAA,OAAAwD,QAAA,EAAKjD,cAAc,CAACM,WAAW,CAACoD,OAAO,CAAC,CAAC,CAAC,CAAC,MAAI,EAAI,CAAC,EAC7C,CAAC,CACV,CAAC,CACN,CAAC,cACNnE,IAAA,CAACP,GAAG,EAACoE,EAAE,CAAE,CAAE,CAAAH,QAAA,cACP1D,IAAA,CAACN,IAAI,EAACiE,SAAS,CAAC,gBAAgB,CAAAD,QAAA,cAC5BxD,KAAA,CAACR,IAAI,CAACoE,IAAI,EAAAJ,QAAA,eACN1D,IAAA,CAACN,IAAI,CAACqE,KAAK,EAAAL,QAAA,CAAEtD,CAAC,CAAC,mBAAmB,CAAC,CAAa,CAAC,cACjDF,KAAA,OAAAwD,QAAA,EAAKjD,cAAc,CAACO,gBAAgB,CAACmD,OAAO,CAAC,CAAC,CAAC,CAAC,MAAI,EAAI,CAAC,EAClD,CAAC,CACV,CAAC,CACN,CAAC,EACL,CAAC,cAENjE,KAAA,CAACV,GAAG,EAACmE,SAAS,CAAC,MAAM,CAAAD,QAAA,eACjB1D,IAAA,CAACP,GAAG,EAACoE,EAAE,CAAE,CAAE,CAACF,SAAS,CAAC,aAAa,CAAAD,QAAA,cAC/B1D,IAAA,CAACN,IAAI,EAAAgE,QAAA,cACDxD,KAAA,CAACR,IAAI,CAACoE,IAAI,EAAAJ,QAAA,eACN1D,IAAA,OAAA0D,QAAA,CAAKtD,CAAC,CAAC,mBAAmB,CAAC,CAAK,CAAC,cACjCJ,IAAA,MAAA0D,QAAA,CAAItD,CAAC,CAAC,wBAAwB,CAAC,CAAI,CAAC,cACpCJ,IAAA,CAACL,MAAM,EAACyE,EAAE,CAAEvE,IAAK,CAACwE,EAAE,CAAC,oBAAoB,CAAAX,QAAA,CAAEtD,CAAC,CAAC,mBAAmB,CAAC,CAAS,CAAC,EACpE,CAAC,CACV,CAAC,CACN,CAAC,cACNJ,IAAA,CAACP,GAAG,EAACoE,EAAE,CAAE,CAAE,CAACF,SAAS,CAAC,aAAa,CAAAD,QAAA,cAC/B1D,IAAA,CAACN,IAAI,EAAAgE,QAAA,cACDxD,KAAA,CAACR,IAAI,CAACoE,IAAI,EAAAJ,QAAA,eACN1D,IAAA,OAAA0D,QAAA,CAAKtD,CAAC,CAAC,oBAAoB,CAAC,CAAK,CAAC,cAClCJ,IAAA,MAAA0D,QAAA,CAAItD,CAAC,CAAC,kBAAkB,CAAC,CAAI,CAAC,cAC9BJ,IAAA,CAACL,MAAM,EAACyE,EAAE,CAAEvE,IAAK,CAACwE,EAAE,CAAC,iBAAiB,CAAAX,QAAA,CAAEtD,CAAC,CAAC,uBAAuB,CAAC,CAAS,CAAC,EACrE,CAAC,CACV,CAAC,CACN,CAAC,EACL,CAAC,EACC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAAD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}