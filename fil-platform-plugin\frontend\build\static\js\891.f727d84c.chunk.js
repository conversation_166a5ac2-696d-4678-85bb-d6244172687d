"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[891],{1068:(e,s,a)=>{a.d(s,{A:()=>d});var r=a(8139),t=a.n(r),l=a(5043),o=a(5673),i=a(7852),n=a(579);const c=l.forwardRef((e,s)=>{let{id:a,bsPrefix:r,className:c,type:d="checkbox",isValid:f=!1,isInvalid:m=!1,as:x="input",...p}=e;const{controlId:N}=(0,l.useContext)(o.A);return r=(0,i.oU)(r,"form-check-input"),(0,n.jsx)(x,{...p,ref:s,type:d,id:a||N,className:t()(c,r,f&&"is-valid",m&&"is-invalid")})});c.displayName="FormCheckInput";const d=c},2663:(e,s,a)=>{a.d(s,{Tj:()=>t,mf:()=>l});var r=a(5043);function t(e,s){let a=0;return r.Children.map(e,e=>r.isValidElement(e)?s(e,a++):e)}function l(e,s){return r.Children.toArray(e).some(e=>r.isValidElement(e)&&e.type===s)}},3722:(e,s,a)=>{a.d(s,{A:()=>E});var r=a(8139),t=a.n(r),l=a(5173),o=a.n(l),i=a(5043),n=a(579);const c={type:o().string,tooltip:o().bool,as:o().elementType},d=i.forwardRef((e,s)=>{let{as:a="div",className:r,type:l="valid",tooltip:o=!1,...i}=e;return(0,n.jsx)(a,{...i,ref:s,className:t()(r,`${l}-${o?"tooltip":"feedback"}`)})});d.displayName="Feedback",d.propTypes=c;const f=d;var m=a(1068),x=a(5673),p=a(7852);const N=i.forwardRef((e,s)=>{let{bsPrefix:a,className:r,htmlFor:l,...o}=e;const{controlId:c}=(0,i.useContext)(x.A);return a=(0,p.oU)(a,"form-check-label"),(0,n.jsx)("label",{...o,ref:s,htmlFor:l||c,className:t()(r,a)})});N.displayName="FormCheckLabel";const u=N;var b=a(2663);const y=i.forwardRef((e,s)=>{let{id:a,bsPrefix:r,bsSwitchPrefix:l,inline:o=!1,reverse:c=!1,disabled:d=!1,isValid:N=!1,isInvalid:y=!1,feedbackTooltip:h=!1,feedback:v,feedbackType:j,className:w,style:g,title:C="",type:P="checkbox",label:$,children:R,as:F="input",...I}=e;r=(0,p.oU)(r,"form-check"),l=(0,p.oU)(l,"form-switch");const{controlId:k}=(0,i.useContext)(x.A),U=(0,i.useMemo)(()=>({controlId:a||k}),[k,a]),A=!R&&null!=$&&!1!==$||(0,b.mf)(R,u),T=(0,n.jsx)(m.A,{...I,type:"switch"===P?"checkbox":P,ref:s,isValid:N,isInvalid:y,disabled:d,as:F});return(0,n.jsx)(x.A.Provider,{value:U,children:(0,n.jsx)("div",{style:g,className:t()(w,A&&r,o&&`${r}-inline`,c&&`${r}-reverse`,"switch"===P&&l),children:R||(0,n.jsxs)(n.Fragment,{children:[T,A&&(0,n.jsx)(u,{title:C,children:$}),v&&(0,n.jsx)(f,{type:j,tooltip:h,children:v})]})})})});y.displayName="FormCheck";const h=Object.assign(y,{Input:m.A,Label:u});a(6440);const v=i.forwardRef((e,s)=>{let{bsPrefix:a,type:r,size:l,htmlSize:o,id:c,className:d,isValid:f=!1,isInvalid:m=!1,plaintext:N,readOnly:u,as:b="input",...y}=e;const{controlId:h}=(0,i.useContext)(x.A);return a=(0,p.oU)(a,"form-control"),(0,n.jsx)(b,{...y,type:r,size:o,ref:s,readOnly:u,id:c||h,className:t()(d,N?`${a}-plaintext`:a,l&&`${a}-${l}`,"color"===r&&`${a}-color`,f&&"is-valid",m&&"is-invalid")})});v.displayName="FormControl";const j=Object.assign(v,{Feedback:f}),w=i.forwardRef((e,s)=>{let{className:a,bsPrefix:r,as:l="div",...o}=e;return r=(0,p.oU)(r,"form-floating"),(0,n.jsx)(l,{ref:s,className:t()(a,r),...o})});w.displayName="FormFloating";const g=w,C=i.forwardRef((e,s)=>{let{controlId:a,as:r="div",...t}=e;const l=(0,i.useMemo)(()=>({controlId:a}),[a]);return(0,n.jsx)(x.A.Provider,{value:l,children:(0,n.jsx)(r,{...t,ref:s})})});C.displayName="FormGroup";const P=C;var $=a(8602);const R=i.forwardRef((e,s)=>{let{as:a="label",bsPrefix:r,column:l=!1,visuallyHidden:o=!1,className:c,htmlFor:d,...f}=e;const{controlId:m}=(0,i.useContext)(x.A);r=(0,p.oU)(r,"form-label");let N="col-form-label";"string"===typeof l&&(N=`${N} ${N}-${l}`);const u=t()(c,r,o&&"visually-hidden",l&&N);return d=d||m,l?(0,n.jsx)($.A,{ref:s,as:"label",className:u,htmlFor:d,...f}):(0,n.jsx)(a,{ref:s,className:u,htmlFor:d,...f})});R.displayName="FormLabel";const F=R,I=i.forwardRef((e,s)=>{let{bsPrefix:a,className:r,id:l,...o}=e;const{controlId:c}=(0,i.useContext)(x.A);return a=(0,p.oU)(a,"form-range"),(0,n.jsx)("input",{...o,type:"range",ref:s,className:t()(r,a),id:l||c})});I.displayName="FormRange";const k=I,U=i.forwardRef((e,s)=>{let{bsPrefix:a,size:r,htmlSize:l,className:o,isValid:c=!1,isInvalid:d=!1,id:f,...m}=e;const{controlId:N}=(0,i.useContext)(x.A);return a=(0,p.oU)(a,"form-select"),(0,n.jsx)("select",{...m,size:l,ref:s,className:t()(o,a,r&&`${a}-${r}`,c&&"is-valid",d&&"is-invalid"),id:f||N})});U.displayName="FormSelect";const A=U,T=i.forwardRef((e,s)=>{let{bsPrefix:a,className:r,as:l="small",muted:o,...i}=e;return a=(0,p.oU)(a,"form-text"),(0,n.jsx)(l,{...i,ref:s,className:t()(r,a,o&&"text-muted")})});T.displayName="FormText";const L=T,O=i.forwardRef((e,s)=>(0,n.jsx)(h,{...e,ref:s,type:"switch"}));O.displayName="Switch";const S=Object.assign(O,{Input:h.Input,Label:h.Label}),V=i.forwardRef((e,s)=>{let{bsPrefix:a,className:r,children:l,controlId:o,label:i,...c}=e;return a=(0,p.oU)(a,"form-floating"),(0,n.jsxs)(P,{ref:s,className:t()(r,a),controlId:o,...c,children:[l,(0,n.jsx)("label",{htmlFor:o,children:i})]})});V.displayName="FloatingLabel";const z=V,H={_ref:o().any,validated:o().bool,as:o().elementType},B=i.forwardRef((e,s)=>{let{className:a,validated:r,as:l="form",...o}=e;return(0,n.jsx)(l,{...o,ref:s,className:t()(a,r&&"was-validated")})});B.displayName="Form",B.propTypes=H;const E=Object.assign(B,{Group:P,Control:j,Floating:g,Check:h,Switch:S,Label:F,Text:L,Range:k,Select:A,FloatingLabel:z})},5673:(e,s,a)=>{a.d(s,{A:()=>r});const r=a(5043).createContext({})},8602:(e,s,a)=>{a.d(s,{A:()=>c});var r=a(8139),t=a.n(r),l=a(5043),o=a(7852),i=a(579);const n=l.forwardRef((e,s)=>{const[{className:a,...r},{as:l="div",bsPrefix:n,spans:c}]=function(e){let{as:s,bsPrefix:a,className:r,...l}=e;a=(0,o.oU)(a,"col");const i=(0,o.gy)(),n=(0,o.Jm)(),c=[],d=[];return i.forEach(e=>{const s=l[e];let r,t,o;delete l[e],"object"===typeof s&&null!=s?({span:r,offset:t,order:o}=s):r=s;const i=e!==n?`-${e}`:"";r&&c.push(!0===r?`${a}${i}`:`${a}${i}-${r}`),null!=o&&d.push(`order${i}-${o}`),null!=t&&d.push(`offset${i}-${t}`)}),[{...l,className:t()(r,...c,...d)},{as:s,bsPrefix:a,spans:c}]}(e);return(0,i.jsx)(l,{...r,ref:s,className:t()(a,!c.length&&n)})});n.displayName="Col";const c=n},8628:(e,s,a)=>{a.d(s,{A:()=>U});var r=a(8139),t=a.n(r),l=a(5043),o=a(7852),i=a(579);const n=l.forwardRef((e,s)=>{let{className:a,bsPrefix:r,as:l="div",...n}=e;return r=(0,o.oU)(r,"card-body"),(0,i.jsx)(l,{ref:s,className:t()(a,r),...n})});n.displayName="CardBody";const c=n,d=l.forwardRef((e,s)=>{let{className:a,bsPrefix:r,as:l="div",...n}=e;return r=(0,o.oU)(r,"card-footer"),(0,i.jsx)(l,{ref:s,className:t()(a,r),...n})});d.displayName="CardFooter";const f=d;var m=a(1778);const x=l.forwardRef((e,s)=>{let{bsPrefix:a,className:r,as:n="div",...c}=e;const d=(0,o.oU)(a,"card-header"),f=(0,l.useMemo)(()=>({cardHeaderBsPrefix:d}),[d]);return(0,i.jsx)(m.A.Provider,{value:f,children:(0,i.jsx)(n,{ref:s,...c,className:t()(r,d)})})});x.displayName="CardHeader";const p=x,N=l.forwardRef((e,s)=>{let{bsPrefix:a,className:r,variant:l,as:n="img",...c}=e;const d=(0,o.oU)(a,"card-img");return(0,i.jsx)(n,{ref:s,className:t()(l?`${d}-${l}`:d,r),...c})});N.displayName="CardImg";const u=N,b=l.forwardRef((e,s)=>{let{className:a,bsPrefix:r,as:l="div",...n}=e;return r=(0,o.oU)(r,"card-img-overlay"),(0,i.jsx)(l,{ref:s,className:t()(a,r),...n})});b.displayName="CardImgOverlay";const y=b,h=l.forwardRef((e,s)=>{let{className:a,bsPrefix:r,as:l="a",...n}=e;return r=(0,o.oU)(r,"card-link"),(0,i.jsx)(l,{ref:s,className:t()(a,r),...n})});h.displayName="CardLink";const v=h;var j=a(4488);const w=(0,j.A)("h6"),g=l.forwardRef((e,s)=>{let{className:a,bsPrefix:r,as:l=w,...n}=e;return r=(0,o.oU)(r,"card-subtitle"),(0,i.jsx)(l,{ref:s,className:t()(a,r),...n})});g.displayName="CardSubtitle";const C=g,P=l.forwardRef((e,s)=>{let{className:a,bsPrefix:r,as:l="p",...n}=e;return r=(0,o.oU)(r,"card-text"),(0,i.jsx)(l,{ref:s,className:t()(a,r),...n})});P.displayName="CardText";const $=P,R=(0,j.A)("h5"),F=l.forwardRef((e,s)=>{let{className:a,bsPrefix:r,as:l=R,...n}=e;return r=(0,o.oU)(r,"card-title"),(0,i.jsx)(l,{ref:s,className:t()(a,r),...n})});F.displayName="CardTitle";const I=F,k=l.forwardRef((e,s)=>{let{bsPrefix:a,className:r,bg:l,text:n,border:d,body:f=!1,children:m,as:x="div",...p}=e;const N=(0,o.oU)(a,"card");return(0,i.jsx)(x,{ref:s,...p,className:t()(r,N,l&&`bg-${l}`,n&&`text-${n}`,d&&`border-${d}`),children:f?(0,i.jsx)(c,{children:m}):m})});k.displayName="Card";const U=Object.assign(k,{Img:u,Title:I,Subtitle:C,Body:c,Link:v,Text:$,Header:p,Footer:f,ImgOverlay:y})}}]);
//# sourceMappingURL=891.f727d84c.chunk.js.map