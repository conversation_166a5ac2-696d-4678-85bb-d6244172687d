import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const NetworkStats = () => {
    const { t } = useTranslation();
    const [stats, setStats] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchStats = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // Fetch network stats
            const { data, error } = await supabase
                .from('network_stats')
                .select(`
                    stat_date,
                    blockchain_height,
                    fil_per_tib
                `)
                .order('stat_date', { ascending: false });

            if (error) {
                console.error('Error fetching network stats:', error);
            } else {
                setStats(data);
            }
            setLoading(false);
        };

        fetchStats();
    }, []);

    if (loading) {
        return <div>{t('loading_network_stats')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('network_stats')}</h2>
            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('stat_date')}</th>
                                        <th>{t('blockchain_height')}</th>
                                        <th>{t('fil_per_tib')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {stats.length === 0 ? (
                                        <tr>
                                            <td colSpan="3" className="text-center">{t('no_network_stats_available')}</td>
                                        </tr>
                                    ) : (
                                        stats.map((stat, index) => (
                                            <tr key={`${stat.stat_date}`}>
                                                <td>{new Date(stat.stat_date).toLocaleDateString()}</td>
                                                <td>{stat.blockchain_height ? stat.blockchain_height.toLocaleString() : '-'}</td>
                                                <td>{stat.fil_per_tib ? Number(stat.fil_per_tib).toFixed(8) : '0'} FIL/TiB</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default NetworkStats;
