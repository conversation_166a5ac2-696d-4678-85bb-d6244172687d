"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[434],{1072:(e,s,r)=>{r.d(s,{A:()=>n});var a=r(8139),t=r.n(a),d=r(5043),i=r(7852),c=r(579);const l=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:d="div",...l}=e;const n=(0,i.oU)(r,"row"),o=(0,i.gy)(),f=(0,i.Jm)(),m=`${n}-cols`,x=[];return o.forEach(e=>{const s=l[e];let r;delete l[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const a=e!==f?`-${e}`:"";null!=r&&x.push(`${m}${a}-${r}`)}),(0,c.jsx)(d,{ref:s,...l,className:t()(a,n,...x)})});l.displayName="Row";const n=l},4196:(e,s,r)=>{r.d(s,{A:()=>n});var a=r(8139),t=r.n(a),d=r(5043),i=r(7852),c=r(579);const l=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,striped:d,bordered:l,borderless:n,hover:o,size:f,variant:m,responsive:x,...h}=e;const u=(0,i.oU)(r,"table"),j=t()(a,u,m&&`${u}-${m}`,f&&`${u}-${f}`,d&&`${u}-${"string"===typeof d?`striped-${d}`:"striped"}`,l&&`${u}-bordered`,n&&`${u}-borderless`,o&&`${u}-hover`),N=(0,c.jsx)("table",{...h,className:j,ref:s});if(x){let e=`${u}-responsive`;return"string"===typeof x&&(e=`${e}-${x}`),(0,c.jsx)("div",{className:e,children:N})}return N});l.displayName="Table";const n=l},7434:(e,s,r)=>{r.r(s),r.d(s,{default:()=>x});var a=r(5043),t=r(3519),d=r(1072),i=r(8602),c=r(8628),l=r(4282),n=r(4196),o=r(4312),f=r(4117),m=r(579);const x=()=>{const{t:e}=(0,f.Bd)(),[s,r]=(0,a.useState)([]),[x,h]=(0,a.useState)(!0),[u,j]=(0,a.useState)("");(0,a.useEffect)(()=>{(async()=>{const e=(0,o.b)();if(!e)return;h(!0);const{data:{user:s}}=await e.auth.getUser();if(!s)return void h(!1);const{data:a,error:t}=await e.from("users").select("invite_code").eq("id",s.id).single();t?console.error("Error fetching invite code:",t):a&&j(a.invite_code);const{data:d,error:i}=await e.from("users").select("id, email, created_at").eq("referred_by",s.id).order("created_at",{ascending:!1});i?console.error("Error fetching referrals:",i):r(d),h(!1)})()},[]);return x?(0,m.jsx)("div",{children:e("loading_referral_data")}):(0,m.jsxs)(t.A,{children:[(0,m.jsx)("h2",{className:"mb-4",children:e("my_recommendations")}),(0,m.jsx)(d.A,{className:"mb-4",children:(0,m.jsx)(i.A,{children:(0,m.jsx)(c.A,{children:(0,m.jsxs)(c.A.Body,{children:[(0,m.jsx)(c.A.Title,{children:e("my_invite_code")}),(0,m.jsx)("p",{className:"lead",children:(0,m.jsx)("strong",{children:u||"N/A"})}),u&&(0,m.jsx)(l.A,{variant:"primary",onClick:()=>{navigator.clipboard.writeText(u),alert(e("invite_code_copied"))},children:e("copy_invite_code")}),(0,m.jsx)("p",{className:"mt-3 text-muted",children:e("share_invite_description")})]})})})}),(0,m.jsx)(d.A,{children:(0,m.jsx)(i.A,{children:(0,m.jsx)(c.A,{children:(0,m.jsxs)(c.A.Body,{children:[(0,m.jsx)(c.A.Title,{children:e("my_subordinate_users")}),(0,m.jsxs)(n.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,m.jsx)("thead",{children:(0,m.jsxs)("tr",{children:[(0,m.jsx)("th",{children:e("user_id")}),(0,m.jsx)("th",{children:e("email")}),(0,m.jsx)("th",{children:e("registration_time")})]})}),(0,m.jsx)("tbody",{children:0===s.length?(0,m.jsx)("tr",{children:(0,m.jsx)("td",{colSpan:"3",className:"text-center",children:e("no_subordinate_users")})}):s.map(e=>(0,m.jsxs)("tr",{children:[(0,m.jsxs)("td",{children:[e.id.substring(0,8),"..."]}),(0,m.jsx)("td",{children:e.email}),(0,m.jsx)("td",{children:new Date(e.created_at).toLocaleString()})]},e.id))})]})]})})})})]})}},8602:(e,s,r)=>{r.d(s,{A:()=>n});var a=r(8139),t=r.n(a),d=r(5043),i=r(7852),c=r(579);const l=d.forwardRef((e,s)=>{const[{className:r,...a},{as:d="div",bsPrefix:l,spans:n}]=function(e){let{as:s,bsPrefix:r,className:a,...d}=e;r=(0,i.oU)(r,"col");const c=(0,i.gy)(),l=(0,i.Jm)(),n=[],o=[];return c.forEach(e=>{const s=d[e];let a,t,i;delete d[e],"object"===typeof s&&null!=s?({span:a,offset:t,order:i}=s):a=s;const c=e!==l?`-${e}`:"";a&&n.push(!0===a?`${r}${c}`:`${r}${c}-${a}`),null!=i&&o.push(`order${c}-${i}`),null!=t&&o.push(`offset${c}-${t}`)}),[{...d,className:t()(a,...n,...o)},{as:s,bsPrefix:r,spans:n}]}(e);return(0,c.jsx)(d,{...a,ref:s,className:t()(r,!n.length&&l)})});l.displayName="Col";const n=l},8628:(e,s,r)=>{r.d(s,{A:()=>k});var a=r(8139),t=r.n(a),d=r(5043),i=r(7852),c=r(579);const l=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...l}=e;return a=(0,i.oU)(a,"card-body"),(0,c.jsx)(d,{ref:s,className:t()(r,a),...l})});l.displayName="CardBody";const n=l,o=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...l}=e;return a=(0,i.oU)(a,"card-footer"),(0,c.jsx)(d,{ref:s,className:t()(r,a),...l})});o.displayName="CardFooter";const f=o;var m=r(1778);const x=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:l="div",...n}=e;const o=(0,i.oU)(r,"card-header"),f=(0,d.useMemo)(()=>({cardHeaderBsPrefix:o}),[o]);return(0,c.jsx)(m.A.Provider,{value:f,children:(0,c.jsx)(l,{ref:s,...n,className:t()(a,o)})})});x.displayName="CardHeader";const h=x,u=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,variant:d,as:l="img",...n}=e;const o=(0,i.oU)(r,"card-img");return(0,c.jsx)(l,{ref:s,className:t()(d?`${o}-${d}`:o,a),...n})});u.displayName="CardImg";const j=u,N=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...l}=e;return a=(0,i.oU)(a,"card-img-overlay"),(0,c.jsx)(d,{ref:s,className:t()(r,a),...l})});N.displayName="CardImgOverlay";const b=N,p=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="a",...l}=e;return a=(0,i.oU)(a,"card-link"),(0,c.jsx)(d,{ref:s,className:t()(r,a),...l})});p.displayName="CardLink";const v=p;var y=r(4488);const $=(0,y.A)("h6"),g=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d=$,...l}=e;return a=(0,i.oU)(a,"card-subtitle"),(0,c.jsx)(d,{ref:s,className:t()(r,a),...l})});g.displayName="CardSubtitle";const _=g,w=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="p",...l}=e;return a=(0,i.oU)(a,"card-text"),(0,c.jsx)(d,{ref:s,className:t()(r,a),...l})});w.displayName="CardText";const A=w,P=(0,y.A)("h5"),C=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d=P,...l}=e;return a=(0,i.oU)(a,"card-title"),(0,c.jsx)(d,{ref:s,className:t()(r,a),...l})});C.displayName="CardTitle";const R=C,U=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,bg:d,text:l,border:o,body:f=!1,children:m,as:x="div",...h}=e;const u=(0,i.oU)(r,"card");return(0,c.jsx)(x,{ref:s,...h,className:t()(a,u,d&&`bg-${d}`,l&&`text-${l}`,o&&`border-${o}`),children:f?(0,c.jsx)(n,{children:m}):m})});U.displayName="Card";const k=Object.assign(U,{Img:j,Title:R,Subtitle:_,Body:n,Link:v,Text:A,Header:h,Footer:f,ImgOverlay:b})}}]);
//# sourceMappingURL=434.b0fccb96.chunk.js.map