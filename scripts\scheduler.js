#!/usr/bin/env node

/**
 * <PERSON>ron Scheduler for Filfox Scraper
 * 
 * This script runs the filfox scraper on a schedule
 * Default: Every day at 2:00 AM Tokyo time (JST)
 */

const cron = require('node-cron');
const { spawn } = require('child_process');
const path = require('path');
require('dotenv').config();

// Set timezone to Tokyo
process.env.TZ = 'Asia/Tokyo';

function log(message) {
    const timestamp = new Date().toLocaleString('en-US', { timeZone: 'Asia/Tokyo' });
    console.log(`[${timestamp} JST] ${message}`);
}

function logError(message, error) {
    const timestamp = new Date().toLocaleString('en-US', { timeZone: 'Asia/Tokyo' });
    console.error(`[${timestamp} JST] ERROR: ${message}`, error);
}

/**
 * Run the scraper script
 */
function runScraper() {
    return new Promise((resolve, reject) => {
        log('Starting filfox scraper...');
        
        const scraperPath = path.join(__dirname, 'filfox-scraper.js');
        const child = spawn('node', [scraperPath], {
            stdio: 'pipe',
            env: process.env
        });

        let stdout = '';
        let stderr = '';

        child.stdout.on('data', (data) => {
            const output = data.toString();
            stdout += output;
            console.log(output.trim());
        });

        child.stderr.on('data', (data) => {
            const output = data.toString();
            stderr += output;
            console.error(output.trim());
        });

        child.on('close', (code) => {
            if (code === 0) {
                log('Scraper completed successfully');
                resolve({ code, stdout, stderr });
            } else {
                logError(`Scraper failed with exit code ${code}`, stderr);
                reject(new Error(`Scraper failed with exit code ${code}`));
            }
        });

        child.on('error', (error) => {
            logError('Failed to start scraper process', error);
            reject(error);
        });
    });
}

/**
 * Scheduled task function
 */
async function scheduledTask() {
    try {
        log('=== Scheduled Filfox Scraper Task Started ===');
        await runScraper();
        log('=== Scheduled Task Completed Successfully ===');
    } catch (error) {
        logError('Scheduled task failed', error);
    }
}

/**
 * Main function
 */
function main() {
    log('Filfox Scraper Scheduler started');
    log('Timezone: ' + process.env.TZ);
    log('Current time: ' + new Date().toLocaleString('en-US', { timeZone: 'Asia/Tokyo' }));
    
    // Schedule the task to run every day at 2:00 AM JST
    // Cron format: second minute hour day month dayOfWeek
    // '0 2 * * *' means: at 2:00 AM every day
    const cronExpression = '0 2 * * *';
    
    log(`Scheduling scraper to run daily at 2:00 AM JST (cron: ${cronExpression})`);
    
    cron.schedule(cronExpression, scheduledTask, {
        scheduled: true,
        timezone: 'Asia/Tokyo'
    });

    // Optional: Run immediately for testing (comment out in production)
    if (process.argv.includes('--run-now')) {
        log('Running scraper immediately for testing...');
        scheduledTask();
    }

    // Keep the process running
    log('Scheduler is running. Press Ctrl+C to stop.');
    
    // Graceful shutdown
    process.on('SIGINT', () => {
        log('Received SIGINT. Shutting down gracefully...');
        process.exit(0);
    });

    process.on('SIGTERM', () => {
        log('Received SIGTERM. Shutting down gracefully...');
        process.exit(0);
    });
}

// Run the scheduler
if (require.main === module) {
    main();
}
