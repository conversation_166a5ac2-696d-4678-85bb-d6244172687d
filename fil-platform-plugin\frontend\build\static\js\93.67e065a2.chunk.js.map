{"version": 3, "file": "static/js/93.67e065a2.chunk.js", "mappings": "qMAOA,MAAMA,GAAgBC,EAAAA,EAAAA,GAAiB,MACvCD,EAAcE,YAAc,gBAC5B,MAAMC,EAA4BC,EAAAA,WAAiB,CAAAC,EAKhDC,KAAQ,IALyC,UAClDC,EAAS,SACTC,EACAC,GAAIC,EAAYV,KACbW,GACJN,EAEC,OADAG,GAAWI,EAAAA,EAAAA,IAAmBJ,EAAU,kBACpBK,EAAAA,EAAAA,KAAKH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,MAC9BG,MAGPR,EAAaD,YAAc,eAC3B,U,cChBA,MAAMa,EAAyBX,EAAAA,WAAiB,CAAAC,EAK7CC,KAAQ,IALsC,UAC/CC,EAAS,SACTC,EACAC,GAAIC,EAAYM,EAAAA,KACbL,GACJN,EAEC,OADAG,GAAWI,EAAAA,EAAAA,IAAmBJ,EAAU,eACpBK,EAAAA,EAAAA,KAAKH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,MAC9BG,MAGPI,EAAUb,YAAc,YACxB,U,wBCRA,MAAMe,EAAqBb,EAAAA,WAAiB,CAACc,EAAmBZ,KAC9D,MAAM,SACJE,EAAQ,KACRW,GAAO,EAAI,WACXC,EAAa,cAAa,aAC1BC,EAAY,UACZd,EAAS,SACTe,EAAQ,QACRC,EAAU,UAAS,QACnBC,EAAO,YACPC,EAAW,WACXC,EAAaC,EAAAA,KACVhB,IACDiB,EAAAA,EAAAA,IAAgBV,EAAmB,CACrCC,KAAM,YAEFU,GAASjB,EAAAA,EAAAA,IAAmBJ,EAAU,SACtCsB,GAAcC,EAAAA,EAAAA,GAAiBC,IAC/BR,GACFA,GAAQ,EAAOQ,KAGbC,GAA4B,IAAfP,EAAsBC,EAAAA,EAAOD,EAC1CQ,GAAqBC,EAAAA,EAAAA,MAAM,MAAO,CACtCC,KAAM,WACDH,OAAqBI,EAAR1B,EAClBL,IAAKA,EACLC,UAAWO,IAAWP,EAAWsB,EAAQN,GAAW,GAAGM,KAAUN,IAAWE,GAAe,GAAGI,iBAC9FP,SAAU,CAACG,IAA4BZ,EAAAA,EAAAA,KAAKyB,EAAAA,EAAa,CACvDC,QAAST,EACT,aAAcV,EACdG,QAASF,IACPC,KAEN,OAAKW,GACepB,EAAAA,EAAAA,KAAKoB,EAAY,CACnCO,eAAe,KACZ7B,EACHL,SAAK+B,EACLI,GAAItB,EACJG,SAAUY,IANYf,EAAOe,EAAQ,OASzCjB,EAAMf,YAAc,QACpB,QAAewC,OAAOC,OAAO1B,EAAO,CAClC2B,KAAM7B,EACN8B,QAAS1C,G,4ICrDX,MAyGA,EAzGkB2C,KAChB,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAAOC,IAAYC,EAAAA,EAAAA,UAAS,KAC5BC,EAAUC,IAAeF,EAAAA,EAAAA,UAAS,KAClCG,EAAOC,IAAYJ,EAAAA,EAAAA,UAAS,KAC5BK,EAASC,IAAcN,EAAAA,EAAAA,WAAS,GACjCO,GAAWC,EAAAA,EAAAA,MA2DjB,OACE9C,EAAAA,EAAAA,KAAA,OACEN,UAAU,mDACVqD,MAAO,CAAEC,UAAW,QAASvC,UAE7BT,EAAAA,EAAAA,KAACiD,EAAAA,EAAI,CAACF,MAAO,CAAEG,MAAO,SAAUzC,UAC9Ba,EAAAA,EAAAA,MAAC2B,EAAAA,EAAKE,KAAI,CAAA1C,SAAA,EACRT,EAAAA,EAAAA,KAAA,MAAIN,UAAU,mBAAkBe,SAAEyB,EAAE,WACnCO,IAASzC,EAAAA,EAAAA,KAACI,EAAAA,EAAK,CAACM,QAAQ,SAAQD,SAAEgC,KACnCnB,EAAAA,EAAAA,MAAC8B,EAAAA,EAAI,CAACC,SAlEOC,UACnBnC,EAAEoC,iBACFb,EAAS,IACTE,GAAW,GAEX,IAAK,IAADY,EACF,MAAMC,GAAWC,EAAAA,EAAAA,MAGTjB,MAAOkB,SAAoBF,EAASG,KAAKC,mBAAmB,CAClEzB,QACAG,aAEF,GAAIoB,EAAW,MAAMA,EAGrB,MACEG,MAAM,KAAEC,GACRtB,MAAOuB,SACCP,EAASG,KAAKK,UACxB,GAAID,EAAS,MAAMA,EAEnB,IAAIzC,EAAW,OAAJwC,QAAI,IAAJA,GAAmB,QAAfP,EAAJO,EAAMG,qBAAa,IAAAV,OAAf,EAAJA,EAAqBjC,KAGhC,IAAKA,EAAM,CACT,MAAM,KAAEuC,EAAMrB,MAAO0B,SAAqBV,EACvCW,KAAK,SACLC,OAAO,QACPC,GAAG,KAAMP,EAAKQ,IACdC,SACH,GAAIL,EAAY,MAAMA,EACtB5C,EAAOuC,EAAKvC,IACd,CAMA,OAHAkD,aAAaC,QAAQ,YAAanD,GAG1BA,GACN,IAAK,QACHsB,EAAS,SAAU,CAAE8B,SAAS,IAC9B,MACF,IAAK,QACH9B,EAAS,SAAU,CAAE8B,SAAS,IAC9B,MACF,QACE9B,EAAS,IAAK,CAAE8B,SAAS,IAE/B,CAAE,MAAOC,GACPC,QAAQpC,MAAM,eAAgBmC,GAC9BlC,EAASkC,EAAIE,SAAW5C,EAAE,gBAC5B,CAEAU,GAAW,IAYwBnC,SAAA,EAC3Ba,EAAAA,EAAAA,MAAC8B,EAAAA,EAAK2B,MAAK,CAACR,GAAG,QAAQ7E,UAAU,OAAMe,SAAA,EACrCT,EAAAA,EAAAA,KAACoD,EAAAA,EAAK4B,MAAK,CAAAvE,SAAEyB,EAAE,oBACflC,EAAAA,EAAAA,KAACoD,EAAAA,EAAK6B,QAAO,CACXC,KAAK,QACLC,UAAQ,EACRC,MAAOhD,EACPiD,SAAWlE,GAAMkB,EAASlB,EAAEmE,OAAOF,aAIvC9D,EAAAA,EAAAA,MAAC8B,EAAAA,EAAK2B,MAAK,CAACR,GAAG,WAAW7E,UAAU,OAAMe,SAAA,EACxCT,EAAAA,EAAAA,KAACoD,EAAAA,EAAK4B,MAAK,CAAAvE,SAAEyB,EAAE,eACflC,EAAAA,EAAAA,KAACoD,EAAAA,EAAK6B,QAAO,CACXC,KAAK,WACLC,UAAQ,EACRC,MAAO7C,EACP8C,SAAWlE,GAAMqB,EAAYrB,EAAEmE,OAAOF,aAI1CpF,EAAAA,EAAAA,KAACuF,EAAAA,EAAM,CAACC,SAAU7C,EAASjD,UAAU,QAAQwF,KAAK,SAAQzE,SAC7CyB,EAAVS,EAAY,aAAkB,qB", "sources": ["../node_modules/react-bootstrap/esm/AlertHeading.js", "../node_modules/react-bootstrap/esm/AlertLink.js", "../node_modules/react-bootstrap/esm/Alert.js", "pages/LoginPage.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH4 = divWithClassName('h4');\nDivStyledAsH4.displayName = 'DivStyledAsH4';\nconst AlertHeading = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH4,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'alert-heading');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nAlertHeading.displayName = 'AlertHeading';\nexport default AlertHeading;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Anchor from '@restart/ui/Anchor';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst AlertLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = Anchor,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'alert-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nAlertLink.displayName = 'AlertLink';\nexport default AlertLink;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useUncontrolled } from 'uncontrollable';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AlertHeading from './AlertHeading';\nimport AlertLink from './AlertLink';\nimport Fade from './Fade';\nimport CloseButton from './CloseButton';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst Alert = /*#__PURE__*/React.forwardRef((uncontrolledProps, ref) => {\n  const {\n    bsPrefix,\n    show = true,\n    closeLabel = 'Close alert',\n    closeVariant,\n    className,\n    children,\n    variant = 'primary',\n    onClose,\n    dismissible,\n    transition = Fade,\n    ...props\n  } = useUncontrolled(uncontrolledProps, {\n    show: 'onClose'\n  });\n  const prefix = useBootstrapPrefix(bsPrefix, 'alert');\n  const handleClose = useEventCallback(e => {\n    if (onClose) {\n      onClose(false, e);\n    }\n  });\n  const Transition = transition === true ? Fade : transition;\n  const alert = /*#__PURE__*/_jsxs(\"div\", {\n    role: \"alert\",\n    ...(!Transition ? props : undefined),\n    ref: ref,\n    className: classNames(className, prefix, variant && `${prefix}-${variant}`, dismissible && `${prefix}-dismissible`),\n    children: [dismissible && /*#__PURE__*/_jsx(CloseButton, {\n      onClick: handleClose,\n      \"aria-label\": closeLabel,\n      variant: closeVariant\n    }), children]\n  });\n  if (!Transition) return show ? alert : null;\n  return /*#__PURE__*/_jsx(Transition, {\n    unmountOnExit: true,\n    ...props,\n    ref: undefined,\n    in: show,\n    children: alert\n  });\n});\nAlert.displayName = 'Alert';\nexport default Object.assign(Alert, {\n  Link: AlertLink,\n  Heading: AlertHeading\n});", "import React, { useState } from 'react';\nimport { <PERSON>, Button, Card, Alert } from 'react-bootstrap';\nimport { useTranslation } from 'react-i18next';\nimport { getSupabase } from '../supabaseClient';\nimport { useNavigate } from 'react-router-dom';\n\nconst LoginPage = () => {\n  const { t } = useTranslation();\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setError('');\n    setLoading(true);\n\n    try {\n      const supabase = getSupabase();\n\n      /* ① 登录 */\n      const { error: signError } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      });\n      if (signError) throw signError;\n\n      /* ② 取当前用户 & role */\n      const {\n        data: { user },\n        error: userErr,\n      } = await supabase.auth.getUser();\n      if (userErr) throw userErr;\n\n      let role = user?.user_metadata?.role;\n\n      // 如果 user_metadata 里没有 role，就去 public.users 表查询\n      if (!role) {\n        const { data, error: profileErr } = await supabase\n          .from('users')\n          .select('role')\n          .eq('id', user.id)\n          .single();\n        if (profileErr) throw profileErr;\n        role = data.role;\n      }\n\n      /* ③ 把 role 存到 localStorage，供前端使用 */\n      localStorage.setItem('user_role', role);\n\n      /* ④ 根据 role 重定向 */\n      switch (role) {\n        case 'maker':\n          navigate('/maker', { replace: true });\n          break;\n        case 'agent':\n          navigate('/agent', { replace: true });\n          break;\n        default:\n          navigate('/', { replace: true }); // customer\n      }\n    } catch (err) {\n      console.error('Login Error:', err);\n      setError(err.message || t('login_failed'));\n    }\n\n    setLoading(false);\n  };\n\n  return (\n    <div\n      className=\"d-flex justify-content-center align-items-center\"\n      style={{ minHeight: '80vh' }}\n    >\n      <Card style={{ width: '400px' }}>\n        <Card.Body>\n          <h2 className=\"text-center mb-4\">{t('login')}</h2>\n          {error && <Alert variant=\"danger\">{error}</Alert>}\n          <Form onSubmit={handleSubmit}>\n            <Form.Group id=\"email\" className=\"mb-3\">\n              <Form.Label>{t('email_address')}</Form.Label>\n              <Form.Control\n                type=\"email\"\n                required\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n              />\n            </Form.Group>\n\n            <Form.Group id=\"password\" className=\"mb-3\">\n              <Form.Label>{t('password')}</Form.Label>\n              <Form.Control\n                type=\"password\"\n                required\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n              />\n            </Form.Group>\n\n            <Button disabled={loading} className=\"w-100\" type=\"submit\">\n              {loading ? t('logging_in') : t('login')}\n            </Button>\n          </Form>\n        </Card.Body>\n      </Card>\n    </div>\n  );\n};\n\nexport default LoginPage;"], "names": ["DivStyledAsH4", "divWithClassName", "displayName", "AlertHeading", "React", "_ref", "ref", "className", "bsPrefix", "as", "Component", "props", "useBootstrapPrefix", "_jsx", "classNames", "AlertLink", "<PERSON><PERSON>", "<PERSON><PERSON>", "uncontrolledProps", "show", "<PERSON><PERSON><PERSON><PERSON>", "closeVariant", "children", "variant", "onClose", "dismissible", "transition", "Fade", "useUncontrolled", "prefix", "handleClose", "useEventCallback", "e", "Transition", "alert", "_jsxs", "role", "undefined", "CloseButton", "onClick", "unmountOnExit", "in", "Object", "assign", "Link", "Heading", "LoginPage", "t", "useTranslation", "email", "setEmail", "useState", "password", "setPassword", "error", "setError", "loading", "setLoading", "navigate", "useNavigate", "style", "minHeight", "Card", "width", "Body", "Form", "onSubmit", "async", "preventDefault", "_user$user_metadata", "supabase", "getSupabase", "signError", "auth", "signInWithPassword", "data", "user", "userErr", "getUser", "user_metadata", "profileErr", "from", "select", "eq", "id", "single", "localStorage", "setItem", "replace", "err", "console", "message", "Group", "Label", "Control", "type", "required", "value", "onChange", "target", "<PERSON><PERSON>", "disabled"], "sourceRoot": ""}