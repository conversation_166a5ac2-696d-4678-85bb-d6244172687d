version: '3.8'

services:
  filfox-scraper:
    build: .
    container_name: filfox-scraper
    restart: unless-stopped
    environment:
      - TZ=Asia/Tokyo
      - NODE_ENV=production
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
    networks:
      - scraper-network
    healthcheck:
      test: ["CMD", "node", "-e", "console.log('Health check')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  scraper-network:
    driver: bridge

volumes:
  logs:
