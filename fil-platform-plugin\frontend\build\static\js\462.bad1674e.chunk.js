"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[462],{1072:(e,r,s)=>{s.d(r,{A:()=>d});var a=s(8139),t=s.n(a),l=s(5043),n=s(7852),i=s(579);const c=l.forwardRef((e,r)=>{let{bsPrefix:s,className:a,as:l="div",...c}=e;const d=(0,n.oU)(s,"row"),o=(0,n.gy)(),h=(0,n.Jm)(),m=`${d}-cols`,f=[];return o.forEach(e=>{const r=c[e];let s;delete c[e],null!=r&&"object"===typeof r?({cols:s}=r):s=r;const a=e!==h?`-${e}`:"";null!=s&&f.push(`${m}${a}-${s}`)}),(0,i.jsx)(l,{ref:r,...c,className:t()(a,d,...f)})});c.displayName="Row";const d=c},1462:(e,r,s)=>{s.r(r),s.d(r,{default:()=>A});var a=s(5043),t=s(3519),l=s(8139),n=s.n(l),i=s(7852),c=s(579);const d=a.forwardRef((e,r)=>{let{bsPrefix:s,variant:a,animation:t="border",size:l,as:d="div",className:o,...h}=e;s=(0,i.oU)(s,"spinner");const m=`${s}-${t}`;return(0,c.jsx)(d,{ref:r,...h,className:n()(o,m,l&&`${m}-${l}`,a&&`text-${a}`)})});d.displayName="Spinner";const o=d;var h=s(1719),m=s(1072),f=s(8602),x=s(8628),u=s(3722),j=s(4282),_=s(3204),g=s(4312),p=s(4117);const A=()=>{const{t:e}=(0,p.Bd)(),[r,s]=(0,a.useState)(!0),[l,n]=(0,a.useState)([]),[i,d]=(0,a.useState)([]),[A,N]=(0,a.useState)(new Set),[v,b]=(0,a.useState)(null),[y,w]=(0,a.useState)("");(0,a.useEffect)(()=>{(async()=>{const r=(0,g.b)();if(!r)return;s(!0),b(null);let a=null;try{const{data:{user:t}}=await r.auth.getUser();if(a=t,!t)return b(e("user_not_logged_in")),void s(!1);const{data:l,error:i}=await r.from("agent_profiles").select("maker_id").eq("user_id",t.id).single();if(i)return console.error("Error fetching agent profile:",i),b(e("agent_profile_not_found")),void s(!1);const{data:c,error:o}=await r.from("customer_profiles").select("user_id").eq("agent_id",t.id).limit(100);if(o)return console.error("Error fetching customer profiles:",o),b(e("failed_to_load_referral_data")),void s(!1);const h=c.map(e=>e.user_id).filter(Boolean),{data:m,error:f}=await r.from("users").select("id, email, referred_by, created_at, role").in("id",h);if(f)return console.error("Error fetching user info:",f),b(e("failed_to_load_referral_data")),void s(!1);null;const x=m||[],u=async function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3;if(s>=a||0===e.length)return[];try{const{data:t,error:l}=await r.from("users").select("id, email, referred_by, created_at, role").in("id",e).limit(50);if(l)return console.error(`Error fetching referrers at level ${s}:`,l),[];const n=t||[],i=[...new Set(n.map(e=>e.referred_by).filter(Boolean))],c=await u(i,s+1,a);return[...n,...c]}catch(v){return console.error(`Error in fetchReferrers at level ${s}:`,v),[]}},j=[...new Set(x.map(e=>e.referred_by).filter(Boolean))],_=await u(j),g=[...x,..._].filter((e,r,s)=>r===s.findIndex(r=>r.id===e.id)),p=$(g);n(p),d(p)}catch(l){console.error("Error:",l);try{var t;console.log("Attempting fallback query...");if(!(null===(t=a)||void 0===t?void 0:t.id)){const{data:{user:s}}=await r.auth.getUser();if(!s)return void b(e("user_not_logged_in"));a=s}const{data:s,error:l}=await r.from("customer_profiles").select("\n                            users (\n                                id,\n                                email,\n                                created_at,\n                                role\n                            )\n                        ").eq("agent_id",a.id).limit(50);if(!l&&s&&s.length>0){const r=s.map(e=>e.users).filter(Boolean).map(e=>({...e,referred_by:null,children:[]}));console.log("Fallback successful, loaded",r.length,"users"),n(r),d(r),b(e("limited_referral_data"))}else console.log("Fallback failed or no data:",l),n([]),d([]),b(e("no_referral_data"))}catch(i){console.error("Fallback error:",i),n([]),d([]),b(e("unexpected_error"))}}finally{s(!1)}})()},[e]),(0,a.useEffect)(()=>{if(!y.trim())return void d(l);const e=r=>r.filter(r=>{const s=r.email.toLowerCase().includes(y.toLowerCase())||r.id.toLowerCase().includes(y.toLowerCase()),a=r.children?e(r.children):[];return s||a.length>0}).map(r=>({...r,children:r.children?e(r.children):[]}));d(e(l))},[y,l]);const $=e=>{if(!e||0===e.length)return[];const r=new Map,s=[];return e.forEach(e=>{r.set(e.id,{...e,children:[]})}),e.forEach(e=>{if(e.referred_by){const a=r.get(e.referred_by);a?a.children.push(r.get(e.id)):s.push(r.get(e.id))}else s.push(r.get(e.id))}),s},k=e=>{switch(e){case"maker":return(0,c.jsx)(_.YXz,{className:"text-primary me-1"});case"agent":return(0,c.jsx)(_.x$1,{className:"text-success me-1"});case"customer":return(0,c.jsx)(_.x$1,{className:"text-info me-1"});default:return(0,c.jsx)(_.x$1,{className:"text-secondary me-1"})}},C=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;const s=e.children&&e.children.length>0,a=A.has(e.id),t=20*r;return(0,c.jsxs)("div",{className:"mb-1",children:[(0,c.jsxs)("div",{className:"d-flex align-items-center p-2 border-bottom",style:{paddingLeft:`${t}px`,cursor:s?"pointer":"default"},onClick:()=>s&&(e=>{const r=new Set(A);r.has(e)?r.delete(e):r.add(e),N(r)})(e.id),children:[s?a?(0,c.jsx)(_.Vr3,{className:"me-2 text-muted"}):(0,c.jsx)(_.X6T,{className:"me-2 text-muted"}):(0,c.jsx)("span",{className:"me-4"}),k(e.role),(0,c.jsx)("span",{className:"me-2",children:e.email}),(0,c.jsxs)("span",{className:"text-muted small",children:["[",(l=e.id,l.substring(0,8)),"]"]}),s&&(0,c.jsx)("span",{className:"ms-auto badge bg-secondary",children:e.children.length})]}),s&&a&&(0,c.jsx)("div",{children:e.children.map(e=>C(e,r+1))})]},e.id);var l};return r?(0,c.jsx)(t.A,{className:"d-flex justify-content-center align-items-center",style:{minHeight:"400px"},children:(0,c.jsxs)("div",{className:"text-center",children:[(0,c.jsx)(o,{animation:"border",role:"status",className:"mb-3"}),(0,c.jsx)("div",{children:e("loading_referral_data")})]})}):v?(0,c.jsx)(t.A,{children:(0,c.jsx)(h.A,{variant:"danger",children:v})}):(0,c.jsxs)(t.A,{children:[(0,c.jsx)(m.A,{className:"mb-4",children:(0,c.jsxs)(f.A,{children:[(0,c.jsx)("h2",{children:e("referral_relationships")}),(0,c.jsx)("p",{className:"text-muted",children:e("referral_tree_description")})]})}),(0,c.jsx)(m.A,{className:"mb-3",children:(0,c.jsx)(f.A,{children:(0,c.jsx)(x.A,{children:(0,c.jsx)(x.A.Body,{children:(0,c.jsxs)(m.A,{className:"align-items-end",children:[(0,c.jsx)(f.A,{md:4,children:(0,c.jsxs)(u.A.Group,{children:[(0,c.jsx)(u.A.Label,{children:e("search_users")}),(0,c.jsx)(u.A.Control,{type:"text",placeholder:e("search_by_email_or_id"),value:y,onChange:e=>w(e.target.value)})]})}),(0,c.jsx)(f.A,{md:4,children:(0,c.jsxs)("div",{className:"d-flex gap-2",children:[(0,c.jsxs)(j.A,{variant:"outline-primary",size:"sm",onClick:()=>{const e=new Set,r=s=>{s.forEach(s=>{s.children&&s.children.length>0&&(e.add(s.id),r(s.children))})};r(i),N(e)},children:[(0,c.jsx)(_.xKl,{className:"me-1"}),e("expand_all")]}),(0,c.jsxs)(j.A,{variant:"outline-secondary",size:"sm",onClick:()=>{N(new Set)},children:[(0,c.jsx)(_.f93,{className:"me-1"}),e("collapse_all")]})]})})]})})})})}),(0,c.jsxs)(m.A,{className:"mb-3",children:[(0,c.jsx)(f.A,{md:4,children:(0,c.jsx)(x.A,{className:"bg-primary text-white",children:(0,c.jsxs)(x.A.Body,{children:[(0,c.jsx)("h5",{children:e("total_users")}),(0,c.jsx)("h3",{children:(e=>{let r=0;const s=e=>{e.forEach(e=>{r++,e.children&&e.children.length>0&&s(e.children)})};return s(e),r})(i)})]})})}),(0,c.jsx)(f.A,{md:4,children:(0,c.jsx)(x.A,{className:"bg-success text-white",children:(0,c.jsxs)(x.A.Body,{children:[(0,c.jsx)("h5",{children:e("root_users")}),(0,c.jsx)("h3",{children:i.length})]})})}),(0,c.jsx)(f.A,{md:4,children:(0,c.jsx)(x.A,{className:"bg-info text-white",children:(0,c.jsxs)(x.A.Body,{children:[(0,c.jsx)("h5",{children:e("expanded_nodes")}),(0,c.jsx)("h3",{children:A.size})]})})})]}),(0,c.jsx)(m.A,{children:(0,c.jsx)(f.A,{children:(0,c.jsxs)(x.A,{children:[(0,c.jsxs)(x.A.Header,{children:[(0,c.jsx)("h5",{className:"mb-0",children:e("referral_tree")}),(0,c.jsx)("small",{className:"text-muted",children:e("click_to_expand_collapse")})]}),(0,c.jsx)(x.A.Body,{style:{maxHeight:"600px",overflowY:"auto"},children:0===i.length?(0,c.jsx)("div",{className:"text-center text-muted py-4",children:e(y?"no_search_results":"no_referral_data")}):(0,c.jsx)("div",{children:i.map(e=>C(e))})})]})})})]})}},1719:(e,r,s)=>{s.d(r,{A:()=>A});var a=s(8139),t=s.n(a),l=s(5043),n=s(1969),i=s(6618),c=s(7852),d=s(4488),o=s(579);const h=(0,d.A)("h4");h.displayName="DivStyledAsH4";const m=l.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:l=h,...n}=e;return a=(0,c.oU)(a,"alert-heading"),(0,o.jsx)(l,{ref:r,className:t()(s,a),...n})});m.displayName="AlertHeading";const f=m;var x=s(7071);const u=l.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:l=x.A,...n}=e;return a=(0,c.oU)(a,"alert-link"),(0,o.jsx)(l,{ref:r,className:t()(s,a),...n})});u.displayName="AlertLink";const j=u;var _=s(8072),g=s(5632);const p=l.forwardRef((e,r)=>{const{bsPrefix:s,show:a=!0,closeLabel:l="Close alert",closeVariant:d,className:h,children:m,variant:f="primary",onClose:x,dismissible:u,transition:j=_.A,...p}=(0,n.Zw)(e,{show:"onClose"}),A=(0,c.oU)(s,"alert"),N=(0,i.A)(e=>{x&&x(!1,e)}),v=!0===j?_.A:j,b=(0,o.jsxs)("div",{role:"alert",...v?void 0:p,ref:r,className:t()(h,A,f&&`${A}-${f}`,u&&`${A}-dismissible`),children:[u&&(0,o.jsx)(g.A,{onClick:N,"aria-label":l,variant:d}),m]});return v?(0,o.jsx)(v,{unmountOnExit:!0,...p,ref:void 0,in:a,children:b}):a?b:null});p.displayName="Alert";const A=Object.assign(p,{Link:j,Heading:f})}}]);
//# sourceMappingURL=462.bad1674e.chunk.js.map