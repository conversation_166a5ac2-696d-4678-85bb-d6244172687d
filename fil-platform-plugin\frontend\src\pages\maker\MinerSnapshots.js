import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const MinerSnapshots = () => {
    const { t } = useTranslation();
    const [snapshots, setSnapshots] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchSnapshots = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // Fetch miner daily snapshots with miner information
            const { data, error } = await supabase
                .from('miner_daily_snapshots')
                .select(`
                    miner_id,
                    snapshot_date,
                    blockchain_height,
                    power,
                    available_balance,
                    pledge_locked,
                    balance,
                    miners (
                        filecoin_miner_id,
                        category,
                        facilities (
                            name
                        )
                    )
                `)
                .order('snapshot_date', { ascending: false });

            if (error) {
                console.error('Error fetching miner snapshots:', error);
            } else {
                setSnapshots(data);
            }
            setLoading(false);
        };

        fetchSnapshots();
    }, []);

    if (loading) {
        return <div>{t('loading_snapshots')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('miner_snapshots')}</h2>
            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('miner_id')}</th>
                                        <th>{t('facility')}</th>
                                        <th>{t('snapshot_date')}</th>
                                        <th>{t('blockchain_height')}</th>
                                        <th>{t('power')}</th>
                                        <th>{t('available_balance')}</th>
                                        <th>{t('pledge_locked')}</th>
                                        <th>{t('balance')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {snapshots.length === 0 ? (
                                        <tr>
                                            <td colSpan="8" className="text-center">{t('no_snapshots_available')}</td>
                                        </tr>
                                    ) : (
                                        snapshots.map((snapshot, index) => (
                                            <tr key={`${snapshot.miner_id}-${snapshot.snapshot_date}`}>
                                                <td>{snapshot.miners?.filecoin_miner_id || '-'}</td>
                                                <td>{snapshot.miners?.facilities?.name || '-'}</td>
                                                <td>{new Date(snapshot.snapshot_date).toLocaleDateString()}</td>
                                                <td>{snapshot.blockchain_height || '-'}</td>
                                                <td>{snapshot.power ? Number(snapshot.power).toFixed(2) : '0'} TiB</td>
                                                <td>{snapshot.available_balance ? Number(snapshot.available_balance).toFixed(6) : '0'} FIL</td>
                                                <td>{snapshot.pledge_locked ? Number(snapshot.pledge_locked).toFixed(6) : '0'} FIL</td>
                                                <td>{snapshot.balance ? Number(snapshot.balance).toFixed(6) : '0'} FIL</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default MinerSnapshots;
