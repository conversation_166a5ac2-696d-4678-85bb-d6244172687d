import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge, Button, Modal, Form, Alert } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const MakerFacilities = () => {
    const { t } = useTranslation();
    const [facilities, setFacilities] = useState([]);
    const [loading, setLoading] = useState(true);

    // Edit modal states
    const [showEditModal, setShowEditModal] = useState(false);
    const [editingFacility, setEditingFacility] = useState(null);
    const [editFormData, setEditFormData] = useState({
        name: ''
    });

    // Delete modal states
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [deletingFacility, setDeletingFacility] = useState(null);

    // Alert states
    const [alert, setAlert] = useState({ show: false, type: '', message: '' });

    useEffect(() => {
        const fetchFacilities = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // Fetch facilities associated with products from this maker
            const { data, error } = await supabase
                .from('facilities')
                .select(`
                    id,
                    name,
                    created_at,
                    updated_at
                `)
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Error fetching facilities:', error);
            } else {
                setFacilities(data);
            }
            setLoading(false);
        };

        fetchFacilities();
    }, []);

    // Handle edit button click
    const handleEditClick = (facility) => {
        setEditingFacility(facility);
        setEditFormData({
            name: facility.name || ''
        });
        setShowEditModal(true);
    };

    // Handle delete button click
    const handleDeleteClick = (facility) => {
        setDeletingFacility(facility);
        setShowDeleteModal(true);
    };

    // Handle edit form submission
    const handleEditSubmit = async (e) => {
        e.preventDefault();
        const supabase = getSupabase();
        if (!supabase || !editingFacility) return;

        try {
            const { error } = await supabase
                .from('facilities')
                .update({
                    name: editFormData.name,
                    updated_at: new Date().toISOString()
                })
                .eq('id', editingFacility.id);

            if (error) throw error;

            // Update local state
            setFacilities(facilities.map(facility =>
                facility.id === editingFacility.id
                    ? { ...facility, ...editFormData, updated_at: new Date().toISOString() }
                    : facility
            ));

            setAlert({ show: true, type: 'success', message: t('item_updated_successfully') });
            setShowEditModal(false);
            setEditingFacility(null);
        } catch (error) {
            console.error('Error updating facility:', error);
            setAlert({ show: true, type: 'danger', message: t('failed_to_update_item') + ': ' + error.message });
        }
    };

    // Handle delete confirmation
    const handleDeleteConfirm = async () => {
        const supabase = getSupabase();
        if (!supabase || !deletingFacility) return;

        try {
            const { error } = await supabase
                .from('facilities')
                .delete()
                .eq('id', deletingFacility.id);

            if (error) throw error;

            // Update local state
            setFacilities(facilities.filter(facility => facility.id !== deletingFacility.id));

            setAlert({ show: true, type: 'success', message: t('item_deleted_successfully') });
            setShowDeleteModal(false);
            setDeletingFacility(null);
        } catch (error) {
            console.error('Error deleting facility:', error);
            setAlert({ show: true, type: 'danger', message: t('failed_to_delete_item') + ': ' + error.message });
        }
    };

    // Handle form input changes
    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setEditFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    // Close alert
    const closeAlert = () => {
        setAlert({ show: false, type: '', message: '' });
    };

    if (loading) {
        return <div>{t('loading_facilities')}</div>;
    }

    return (
        <Container>
            {alert.show && (
                <Alert variant={alert.type} dismissible onClose={closeAlert} className="mb-4">
                    {alert.message}
                </Alert>
            )}
            <h2 className="mb-4">{t('all_facilities')}</h2>
                <Row>
                    <Col>
                        <Card>
                            <Card.Body>
                                <Table striped bordered hover responsive>
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>{t('name')}</th>
                                            <th>{t('created_at')}</th>
                                            <th>{t('agent')}</th>
                                            <th>{t('actions')}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {facilities.length === 0 ? (
                                            <tr>
                                                <td colSpan="9" className="text-center">{t('no_facilities_available')}</td>
                                            </tr>
                                        ) : (
                                            facilities.map(facility => (
                                                <tr key={facility.id}>
                                                    <td>{facility.id}</td>
                                                    <td>{facility.name}</td>
                                                    <td>{new Date(facility.created_at).toLocaleString()}</td>
                                                    <td>{new Date(facility.updated_at).toLocaleString()}</td>
                                                    <td>
                                                        <Button
                                                            variant="info"
                                                            size="sm"
                                                            className="me-2"
                                                            onClick={() => handleEditClick(facility)}
                                                        >
                                                            {t('edit')}
                                                        </Button>
                                                        <Button
                                                            variant="danger"
                                                            size="sm"
                                                            onClick={() => handleDeleteClick(facility)}
                                                        >
                                                            {t('delete')}
                                                        </Button>
                                                    </td>
                                                </tr>
                                            ))
                                        )}
                                    </tbody>
                                </Table>
                            </Card.Body>
                        </Card>
                    </Col>
                </Row>

            {/* Edit Modal */}
            <Modal show={showEditModal} onHide={() => setShowEditModal(false)} size="lg">
                <Modal.Header closeButton>
                    <Modal.Title>{t('edit_facility')}</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Form onSubmit={handleEditSubmit}>
                        <Form.Group className="mb-3">
                            <Form.Label>{t('name')}</Form.Label>
                            <Form.Control
                                type="text"
                                name="name"
                                value={editFormData.name}
                                onChange={handleInputChange}
                                required
                            />
                        </Form.Group>
                        <div className="d-flex justify-content-end">
                            <Button variant="secondary" className="me-2" onClick={() => setShowEditModal(false)}>
                                {t('cancel')}
                            </Button>
                            <Button variant="primary" type="submit">
                                {t('save_changes')}
                            </Button>
                        </div>
                    </Form>
                </Modal.Body>
            </Modal>

            {/* Delete Confirmation Modal */}
            <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
                <Modal.Header closeButton>
                    <Modal.Title>{t('confirm_delete')}</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <p>{t('delete_confirmation')}</p>
                    {deletingFacility && (
                        <p><strong>{t('name')}: {deletingFacility.name}</strong></p>
                    )}
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
                        {t('cancel')}
                    </Button>
                    <Button variant="danger" onClick={handleDeleteConfirm}>
                        {t('confirm')}
                    </Button>
                </Modal.Footer>
            </Modal>
        </Container>
    );
};

export default MakerFacilities;
