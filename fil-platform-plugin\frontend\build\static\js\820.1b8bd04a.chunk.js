"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[820],{1072:(e,r,s)=>{s.d(r,{A:()=>c});var n=s(8139),t=s.n(n),a=s(5043),i=s(7852),d=s(579);const l=a.forwardRef((e,r)=>{let{bsPrefix:s,className:n,as:a="div",...l}=e;const c=(0,i.oU)(s,"row"),o=(0,i.gy)(),u=(0,i.Jm)(),h=`${c}-cols`,x=[];return o.forEach(e=>{const r=l[e];let s;delete l[e],null!=r&&"object"===typeof r?({cols:s}=r):s=r;const n=e!==u?`-${e}`:"";null!=s&&x.push(`${h}${n}-${s}`)}),(0,d.jsx)(a,{ref:r,...l,className:t()(n,c,...x)})});l.displayName="Row";const c=l},4063:(e,r,s)=>{s.d(r,{A:()=>c});var n=s(8139),t=s.n(n),a=s(5043),i=s(7852),d=s(579);const l=a.forwardRef((e,r)=>{let{bsPrefix:s,bg:n="primary",pill:a=!1,text:l,className:c,as:o="span",...u}=e;const h=(0,i.oU)(s,"badge");return(0,d.jsx)(o,{ref:r,...u,className:t()(c,h,a&&"rounded-pill",l&&`text-${l}`,n&&`bg-${n}`)})});l.displayName="Badge";const c=l},4196:(e,r,s)=>{s.d(r,{A:()=>c});var n=s(8139),t=s.n(n),a=s(5043),i=s(7852),d=s(579);const l=a.forwardRef((e,r)=>{let{bsPrefix:s,className:n,striped:a,bordered:l,borderless:c,hover:o,size:u,variant:h,responsive:x,...j}=e;const m=(0,i.oU)(s,"table"),v=t()(n,m,h&&`${m}-${h}`,u&&`${m}-${u}`,a&&`${m}-${"string"===typeof a?`striped-${a}`:"striped"}`,l&&`${m}-bordered`,c&&`${m}-borderless`,o&&`${m}-hover`),p=(0,d.jsx)("table",{...j,className:v,ref:r});if(x){let e=`${m}-responsive`;return"string"===typeof x&&(e=`${e}-${x}`),(0,d.jsx)("div",{className:e,children:p})}return p});l.displayName="Table";const c=l},8820:(e,r,s)=>{s.r(r),s.d(r,{default:()=>N});var n=s(5043),t=s(4063),a=s(3519),i=s(1072),d=s(8602),l=s(8628),c=s(4282),o=s(3722),u=s(8139),h=s.n(u),x=s(7852),j=s(1068),m=s(9334),v=s(579);const p=n.forwardRef((e,r)=>{let{className:s,bsPrefix:n,as:t="span",...a}=e;return n=(0,x.oU)(n,"input-group-text"),(0,v.jsx)(t,{ref:r,className:h()(s,n),...a})});p.displayName="InputGroupText";const _=p,g=n.forwardRef((e,r)=>{let{bsPrefix:s,size:t,hasValidation:a,className:i,as:d="div",...l}=e;s=(0,x.oU)(s,"input-group");const c=(0,n.useMemo)(()=>({}),[]);return(0,v.jsx)(m.A.Provider,{value:c,children:(0,v.jsx)(d,{ref:r,...l,className:h()(i,s,t&&`${s}-${t}`,a&&"has-validation")})})});g.displayName="InputGroup";const f=Object.assign(g,{Text:_,Radio:e=>(0,v.jsx)(_,{children:(0,v.jsx)(j.A,{type:"radio",...e})}),Checkbox:e=>(0,v.jsx)(_,{children:(0,v.jsx)(j.A,{type:"checkbox",...e})})});var b=s(4196),A=s(3204),w=s(4312),y=s(4117);const N=()=>{const{t:e}=(0,y.Bd)(),[r,s]=(0,n.useState)([]),[u,h]=(0,n.useState)(!0),[x,j]=(0,n.useState)(""),[m,p]=(0,n.useState)(""),[_,g]=(0,n.useState)(""),[N,$]=(0,n.useState)(""),[C,k]=(0,n.useState)([]);(0,n.useEffect)(()=>{(async()=>{const e=(0,w.b)();if(!e)return;h(!0);const{data:{user:r}}=await e.auth.getUser();if(!r)return void h(!1);const{data:n,error:t}=await e.from("customer_profiles").select("user_id, real_name, verify_status").eq("agent_id",r.id).order("created_at",{ascending:!1});if(t||!n)return console.error("Error fetching customer_profiles:",t),void h(!1);n.map(e=>e.user_id).filter(Boolean);const{data:a,error:i}=await e.from("users").select("id, email, created_at");i&&console.error("Error fetching users:",i);const d=new Map((a||[]).map(e=>[e.id,e])),l=n.map(e=>({...e,users:d.get(e.user_id)||{}}));s(l),h(!1)})()},[]),(0,n.useEffect)(()=>{let e=r;x&&(e=e.filter(e=>{var r,s,n;return(null===(r=e.users)||void 0===r||null===(s=r.email)||void 0===s?void 0:s.toLowerCase().includes(x.toLowerCase()))||(null===(n=e.real_name)||void 0===n?void 0:n.toLowerCase().includes(x.toLowerCase()))})),m&&(e=e.filter(e=>e.verify_status===m)),_&&(e=e.filter(e=>{var r;return new Date(null===(r=e.users)||void 0===r?void 0:r.created_at)>=new Date(_)})),N&&(e=e.filter(e=>{var r;return new Date(null===(r=e.users)||void 0===r?void 0:r.created_at)<=new Date(N)})),k(e)},[r,x,m,_,N]);const S=r=>{switch(r){case"approved":return(0,v.jsx)(t.A,{bg:"success",children:e("approved")});case"pending":return(0,v.jsx)(t.A,{bg:"warning",children:e("pending_review")});case"rejected":return(0,v.jsx)(t.A,{bg:"danger",children:e("rejected")});case"under_review":return(0,v.jsx)(t.A,{bg:"info",children:e("under_review")});default:return(0,v.jsx)(t.A,{bg:"secondary",children:r||e("not_submitted")})}};return u?(0,v.jsx)("div",{children:e("loading_members")}):(0,v.jsxs)(a.A,{children:[(0,v.jsx)("h2",{className:"mb-4",children:e("member_list")}),(0,v.jsx)(i.A,{className:"mb-4",children:(0,v.jsx)(d.A,{children:(0,v.jsx)(l.A,{children:(0,v.jsx)(l.A.Body,{children:(0,v.jsxs)(i.A,{className:"align-items-end",children:[(0,v.jsx)(d.A,{md:2,children:(0,v.jsxs)(c.A,{variant:"primary",onClick:()=>{alert(e("add_member_coming_soon"))},className:"mb-2",children:[(0,v.jsx)(A.OiG,{className:"me-1"}),e("add_member")]})}),(0,v.jsx)(d.A,{md:3,children:(0,v.jsxs)(o.A.Group,{children:[(0,v.jsx)(o.A.Label,{children:e("search_username")}),(0,v.jsx)(f,{children:(0,v.jsx)(o.A.Control,{type:"text",placeholder:e("please_enter_username"),value:x,onChange:e=>j(e.target.value)})})]})}),(0,v.jsx)(d.A,{md:2,children:(0,v.jsxs)(o.A.Group,{children:[(0,v.jsx)(o.A.Label,{children:e("status_filter")}),(0,v.jsxs)(o.A.Select,{value:m,onChange:e=>p(e.target.value),children:[(0,v.jsx)("option",{value:"",children:e("please_select_status")}),(0,v.jsx)("option",{value:"pending",children:e("pending_review")}),(0,v.jsx)("option",{value:"approved",children:e("approved")}),(0,v.jsx)("option",{value:"rejected",children:e("rejected")}),(0,v.jsx)("option",{value:"under_review",children:e("under_review")})]})]})}),(0,v.jsx)(d.A,{md:2,children:(0,v.jsxs)(o.A.Group,{children:[(0,v.jsx)(o.A.Label,{children:e("start_date")}),(0,v.jsx)(o.A.Control,{type:"date",value:_,onChange:e=>g(e.target.value)})]})}),(0,v.jsx)(d.A,{md:2,children:(0,v.jsxs)(o.A.Group,{children:[(0,v.jsx)(o.A.Label,{children:e("end_date")}),(0,v.jsx)(o.A.Control,{type:"date",value:N,onChange:e=>$(e.target.value)})]})}),(0,v.jsx)(d.A,{md:1,children:(0,v.jsx)(c.A,{variant:"outline-primary",onClick:()=>{console.log("Search triggered")},className:"mb-2",children:(0,v.jsx)(A.KSO,{})})})]})})})})}),(0,v.jsx)(i.A,{children:(0,v.jsx)(d.A,{children:(0,v.jsx)(l.A,{children:(0,v.jsx)(l.A.Body,{children:(0,v.jsxs)(b.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,v.jsx)("thead",{children:(0,v.jsxs)("tr",{children:[(0,v.jsx)("th",{children:e("username")}),(0,v.jsx)("th",{children:e("real_name")}),(0,v.jsx)("th",{children:e("id_number")}),(0,v.jsx)("th",{children:e("id_front_image")}),(0,v.jsx)("th",{children:e("id_back_image")}),(0,v.jsx)("th",{children:e("agent_name")}),(0,v.jsx)("th",{children:e("status")}),(0,v.jsx)("th",{children:e("registration_time")}),(0,v.jsx)("th",{children:e("actions")})]})}),(0,v.jsx)("tbody",{children:0===C.length?(0,v.jsx)("tr",{children:(0,v.jsx)("td",{colSpan:"9",className:"text-center",children:e("no_members_found")})}):C.map(r=>{var s,n,a,i;return(0,v.jsxs)("tr",{children:[(0,v.jsx)("td",{children:(null===(s=r.users)||void 0===s?void 0:s.email)||"-"}),(0,v.jsx)("td",{children:r.real_name||"-"}),(0,v.jsx)("td",{children:r.id_number||"-"}),(0,v.jsx)("td",{children:r.id_img_front?(0,v.jsx)(t.A,{bg:"success",children:e("uploaded")}):(0,v.jsx)(t.A,{bg:"secondary",children:e("not_uploaded")})}),(0,v.jsx)("td",{children:r.id_img_back?(0,v.jsx)(t.A,{bg:"success",children:e("uploaded")}):(0,v.jsx)(t.A,{bg:"secondary",children:e("not_uploaded")})}),(0,v.jsx)("td",{children:(0,v.jsxs)("div",{children:[(0,v.jsx)("div",{children:(null===(n=r.agent_info)||void 0===n?void 0:n.brand_name)||"-"}),(0,v.jsx)("small",{className:"text-muted",children:(null===(a=r.agent_info)||void 0===a?void 0:a.email)||"-"})]})}),(0,v.jsx)("td",{children:S(r.verify_status)}),(0,v.jsx)("td",{children:null!==(i=r.users)&&void 0!==i&&i.created_at?new Date(r.users.created_at).toLocaleString():"-"}),(0,v.jsx)("td",{children:(0,v.jsxs)("div",{className:"d-flex gap-1",children:[(0,v.jsx)(c.A,{size:"sm",variant:"outline-primary",onClick:()=>(r.user_id,void alert(e("kyc_review_coming_soon"))),title:e("kyc_review"),children:(0,v.jsx)(A.BAG,{})}),(0,v.jsx)(c.A,{size:"sm",variant:"outline-warning",onClick:()=>(r.user_id,void alert(e("change_agent_coming_soon"))),title:e("change_agent"),children:(0,v.jsx)(A.yk7,{})}),(0,v.jsx)(c.A,{size:"sm",variant:"outline-info",onClick:()=>(r.user_id,void alert(e("view_details_coming_soon"))),title:e("view_details"),children:(0,v.jsx)(A.Ny1,{})})]})})]},r.user_id)})})]})})})})})]})}}}]);
//# sourceMappingURL=820.1b8bd04a.chunk.js.map