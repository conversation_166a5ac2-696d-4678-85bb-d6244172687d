import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge, Button, Form, InputGroup, Dropdown } from 'react-bootstrap';
import { FaSearch, FaPlus, FaEye, FaUserCheck, FaExchangeAlt } from 'react-icons/fa';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const Members = () => {
    const { t } = useTranslation();
    const [members, setMembers] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [startDate, setStartDate] = useState('');
    const [endDate, setEndDate] = useState('');
    const [filteredMembers, setFilteredMembers] = useState([]);

    useEffect(() => {
            const fetchMembers = async () => {
                const supabase = getSupabase();
                if (!supabase) return;
    
                setLoading(true);
                const { data: { user } } = await supabase.auth.getUser();
    
                if (!user) {
                    setLoading(false);
                    return;
                }
    
                // Step 1: 查询 customer_profiles
                const { data: customers, error: profileError } = await supabase
                    .from('customer_profiles')
                    .select('user_id, real_name, id_number, id_img_front, id_img_back, verify_status')
                    .eq('agent_id', user.id)
                    .order('created_at', { ascending: false });
    
                if (profileError || !customers) {
                    console.error('Error fetching customer_profiles:', profileError);
                    setLoading(false);
                    return;
                }
    
                // Step 2: 查询 users 表
                const userIds = customers.map(c => c.user_id).filter(Boolean);
    
                const { data: userInfoList, error: userError } = await supabase
                    .from('users')
                    .select('id, email, created_at')

                if (userError) {
                    console.error('Error fetching users:', userError);
                }
    
                // Step 3: 合并结果
                const usersMap = new Map((userInfoList || []).map(u => [u.id, u]));
    
                const enrichedMembers = customers.map(c => ({
                    ...c,
                    users: usersMap.get(c.user_id) || {}
                }));
    
                setMembers(enrichedMembers);
                setLoading(false);
            };

        fetchMembers();
    }, []);

    // Filter members based on search criteria
    useEffect(() => {
        let filtered = members;

        // Search by username (email)
        if (searchTerm) {
            filtered = filtered.filter(member =>
                member.users?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                member.real_name?.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        // Filter by status
        if (statusFilter) {
            filtered = filtered.filter(member => member.verify_status === statusFilter);
        }

        // Filter by date range
        if (startDate) {
            filtered = filtered.filter(member => 
                new Date(member.users?.created_at) >= new Date(startDate)
            );
        }
        if (endDate) {
            filtered = filtered.filter(member => 
                new Date(member.users?.created_at) <= new Date(endDate)
            );
        }

        setFilteredMembers(filtered);
    }, [members, searchTerm, statusFilter, startDate, endDate]);

    const getStatusBadge = (status) => {
        switch (status) {
            case 'approved':
                return <Badge bg="success">{t('approved')}</Badge>;
            case 'pending':
                return <Badge bg="warning">{t('pending_review')}</Badge>;
            case 'rejected':
                return <Badge bg="danger">{t('rejected')}</Badge>;
            case 'under_review':
                return <Badge bg="info">{t('under_review')}</Badge>;
            default:
                return <Badge bg="secondary">{status || t('not_submitted')}</Badge>;
        }
    };

    const handleSearch = () => {
        // Search is handled by useEffect, this function can be used for additional logic if needed
        console.log('Search triggered');
    };

    const handleAddMember = () => {
        // TODO: Implement add member functionality
        alert(t('add_member_coming_soon'));
    };

    const handleKycReview = (memberId) => {
        // TODO: Implement KYC review functionality
        alert(t('kyc_review_coming_soon'));
    };

    const handleChangeAgent = (memberId) => {
        // TODO: Implement change agent functionality
        alert(t('change_agent_coming_soon'));
    };

    const handleViewDetails = (memberId) => {
        // TODO: Implement view details functionality
        alert(t('view_details_coming_soon'));
    };

    if (loading) {
        return <div>{t('loading_members')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('member_list')}</h2>
            
            {/* Top Operation Bar */}
            <Row className="mb-4">
                <Col>
                    <Card>
                        <Card.Body>
                            <Row className="align-items-end">
                                <Col md={2}>
                                    <Button 
                                        variant="primary" 
                                        onClick={handleAddMember}
                                        className="mb-2"
                                    >
                                        <FaPlus className="me-1" />
                                        {t('add_member')}
                                    </Button>
                                </Col>
                                <Col md={3}>
                                    <Form.Group>
                                        <Form.Label>{t('search_username')}</Form.Label>
                                        <InputGroup>
                                            <Form.Control
                                                type="text"
                                                placeholder={t('please_enter_username')}
                                                value={searchTerm}
                                                onChange={(e) => setSearchTerm(e.target.value)}
                                            />
                                        </InputGroup>
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('status_filter')}</Form.Label>
                                        <Form.Select
                                            value={statusFilter}
                                            onChange={(e) => setStatusFilter(e.target.value)}
                                        >
                                            <option value="">{t('please_select_status')}</option>
                                            <option value="pending">{t('pending_review')}</option>
                                            <option value="approved">{t('approved')}</option>
                                            <option value="rejected">{t('rejected')}</option>
                                            <option value="under_review">{t('under_review')}</option>
                                        </Form.Select>
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('start_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={startDate}
                                            onChange={(e) => setStartDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('end_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={endDate}
                                            onChange={(e) => setEndDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={1}>
                                    <Button 
                                        variant="outline-primary" 
                                        onClick={handleSearch}
                                        className="mb-2"
                                    >
                                        <FaSearch />
                                    </Button>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Members Table */}
            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('username')}</th>
                                        <th>{t('real_name')}</th>
                                        <th>{t('id_number')}</th>
                                        <th>{t('id_front_image')}</th>
                                        <th>{t('id_back_image')}</th>
                                        <th>{t('agent_name')}</th>
                                        <th>{t('status')}</th>
                                        <th>{t('registration_time')}</th>
                                        <th>{t('actions')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {filteredMembers.length === 0 ? (
                                        <tr>
                                            <td colSpan="9" className="text-center">{t('no_members_found')}</td>
                                        </tr>
                                    ) : (
                                        filteredMembers.map(member => (
                                            <tr key={member.user_id}>
                                                <td>{member.users?.email || '-'}</td>
                                                <td>{member.real_name || '-'}</td>
                                                <td>{member.id_number || '-'}</td>
                                                <td>
                                                    {member.id_img_front ? (
                                                        <Badge bg="success">{t('uploaded')}</Badge>
                                                    ) : (
                                                        <Badge bg="secondary">{t('not_uploaded')}</Badge>
                                                    )}
                                                </td>
                                                <td>
                                                    {member.id_img_back ? (
                                                        <Badge bg="success">{t('uploaded')}</Badge>
                                                    ) : (
                                                        <Badge bg="secondary">{t('not_uploaded')}</Badge>
                                                    )}
                                                </td>
                                                <td>
                                                    <div>
                                                        <div>{member.agent_info?.brand_name || '-'}</div>
                                                        <small className="text-muted">
                                                            {member.agent_info?.email || '-'}
                                                        </small>
                                                    </div>
                                                </td>
                                                <td>{getStatusBadge(member.verify_status)}</td>
                                                <td>{member.users?.created_at ? new Date(member.users.created_at).toLocaleString() : '-'}</td>
                                                <td>
                                                    <div className="d-flex gap-1">
                                                        <Button
                                                            size="sm"
                                                            variant="outline-primary"
                                                            onClick={() => handleKycReview(member.user_id)}
                                                            title={t('kyc_review')}
                                                        >
                                                            <FaUserCheck />
                                                        </Button>
                                                        <Button
                                                            size="sm"
                                                            variant="outline-warning"
                                                            onClick={() => handleChangeAgent(member.user_id)}
                                                            title={t('change_agent')}
                                                        >
                                                            <FaExchangeAlt />
                                                        </Button>
                                                        <Button
                                                            size="sm"
                                                            variant="outline-info"
                                                            onClick={() => handleViewDetails(member.user_id)}
                                                            title={t('view_details')}
                                                        >
                                                            <FaEye />
                                                        </Button>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default Members;
