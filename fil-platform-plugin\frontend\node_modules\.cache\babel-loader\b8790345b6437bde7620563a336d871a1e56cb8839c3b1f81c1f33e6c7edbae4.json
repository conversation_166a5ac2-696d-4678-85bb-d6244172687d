{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Container,Row,Col,Card,Table,Badge}from'react-bootstrap';import{getSupabase}from'../../supabaseClient';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AgentProductListPage=()=>{const{t}=useTranslation();const[products,setProducts]=useState([]);const[loading,setLoading]=useState(true);useEffect(()=>{const fetchProducts=async()=>{const supabase=getSupabase();if(!supabase)return;setLoading(true);const{data,error}=await supabase.from('products').select(`\n                    id,\n                    name,\n                    category,\n                    price,\n                    total_shares,\n                    sold_shares,\n                    is_disabled,\n                    agent_profiles ( brand_name )\n                `).eq('is_disabled',false)// Only show active products\n.order('created_at',{ascending:false});if(error){console.error('Error fetching products:',error);}else{setProducts(data);}setLoading(false);};fetchProducts();},[]);if(loading){return/*#__PURE__*/_jsx(\"div\",{children:t('loading_products')});}return/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"mb-4\",children:t('products_on_sale')}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Table,{striped:true,bordered:true,hover:true,responsive:true,children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:t('product_id')}),/*#__PURE__*/_jsx(\"th\",{children:t('product_name')}),/*#__PURE__*/_jsx(\"th\",{children:t('category')}),/*#__PURE__*/_jsx(\"th\",{children:t('price')}),/*#__PURE__*/_jsx(\"th\",{children:t('total_shares')}),/*#__PURE__*/_jsx(\"th\",{children:t('sold_shares')}),/*#__PURE__*/_jsx(\"th\",{children:t('remaining_shares')}),/*#__PURE__*/_jsx(\"th\",{children:t('maker')})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:products.length===0?/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:\"8\",className:\"text-center\",children:t('no_products')})}):products.map(product=>{var _product$maker_profil;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsxs(\"td\",{children:[product.id.substring(0,8),\"...\"]}),/*#__PURE__*/_jsx(\"td\",{children:product.name}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(Badge,{bg:product.category==='spot'?'success':'primary',children:t(product.category==='spot'?'spot':'futures')})}),/*#__PURE__*/_jsx(\"td\",{children:product.price}),/*#__PURE__*/_jsx(\"td\",{children:product.total_shares}),/*#__PURE__*/_jsx(\"td\",{children:product.sold_shares}),/*#__PURE__*/_jsx(\"td\",{children:product.total_shares-product.sold_shares}),/*#__PURE__*/_jsx(\"td\",{children:((_product$maker_profil=product.maker_profiles)===null||_product$maker_profil===void 0?void 0:_product$maker_profil.brand_name)||'N/A'})]},product.id);})})]})})})})})]});};export default AgentProductListPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Table", "Badge", "getSupabase", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "AgentProductListPage", "t", "products", "setProducts", "loading", "setLoading", "fetchProducts", "supabase", "data", "error", "from", "select", "eq", "order", "ascending", "console", "children", "className", "Body", "striped", "bordered", "hover", "responsive", "length", "colSpan", "map", "product", "_product$maker_profil", "id", "substring", "name", "bg", "category", "price", "total_shares", "sold_shares", "maker_profiles", "brand_name"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/agent/AgentProductListPage.js"], "sourcesContent": ["\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst AgentProductListPage = () => {\n    const { t } = useTranslation();\n    const [products, setProducts] = useState([]);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        const fetchProducts = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data, error } = await supabase\n                .from('products')\n                .select(`\n                    id,\n                    name,\n                    category,\n                    price,\n                    total_shares,\n                    sold_shares,\n                    is_disabled,\n                    agent_profiles ( brand_name )\n                `)\n                .eq('is_disabled', false) // Only show active products\n                .order('created_at', { ascending: false });\n\n            if (error) {\n                console.error('Error fetching products:', error);\n            } else {\n                setProducts(data);\n            }\n            setLoading(false);\n        };\n\n        fetchProducts();\n    }, []);\n\n    if (loading) {\n        return <div>{t('loading_products')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('products_on_sale')}</h2>\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('product_id')}</th>\n                                        <th>{t('product_name')}</th>\n                                        <th>{t('category')}</th>\n                                        <th>{t('price')}</th>\n                                        <th>{t('total_shares')}</th>\n                                        <th>{t('sold_shares')}</th>\n                                        <th>{t('remaining_shares')}</th>\n                                        <th>{t('maker')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {products.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"8\" className=\"text-center\">{t('no_products')}</td>\n                                        </tr>\n                                    ) : (\n                                        products.map(product => (\n                                            <tr key={product.id}>\n                                                <td>{product.id.substring(0, 8)}...</td>\n                                                <td>{product.name}</td>\n                                                <td><Badge bg={product.category === 'spot' ? 'success' : 'primary'}>{t(product.category === 'spot' ? 'spot' : 'futures')}</Badge></td>\n                                                <td>{product.price}</td>\n                                                <td>{product.total_shares}</td>\n                                                <td>{product.sold_shares}</td>\n                                                <td>{product.total_shares - product.sold_shares}</td>\n                                                <td>{product.maker_profiles?.brand_name || 'N/A'}</td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default AgentProductListPage;\n"], "mappings": "AACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,KAAK,CAAEC,KAAK,KAAQ,iBAAiB,CACzE,OAASC,WAAW,KAAQ,sBAAsB,CAClD,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,oBAAoB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAEC,CAAE,CAAC,CAAGN,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACO,QAAQ,CAAEC,WAAW,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACkB,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CAE5CC,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAmB,aAAa,CAAG,KAAAA,CAAA,GAAY,CAC9B,KAAM,CAAAC,QAAQ,CAAGb,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACa,QAAQ,CAAE,OAEfF,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEG,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAF,QAAQ,CACjCG,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,CAAC,CACDC,EAAE,CAAC,aAAa,CAAE,KAAK,CAAE;AAAA,CACzBC,KAAK,CAAC,YAAY,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAE9C,GAAIL,KAAK,CAAE,CACPM,OAAO,CAACN,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CACpD,CAAC,IAAM,CACHN,WAAW,CAACK,IAAI,CAAC,CACrB,CACAH,UAAU,CAAC,KAAK,CAAC,CACrB,CAAC,CAEDC,aAAa,CAAC,CAAC,CACnB,CAAC,CAAE,EAAE,CAAC,CAEN,GAAIF,OAAO,CAAE,CACT,mBAAOP,IAAA,QAAAmB,QAAA,CAAMf,CAAC,CAAC,kBAAkB,CAAC,CAAM,CAAC,CAC7C,CAEA,mBACIF,KAAA,CAACX,SAAS,EAAA4B,QAAA,eACNnB,IAAA,OAAIoB,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAEf,CAAC,CAAC,kBAAkB,CAAC,CAAK,CAAC,cACjDJ,IAAA,CAACR,GAAG,EAAA2B,QAAA,cACAnB,IAAA,CAACP,GAAG,EAAA0B,QAAA,cACAnB,IAAA,CAACN,IAAI,EAAAyB,QAAA,cACDnB,IAAA,CAACN,IAAI,CAAC2B,IAAI,EAAAF,QAAA,cACNjB,KAAA,CAACP,KAAK,EAAC2B,OAAO,MAACC,QAAQ,MAACC,KAAK,MAACC,UAAU,MAAAN,QAAA,eACpCnB,IAAA,UAAAmB,QAAA,cACIjB,KAAA,OAAAiB,QAAA,eACInB,IAAA,OAAAmB,QAAA,CAAKf,CAAC,CAAC,YAAY,CAAC,CAAK,CAAC,cAC1BJ,IAAA,OAAAmB,QAAA,CAAKf,CAAC,CAAC,cAAc,CAAC,CAAK,CAAC,cAC5BJ,IAAA,OAAAmB,QAAA,CAAKf,CAAC,CAAC,UAAU,CAAC,CAAK,CAAC,cACxBJ,IAAA,OAAAmB,QAAA,CAAKf,CAAC,CAAC,OAAO,CAAC,CAAK,CAAC,cACrBJ,IAAA,OAAAmB,QAAA,CAAKf,CAAC,CAAC,cAAc,CAAC,CAAK,CAAC,cAC5BJ,IAAA,OAAAmB,QAAA,CAAKf,CAAC,CAAC,aAAa,CAAC,CAAK,CAAC,cAC3BJ,IAAA,OAAAmB,QAAA,CAAKf,CAAC,CAAC,kBAAkB,CAAC,CAAK,CAAC,cAChCJ,IAAA,OAAAmB,QAAA,CAAKf,CAAC,CAAC,OAAO,CAAC,CAAK,CAAC,EACrB,CAAC,CACF,CAAC,cACRJ,IAAA,UAAAmB,QAAA,CACKd,QAAQ,CAACqB,MAAM,GAAK,CAAC,cAClB1B,IAAA,OAAAmB,QAAA,cACInB,IAAA,OAAI2B,OAAO,CAAC,GAAG,CAACP,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAEf,CAAC,CAAC,aAAa,CAAC,CAAK,CAAC,CAC/D,CAAC,CAELC,QAAQ,CAACuB,GAAG,CAACC,OAAO,OAAAC,qBAAA,oBAChB5B,KAAA,OAAAiB,QAAA,eACIjB,KAAA,OAAAiB,QAAA,EAAKU,OAAO,CAACE,EAAE,CAACC,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,KAAG,EAAI,CAAC,cACxChC,IAAA,OAAAmB,QAAA,CAAKU,OAAO,CAACI,IAAI,CAAK,CAAC,cACvBjC,IAAA,OAAAmB,QAAA,cAAInB,IAAA,CAACJ,KAAK,EAACsC,EAAE,CAAEL,OAAO,CAACM,QAAQ,GAAK,MAAM,CAAG,SAAS,CAAG,SAAU,CAAAhB,QAAA,CAAEf,CAAC,CAACyB,OAAO,CAACM,QAAQ,GAAK,MAAM,CAAG,MAAM,CAAG,SAAS,CAAC,CAAQ,CAAC,CAAI,CAAC,cACtInC,IAAA,OAAAmB,QAAA,CAAKU,OAAO,CAACO,KAAK,CAAK,CAAC,cACxBpC,IAAA,OAAAmB,QAAA,CAAKU,OAAO,CAACQ,YAAY,CAAK,CAAC,cAC/BrC,IAAA,OAAAmB,QAAA,CAAKU,OAAO,CAACS,WAAW,CAAK,CAAC,cAC9BtC,IAAA,OAAAmB,QAAA,CAAKU,OAAO,CAACQ,YAAY,CAAGR,OAAO,CAACS,WAAW,CAAK,CAAC,cACrDtC,IAAA,OAAAmB,QAAA,CAAK,EAAAW,qBAAA,CAAAD,OAAO,CAACU,cAAc,UAAAT,qBAAA,iBAAtBA,qBAAA,CAAwBU,UAAU,GAAI,KAAK,CAAK,CAAC,GARjDX,OAAO,CAACE,EASb,CAAC,EACR,CACJ,CACE,CAAC,EACL,CAAC,CACD,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,EACC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAA5B,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}