{"version": 3, "file": "static/js/462.c0122152.chunk.js", "mappings": "wKAMA,MAAMA,EAAmBC,EAAAA,WAAiB,CAAAC,EAMvCC,KAAQ,IANgC,SACzCC,EAAQ,UACRC,EAEAC,GAAIC,EAAY,SACbC,GACJN,EACC,MAAMO,GAAoBC,EAAAA,EAAAA,IAAmBN,EAAU,OACjDO,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChBC,EAAa,GAAGN,SAChBO,EAAU,GAehB,OAdAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAIE,SADGZ,EAAMU,GAEI,MAAbC,GAA0C,kBAAdA,IAE5BC,QACED,GAEJC,EAAOD,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GAChD,MAARE,GAAcJ,EAAQM,KAAK,GAAGP,IAAaM,KAASD,QAEtCG,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWI,KAAsBO,OAG3DhB,EAAIyB,YAAc,MAClB,S,6GCjCA,MAAMC,EAAuBzB,EAAAA,WAAiB,CAAAC,EAS3CC,KAAQ,IAToC,SAC7CC,EAAQ,QACRuB,EAAO,UACPC,EAAY,SAAQ,KACpBC,EAEAvB,GAAIC,EAAY,MAAK,UACrBF,KACGG,GACJN,EACCE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,WACxC,MAAM0B,EAAkB,GAAG1B,KAAYwB,IACvC,OAAoBL,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWyB,EAAiBD,GAAQ,GAAGC,KAAmBD,IAAQF,GAAW,QAAQA,SAG/GD,EAAQD,YAAc,UACtB,U,8FCnBA,MAsfA,EAtfkBM,KACd,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAASC,IAAcC,EAAAA,EAAAA,WAAS,IAChCC,EAAcC,IAAmBF,EAAAA,EAAAA,UAAS,KAC1CG,EAAcC,IAAmBJ,EAAAA,EAAAA,UAAS,KAC1CK,EAAeC,IAAoBN,EAAAA,EAAAA,UAAS,IAAIO,MAChDC,EAAOC,IAAYT,EAAAA,EAAAA,UAAS,OAC5BU,EAAYC,IAAiBX,EAAAA,EAAAA,UAAS,KAE7CY,EAAAA,EAAAA,WAAU,KACoBC,WACtB,MAAMC,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EAAU,OAEff,GAAW,GACXU,EAAS,MAET,IAAIO,EAAc,KAKlB,IACI,MAAQC,MAAM,KAAEC,UAAiBJ,EAASK,KAAKC,UAG/C,GAFAJ,EAAcE,GAETA,EAGD,OAFAT,EAASb,EAAE,4BACXG,GAAW,GAKf,MAAQkB,KAAMI,EAAcb,MAAOc,SAAqBR,EACnDS,KAAK,kBACLC,OAAO,YACPC,GAAG,UAAWP,EAAKQ,IACnBC,SAEL,GAAIL,EAIA,OAHAM,QAAQpB,MAAM,gCAAiCc,GAC/Cb,EAASb,EAAE,iCACXG,GAAW,GAKf,MAAQkB,KAAMY,EAAkBrB,MAAOsB,SAAuBhB,EACzDS,KAAK,qBACLC,OAAO,WACPC,GAAG,WAAYP,EAAKQ,IACpBK,MAAM,KAEX,GAAID,EAIA,OAHAF,QAAQpB,MAAM,oCAAqCsB,GACnDrB,EAASb,EAAE,sCACXG,GAAW,GAKf,MAAMiC,EAAUH,EAAiBI,IAAIC,GAAKA,EAAEC,SAASC,OAAOC,UAEpDpB,KAAMqB,EAAW9B,MAAO+B,SAAqBzB,EAChDS,KAAK,SACLC,OAAO,yDACPgB,GAAG,KAAMR,GAEd,GAAIO,EAIA,OAHAX,QAAQpB,MAAM,4BAA6B+B,GAC3C9B,EAASb,EAAE,sCACXG,GAAW,GApDE,KAgEjB,MAAM0C,EAAgBH,GAAa,GAG7BI,EAAiB7B,eAAOmB,GAAsC,IAA7BW,EAAKC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,EAAGG,EAAQH,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,EACzD,GAAID,GAASI,GAA+B,IAAnBf,EAAQa,OAC7B,MAAO,GAGX,IACI,MAAQ5B,KAAM+B,EAAcxC,MAAOyC,SAAwBnC,EACtDS,KAAK,SACLC,OAAO,yDACPgB,GAAG,KAAMR,GACTD,MAAM,IAEX,GAAIkB,EAEA,OADArB,QAAQpB,MAAM,qCAAqCmC,KAAUM,GACtD,GAGX,MAAMC,EAAYF,GAAgB,GAG5BG,EAAe,IAAI,IAAI5C,IAAI2C,EAC5BjB,IAAImB,GAAKA,EAAEC,aACXjB,OAAOC,WAGNiB,QAA2BZ,EAAeS,EAAcR,EAAQ,EAAGI,GAEzE,MAAO,IAAIG,KAAcI,EAC7B,CAAE,MAAO9C,GAEL,OADAoB,QAAQpB,MAAM,oCAAoCmC,KAAUnC,GACrD,EACX,CACJ,EAGM+C,EAAqB,IAAI,IAAIhD,IAAIkC,EAClCR,IAAImB,GAAKA,EAAEC,aACXjB,OAAOC,WAENa,QAAkBR,EAAea,GAMjCC,EAHW,IAAIf,KAAkBS,GAGVd,OAAO,CAAClB,EAAMuC,EAAOC,IAC9CD,IAAUC,EAAKC,UAAUP,GAAKA,EAAE1B,KAAOR,EAAKQ,KAI1CkC,EAAOC,EAAkBL,GAC/BtD,EAAgB0D,GAChBxD,EAAgBwD,EAEpB,CAAE,MAAOE,GACLlC,QAAQpB,MAAM,SAAUsD,GAGxB,IAAK,IAADC,EACAnC,QAAQoC,IAAI,gCAIZ,KAD+B,QAAdD,EAAG/C,SAAW,IAAA+C,OAAA,EAAXA,EAAarC,IACf,CACd,MAAQT,MAAQC,KAAM+C,UAAyBnD,EAASK,KAAKC,UAC7D,IAAK6C,EAED,YADAxD,EAASb,EAAE,uBAGfoB,EAAciD,CAClB,CAEA,MAAQhD,KAAMiD,EAAiB1D,MAAO2D,SAAsBrD,EACvDS,KAAK,qBACLC,OAAO,gTASPC,GAAG,WAAYT,EAAYU,IAC3BK,MAAM,IAEX,IAAKoC,GAAeD,GAAmBA,EAAgBrB,OAAS,EAAG,CAC/D,MAAMuB,EAAcF,EACfjC,IAAIoC,GAAKA,EAAEC,OACXlC,OAAOC,SACPJ,IAAImB,IAAC,IACCA,EACHC,YAAa,KACbkB,SAAU,MAGlB3C,QAAQoC,IAAI,8BAA+BI,EAAYvB,OAAQ,SAC/D3C,EAAgBkE,GAChBhE,EAAgBgE,GAChB3D,EAASb,EAAE,yBACf,MACIgC,QAAQoC,IAAI,8BAA+BG,GAC3CjE,EAAgB,IAChBE,EAAgB,IAChBK,EAASb,EAAE,oBAEnB,CAAE,MAAO4E,GACL5C,QAAQpB,MAAM,kBAAmBgE,GACjCtE,EAAgB,IAChBE,EAAgB,IAChBK,EAASb,EAAE,oBACf,CACJ,CAAC,QACGG,GAAW,EACf,GAGJ0E,IACD,CAAC7E,KAGJgB,EAAAA,EAAAA,WAAU,KACN,IAAKF,EAAWgE,OAEZ,YADAtE,EAAgBH,GAIpB,MAAM0E,EAAcC,GACTA,EAAMxC,OAAOyC,IAChB,MAAMC,EAAgBD,EAAKE,MAAMC,cAAcC,SAASvE,EAAWsE,gBAC/CH,EAAKnD,GAAGsD,cAAcC,SAASvE,EAAWsE,eAExDE,EAAmBL,EAAKN,SAAWI,EAAWE,EAAKN,UAAY,GAErE,OAAOO,GAAiBI,EAAiBrC,OAAS,IACnDZ,IAAI4C,IAAI,IACJA,EACHN,SAAUM,EAAKN,SAAWI,EAAWE,EAAKN,UAAY,MAI9DnE,EAAgBuE,EAAW1E,KAC5B,CAACS,EAAYT,IAEhB,MAAM4D,EAAqBS,IACvB,IAAKA,GAA0B,IAAjBA,EAAMzB,OAChB,MAAO,GAGX,MAAMsC,EAAU,IAAIC,IACdC,EAAY,GA0BlB,OAvBAf,EAAMzF,QAAQqC,IACViE,EAAQG,IAAIpE,EAAKQ,GAAI,IACdR,EACHqD,SAAU,OAKlBD,EAAMzF,QAAQqC,IACV,GAAIA,EAAKmC,YAAa,CAClB,MAAMkC,EAASJ,EAAQK,IAAItE,EAAKmC,aAC5BkC,EACAA,EAAOhB,SAASrF,KAAKiG,EAAQK,IAAItE,EAAKQ,KAGtC2D,EAAUnG,KAAKiG,EAAQK,IAAItE,EAAKQ,IAExC,MAEI2D,EAAUnG,KAAKiG,EAAQK,IAAItE,EAAKQ,OAIjC2D,GAmCLI,EAAeC,IACjB,OAAQA,GACJ,IAAK,QACD,OAAOvG,EAAAA,EAAAA,KAACwG,EAAAA,IAAO,CAAC1H,UAAU,sBAC9B,IAAK,QACD,OAAOkB,EAAAA,EAAAA,KAACyG,EAAAA,IAAM,CAAC3H,UAAU,sBAC7B,IAAK,WACD,OAAOkB,EAAAA,EAAAA,KAACyG,EAAAA,IAAM,CAAC3H,UAAU,mBAC7B,QACI,OAAOkB,EAAAA,EAAAA,KAACyG,EAAAA,IAAM,CAAC3H,UAAU,0BAI/B4H,EAAiB,SAAChB,GAAqB,IAAflC,EAAKC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,EAClC,MAAMkD,EAAcjB,EAAKN,UAAYM,EAAKN,SAAS1B,OAAS,EACtDkD,EAAa1F,EAAc2F,IAAInB,EAAKnD,IACpCuE,EAAsB,GAARtD,EAEpB,OACIuD,EAAAA,EAAAA,MAAA,OAAmBjI,UAAU,OAAMsG,SAAA,EAC/B2B,EAAAA,EAAAA,MAAA,OACIjI,UAAU,8CACVkI,MAAO,CAAEF,YAAa,GAAGA,MAAiBG,OAAQN,EAAc,UAAY,WAC5EO,QAASA,IAAMP,GAvDXQ,KAChB,MAAMC,EAAc,IAAIhG,IAAIF,GACxBkG,EAAYP,IAAIM,GAChBC,EAAYC,OAAOF,GAEnBC,EAAYE,IAAIH,GAEpBhG,EAAiBiG,IAgDyBG,CAAW7B,EAAKnD,IAAI6C,SAAA,CAEjDuB,EACGC,GACI5G,EAAAA,EAAAA,KAACwH,EAAAA,IAAa,CAAC1I,UAAU,qBAEzBkB,EAAAA,EAAAA,KAACyH,EAAAA,IAAc,CAAC3I,UAAU,qBAG9BkB,EAAAA,EAAAA,KAAA,QAAMlB,UAAU,SAGnBwH,EAAYZ,EAAKa,OAElBvG,EAAAA,EAAAA,KAAA,QAAMlB,UAAU,OAAMsG,SACjBM,EAAKE,SAGV5F,EAAAA,EAAAA,KAAA,QAAMlB,UAAU,mBAAkBsG,SAC7BM,EAAKgC,cAGTf,IACG3G,EAAAA,EAAAA,KAAA,QAAMlB,UAAU,6BAA4BsG,SACvCM,EAAKN,SAAS1B,YAK1BiD,GAAeC,IACZ5G,EAAAA,EAAAA,KAAA,OAAAoF,SACKM,EAAKN,SAAStC,IAAI6E,GAASjB,EAAeiB,EAAOnE,EAAQ,QAnC5DkC,EAAKnD,GAwCvB,EAoBA,OAAI5B,GAEIX,EAAAA,EAAAA,KAAC4H,EAAAA,EAAS,CAAC9I,UAAU,mDAAmDkI,MAAO,CAAEa,UAAW,SAAUzC,UAClG2B,EAAAA,EAAAA,MAAA,OAAKjI,UAAU,cAAasG,SAAA,EACxBpF,EAAAA,EAAAA,KAACG,EAAO,CAACE,UAAU,SAASkG,KAAK,SAASzH,UAAU,UACpDkB,EAAAA,EAAAA,KAAA,OAAAoF,SAAM3E,EAAE,gCAMpBY,GAEIrB,EAAAA,EAAAA,KAAC4H,EAAAA,EAAS,CAAAxC,UACNpF,EAAAA,EAAAA,KAAC8H,EAAAA,EAAK,CAAC1H,QAAQ,SAAQgF,SAClB/D,OAOb0F,EAAAA,EAAAA,MAACa,EAAAA,EAAS,CAAAxC,SAAA,EACNpF,EAAAA,EAAAA,KAACvB,EAAAA,EAAG,CAACK,UAAU,OAAMsG,UACjB2B,EAAAA,EAAAA,MAACgB,EAAAA,EAAG,CAAA3C,SAAA,EACApF,EAAAA,EAAAA,KAAA,MAAAoF,SAAK3E,EAAE,6BACPT,EAAAA,EAAAA,KAAA,KAAGlB,UAAU,aAAYsG,SAAE3E,EAAE,qCAIrCT,EAAAA,EAAAA,KAACvB,EAAAA,EAAG,CAACK,UAAU,OAAMsG,UACjBpF,EAAAA,EAAAA,KAAC+H,EAAAA,EAAG,CAAA3C,UACApF,EAAAA,EAAAA,KAACgI,EAAAA,EAAI,CAAA5C,UACDpF,EAAAA,EAAAA,KAACgI,EAAAA,EAAKC,KAAI,CAAA7C,UACN2B,EAAAA,EAAAA,MAACtI,EAAAA,EAAG,CAACK,UAAU,kBAAiBsG,SAAA,EAC5BpF,EAAAA,EAAAA,KAAC+H,EAAAA,EAAG,CAACG,GAAI,EAAE9C,UACP2B,EAAAA,EAAAA,MAACoB,EAAAA,EAAKC,MAAK,CAAAhD,SAAA,EACPpF,EAAAA,EAAAA,KAACmI,EAAAA,EAAKE,MAAK,CAAAjD,SAAE3E,EAAE,mBACfT,EAAAA,EAAAA,KAACmI,EAAAA,EAAKG,QAAO,CACTC,KAAK,OACLC,YAAa/H,EAAE,yBACfgI,MAAOlH,EACPmH,SAAWC,GAAMnH,EAAcmH,EAAEC,OAAOH,eAIpDzI,EAAAA,EAAAA,KAAC+H,EAAAA,EAAG,CAACG,GAAI,EAAE9C,UACP2B,EAAAA,EAAAA,MAAA,OAAKjI,UAAU,eAAcsG,SAAA,EACzB2B,EAAAA,EAAAA,MAAC8B,EAAAA,EAAM,CACHzI,QAAQ,kBACRE,KAAK,KACL4G,QAxJtB4B,KACd,MAAMC,EAAa,IAAI3H,IACjB4H,EAAkBvD,IACpBA,EAAM/F,QAAQgG,IACNA,EAAKN,UAAYM,EAAKN,SAAS1B,OAAS,IACxCqF,EAAWzB,IAAI5B,EAAKnD,IACpByG,EAAetD,EAAKN,cAIhC4D,EAAehI,GACfG,EAAiB4H,IA6IsC3D,SAAA,EAEnBpF,EAAAA,EAAAA,KAACiJ,EAAAA,IAAiB,CAACnK,UAAU,SAC5B2B,EAAE,kBAEPsG,EAAAA,EAAAA,MAAC8B,EAAAA,EAAM,CACHzI,QAAQ,oBACRE,KAAK,KACL4G,QAlJpBgC,KAChB/H,EAAiB,IAAIC,MAiJoCgE,SAAA,EAErBpF,EAAAA,EAAAA,KAACmJ,EAAAA,IAAmB,CAACrK,UAAU,SAC9B2B,EAAE,oCAUnCsG,EAAAA,EAAAA,MAACtI,EAAAA,EAAG,CAACK,UAAU,OAAMsG,SAAA,EACjBpF,EAAAA,EAAAA,KAAC+H,EAAAA,EAAG,CAACG,GAAI,EAAE9C,UACPpF,EAAAA,EAAAA,KAACgI,EAAAA,EAAI,CAAClJ,UAAU,wBAAuBsG,UACnC2B,EAAAA,EAAAA,MAACiB,EAAAA,EAAKC,KAAI,CAAA7C,SAAA,EACNpF,EAAAA,EAAAA,KAAA,MAAAoF,SAAK3E,EAAE,kBACPT,EAAAA,EAAAA,KAAA,MAAAoF,SA/FDK,KACnB,IAAI2D,EAAQ,EACZ,MAAMC,EAAcC,IAChBA,EAAS5J,QAAQgG,IACb0D,IACI1D,EAAKN,UAAYM,EAAKN,SAAS1B,OAAS,GACxC2F,EAAW3D,EAAKN,aAK5B,OADAiE,EAAW5D,GACJ2D,GAoFkBG,CAAcvI,aAI/BhB,EAAAA,EAAAA,KAAC+H,EAAAA,EAAG,CAACG,GAAI,EAAE9C,UACPpF,EAAAA,EAAAA,KAACgI,EAAAA,EAAI,CAAClJ,UAAU,wBAAuBsG,UACnC2B,EAAAA,EAAAA,MAACiB,EAAAA,EAAKC,KAAI,CAAA7C,SAAA,EACNpF,EAAAA,EAAAA,KAAA,MAAAoF,SAAK3E,EAAE,iBACPT,EAAAA,EAAAA,KAAA,MAAAoF,SAxFbpE,EAAa0C,iBA4FZ1D,EAAAA,EAAAA,KAAC+H,EAAAA,EAAG,CAACG,GAAI,EAAE9C,UACPpF,EAAAA,EAAAA,KAACgI,EAAAA,EAAI,CAAClJ,UAAU,qBAAoBsG,UAChC2B,EAAAA,EAAAA,MAACiB,EAAAA,EAAKC,KAAI,CAAA7C,SAAA,EACNpF,EAAAA,EAAAA,KAAA,MAAAoF,SAAK3E,EAAE,qBACPT,EAAAA,EAAAA,KAAA,MAAAoF,SAAKlE,EAAcZ,kBAMnCN,EAAAA,EAAAA,KAACvB,EAAAA,EAAG,CAAA2G,UACApF,EAAAA,EAAAA,KAAC+H,EAAAA,EAAG,CAAA3C,UACA2B,EAAAA,EAAAA,MAACiB,EAAAA,EAAI,CAAA5C,SAAA,EACD2B,EAAAA,EAAAA,MAACiB,EAAAA,EAAKwB,OAAM,CAAApE,SAAA,EACRpF,EAAAA,EAAAA,KAAA,MAAIlB,UAAU,OAAMsG,SAAE3E,EAAE,oBACxBT,EAAAA,EAAAA,KAAA,SAAOlB,UAAU,aAAYsG,SACxB3E,EAAE,kCAGXT,EAAAA,EAAAA,KAACgI,EAAAA,EAAKC,KAAI,CAACjB,MAAO,CAAEyC,UAAW,QAASC,UAAW,QAAStE,SAC/B,IAAxBpE,EAAa0C,QACV1D,EAAAA,EAAAA,KAAA,OAAKlB,UAAU,8BAA6BsG,SAC1B3E,EAAbc,EAAe,oBAAyB,uBAG7CvB,EAAAA,EAAAA,KAAA,OAAAoF,SACKpE,EAAa8B,IAAI4C,GAAQgB,EAAehB,kB,oHC1e7E,MAAMiE,GAAgBC,EAAAA,EAAAA,GAAiB,MACvCD,EAAczJ,YAAc,gBAC5B,MAAM2J,EAA4BnL,EAAAA,WAAiB,CAAAC,EAKhDC,KAAQ,IALyC,UAClDE,EAAS,SACTD,EACAE,GAAIC,EAAY2K,KACb1K,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,kBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGP4K,EAAa3J,YAAc,eAC3B,U,cChBA,MAAM4J,EAAyBpL,EAAAA,WAAiB,CAAAC,EAK7CC,KAAQ,IALsC,UAC/CE,EAAS,SACTD,EACAE,GAAIC,EAAY+K,EAAAA,KACb9K,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,eACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGP6K,EAAU5J,YAAc,YACxB,U,wBCRA,MAAM4H,EAAqBpJ,EAAAA,WAAiB,CAACsL,EAAmBpL,KAC9D,MAAM,SACJC,EAAQ,KACRoL,GAAO,EAAI,WACXC,EAAa,cAAa,aAC1BC,EAAY,UACZrL,EAAS,SACTsG,EAAQ,QACRhF,EAAU,UAAS,QACnBgK,EAAO,YACPC,EAAW,WACXC,EAAaC,EAAAA,KACVtL,IACDuL,EAAAA,EAAAA,IAAgBR,EAAmB,CACrCC,KAAM,YAEFQ,GAAStL,EAAAA,EAAAA,IAAmBN,EAAU,SACtC6L,GAAcC,EAAAA,EAAAA,GAAiBhC,IAC/ByB,GACFA,GAAQ,EAAOzB,KAGbiC,GAA4B,IAAfN,EAAsBC,EAAAA,EAAOD,EAC1CO,GAAqB9D,EAAAA,EAAAA,MAAM,MAAO,CACtCR,KAAM,WACDqE,OAAqBjH,EAAR1E,EAClBL,IAAKA,EACLE,UAAWmB,IAAWnB,EAAW2L,EAAQrK,GAAW,GAAGqK,KAAUrK,IAAWiK,GAAe,GAAGI,iBAC9FrF,SAAU,CAACiF,IAA4BrK,EAAAA,EAAAA,KAAK8K,EAAAA,EAAa,CACvD5D,QAASwD,EACT,aAAcR,EACd9J,QAAS+J,IACP/E,KAEN,OAAKwF,GACe5K,EAAAA,EAAAA,KAAK4K,EAAY,CACnCG,eAAe,KACZ9L,EACHL,SAAK+E,EACLN,GAAI4G,EACJ7E,SAAUyF,IANYZ,EAAOY,EAAQ,OASzC/C,EAAM5H,YAAc,QACpB,QAAe8K,OAAOC,OAAOnD,EAAO,CAClCoD,KAAMpB,EACNqB,QAAStB,G", "sources": ["../node_modules/react-bootstrap/esm/Row.js", "../node_modules/react-bootstrap/esm/Spinner.js", "pages/agent/Recommend.js", "../node_modules/react-bootstrap/esm/AlertHeading.js", "../node_modules/react-bootstrap/esm/AlertLink.js", "../node_modules/react-bootstrap/esm/Alert.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Row = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'row');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const sizePrefix = `${decoratedBsPrefix}-cols`;\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let cols;\n    if (propValue != null && typeof propValue === 'object') {\n      ({\n        cols\n      } = propValue);\n    } else {\n      cols = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (cols != null) classes.push(`${sizePrefix}${infix}-${cols}`);\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, decoratedBsPrefix, ...classes)\n  });\n});\nRow.displayName = 'Row';\nexport default Row;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Spinner = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  variant,\n  animation = 'border',\n  size,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  className,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'spinner');\n  const bsSpinnerPrefix = `${bsPrefix}-${animation}`;\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, bsSpinnerPrefix, size && `${bsSpinnerPrefix}-${size}`, variant && `text-${variant}`)\n  });\n});\nSpinner.displayName = 'Spinner';\nexport default Spinner;", "import React, { useState, useEffect } from 'react';\nimport { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Spinner, <PERSON><PERSON>, But<PERSON>, Form } from 'react-bootstrap';\nimport { FaChevronDown, FaChevronRight, FaUser, FaUsers, FaExpandArrowsAlt, FaCompressArrowsAlt, FaSearch } from 'react-icons/fa';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst Recommend = () => {\n    const { t } = useTranslation();\n    const [loading, setLoading] = useState(true);\n    const [referralTree, setReferralTree] = useState([]);\n    const [filteredTree, setFilteredTree] = useState([]);\n    const [expandedNodes, setExpandedNodes] = useState(new Set());\n    const [error, setError] = useState(null);\n    const [searchTerm, setSearchTerm] = useState('');\n\n    useEffect(() => {\n        const fetchReferralData = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            setError(null);\n\n            let currentUser = null;\n            let customers = [];\n            let customersError = null;\n\n\n            try {\n                const { data: { user } } = await supabase.auth.getUser();\n                currentUser = user;\n\n                if (!user) {\n                    setError(t('user_not_logged_in'));\n                    setLoading(false);\n                    return;\n                }\n\n                // First, get the agent's maker_id to determine scope\n                const { data: agentProfile, error: agentError } = await supabase\n                    .from('agent_profiles')\n                    .select('maker_id')\n                    .eq('user_id', user.id)\n                    .single();\n\n                if (agentError) {\n                    console.error('Error fetching agent profile:', agentError);\n                    setError(t('agent_profile_not_found'));\n                    setLoading(false);\n                    return;\n                }\n\n                // Step 1: 获取 customer_profiles 列表\n                const { data: customerProfiles, error: profileError } = await supabase\n                    .from('customer_profiles')\n                    .select('user_id')  // 不嵌套 users 表\n                    .eq('agent_id', user.id)\n                    .limit(100);\n\n                if (profileError) {\n                    console.error('Error fetching customer profiles:', profileError);\n                    setError(t('failed_to_load_referral_data'));\n                    setLoading(false);\n                    return;\n                }\n\n                // Step 2: 用 user_id 去单独获取 users 表\n                const userIds = customerProfiles.map(p => p.user_id).filter(Boolean);\n\n                const { data: usersData, error: usersError } = await supabase\n                    .from('users')\n                    .select('id, email, referred_by, created_at, role, invite_code')\n                    .in('id', userIds);\n\n                if (usersError) {\n                    console.error('Error fetching user info:', usersError);\n                    setError(t('failed_to_load_referral_data'));\n                    setLoading(false);\n                    return;\n                }\n\n                if (customersError) {\n                    console.error('Error fetching customers:', customersError);\n                    setError(t('failed_to_load_referral_data'));\n                    setLoading(false);\n                    return;\n                }\n\n                // Extract user data from customers\n                const customerUsers = usersData || [];\n\n                // Get referrer information recursively (up to 3 levels to avoid infinite loops)\n                const fetchReferrers = async (userIds, level = 0, maxLevel = 3) => {\n                    if (level >= maxLevel || userIds.length === 0) {\n                        return [];\n                    }\n\n                    try {\n                        const { data: referrerData, error: referrerError } = await supabase\n                            .from('users')\n                            .select('id, email, referred_by, created_at, role, invite_code')\n                            .in('id', userIds)\n                            .limit(50); // Limit each level to prevent large queries\n\n                        if (referrerError) {\n                            console.error(`Error fetching referrers at level ${level}:`, referrerError);\n                            return [];\n                        }\n\n                        const referrers = referrerData || [];\n\n                        // Get next level referrer IDs\n                        const nextLevelIds = [...new Set(referrers\n                            .map(u => u.referred_by)\n                            .filter(Boolean))];\n\n                        // Recursively fetch next level\n                        const nextLevelReferrers = await fetchReferrers(nextLevelIds, level + 1, maxLevel);\n\n                        return [...referrers, ...nextLevelReferrers];\n                    } catch (error) {\n                        console.error(`Error in fetchReferrers at level ${level}:`, error);\n                        return [];\n                    }\n                };\n\n                // Get unique referred_by IDs from customers\n                const initialReferrerIds = [...new Set(customerUsers\n                    .map(u => u.referred_by)\n                    .filter(Boolean))];\n\n                const referrers = await fetchReferrers(initialReferrerIds);\n\n                // Combine customer users and their referrers\n                const allUsers = [...customerUsers, ...referrers];\n\n                // Remove duplicates based on user ID\n                const uniqueUsers = allUsers.filter((user, index, self) =>\n                    index === self.findIndex(u => u.id === user.id)\n                );\n\n                // Build the referral tree\n                const tree = buildReferralTree(uniqueUsers);\n                setReferralTree(tree);\n                setFilteredTree(tree);\n\n            } catch (err) {\n                console.error('Error:', err);\n\n                // Fallback: try to get just the direct customers without referrer data\n                try {\n                    console.log('Attempting fallback query...');\n\n                    // Use currentUser if available, otherwise try to get user again\n                    const userIdToUse = currentUser?.id;\n                    if (!userIdToUse) {\n                        const { data: { user: fallbackUser } } = await supabase.auth.getUser();\n                        if (!fallbackUser) {\n                            setError(t('user_not_logged_in'));\n                            return;\n                        }\n                        currentUser = fallbackUser;\n                    }\n\n                    const { data: simpleCustomers, error: simpleError } = await supabase\n                        .from('customer_profiles')\n                        .select(`\n                            users (\n                                id,\n                                email,\n                                created_at,\n                                role,\n                                invite_code\n                            )\n                        `)\n                        .eq('agent_id', currentUser.id)\n                        .limit(50); // Further limit for fallback\n\n                    if (!simpleError && simpleCustomers && simpleCustomers.length > 0) {\n                        const simpleUsers = simpleCustomers\n                            .map(c => c.users)\n                            .filter(Boolean)\n                            .map(u => ({\n                                ...u,\n                                referred_by: null,\n                                children: []\n                            })); // Remove referral info for simplicity\n\n                        console.log('Fallback successful, loaded', simpleUsers.length, 'users');\n                        setReferralTree(simpleUsers);\n                        setFilteredTree(simpleUsers);\n                        setError(t('limited_referral_data'));\n                    } else {\n                        console.log('Fallback failed or no data:', simpleError);\n                        setReferralTree([]);\n                        setFilteredTree([]);\n                        setError(t('no_referral_data'));\n                    }\n                } catch (fallbackErr) {\n                    console.error('Fallback error:', fallbackErr);\n                    setReferralTree([]);\n                    setFilteredTree([]);\n                    setError(t('unexpected_error'));\n                }\n            } finally {\n                setLoading(false);\n            }\n        };\n\n        fetchReferralData();\n    }, [t]);\n\n    // Filter tree based on search term\n    useEffect(() => {\n        if (!searchTerm.trim()) {\n            setFilteredTree(referralTree);\n            return;\n        }\n\n        const filterTree = (nodes) => {\n            return nodes.filter(node => {\n                const matchesSearch = node.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                                    node.id.toLowerCase().includes(searchTerm.toLowerCase());\n\n                const filteredChildren = node.children ? filterTree(node.children) : [];\n\n                return matchesSearch || filteredChildren.length > 0;\n            }).map(node => ({\n                ...node,\n                children: node.children ? filterTree(node.children) : []\n            }));\n        };\n\n        setFilteredTree(filterTree(referralTree));\n    }, [searchTerm, referralTree]);\n\n    const buildReferralTree = (users) => {\n        if (!users || users.length === 0) {\n            return [];\n        }\n\n        const userMap = new Map();\n        const rootUsers = [];\n\n        // Create a map of all users\n        users.forEach(user => {\n            userMap.set(user.id, {\n                ...user,\n                children: []\n            });\n        });\n\n        // Build the tree structure\n        users.forEach(user => {\n            if (user.referred_by) {\n                const parent = userMap.get(user.referred_by);\n                if (parent) {\n                    parent.children.push(userMap.get(user.id));\n                } else {\n                    // Parent not found, treat as root\n                    rootUsers.push(userMap.get(user.id));\n                }\n            } else {\n                // No referrer, this is a root user\n                rootUsers.push(userMap.get(user.id));\n            }\n        });\n\n        return rootUsers;\n    };\n\n    const toggleNode = (userId) => {\n        const newExpanded = new Set(expandedNodes);\n        if (newExpanded.has(userId)) {\n            newExpanded.delete(userId);\n        } else {\n            newExpanded.add(userId);\n        }\n        setExpandedNodes(newExpanded);\n    };\n\n    const expandAll = () => {\n        const allNodeIds = new Set();\n        const collectNodeIds = (nodes) => {\n            nodes.forEach(node => {\n                if (node.children && node.children.length > 0) {\n                    allNodeIds.add(node.id);\n                    collectNodeIds(node.children);\n                }\n            });\n        };\n        collectNodeIds(filteredTree);\n        setExpandedNodes(allNodeIds);\n    };\n\n    const collapseAll = () => {\n        setExpandedNodes(new Set());\n    };\n\n    const formatUserId = (id) => {\n        return id.substring(0, 8);\n    };\n\n    const getRoleIcon = (role) => {\n        switch (role) {\n            case 'maker':\n                return <FaUsers className=\"text-primary me-1\" />;\n            case 'agent':\n                return <FaUser className=\"text-success me-1\" />;\n            case 'customer':\n                return <FaUser className=\"text-info me-1\" />;\n            default:\n                return <FaUser className=\"text-secondary me-1\" />;\n        }\n    };\n\n    const renderTreeNode = (node, level = 0) => {\n        const hasChildren = node.children && node.children.length > 0;\n        const isExpanded = expandedNodes.has(node.id);\n        const paddingLeft = level * 20;\n\n        return (\n            <div key={node.id} className=\"mb-1\">\n                <div \n                    className=\"d-flex align-items-center p-2 border-bottom\"\n                    style={{ paddingLeft: `${paddingLeft}px`, cursor: hasChildren ? 'pointer' : 'default' }}\n                    onClick={() => hasChildren && toggleNode(node.id)}\n                >\n                    {hasChildren ? (\n                        isExpanded ? (\n                            <FaChevronDown className=\"me-2 text-muted\" />\n                        ) : (\n                            <FaChevronRight className=\"me-2 text-muted\" />\n                        )\n                    ) : (\n                        <span className=\"me-4\"></span>\n                    )}\n                    \n                    {getRoleIcon(node.role)}\n                    \n                    <span className=\"me-2\">\n                        {node.email}\n                    </span>\n                    \n                    <span className=\"text-muted small\">\n                        {node.invite_code}\n                    </span>\n                    \n                    {hasChildren && (\n                        <span className=\"ms-auto badge bg-secondary\">\n                            {node.children.length}\n                        </span>\n                    )}\n                </div>\n                \n                {hasChildren && isExpanded && (\n                    <div>\n                        {node.children.map(child => renderTreeNode(child, level + 1))}\n                    </div>\n                )}\n            </div>\n        );\n    };\n\n    const getTotalUsers = (nodes) => {\n        let total = 0;\n        const countNodes = (nodeList) => {\n            nodeList.forEach(node => {\n                total++;\n                if (node.children && node.children.length > 0) {\n                    countNodes(node.children);\n                }\n            });\n        };\n        countNodes(nodes);\n        return total;\n    };\n\n    const getRootUsersCount = () => {\n        return filteredTree.length;\n    };\n\n    if (loading) {\n        return (\n            <Container className=\"d-flex justify-content-center align-items-center\" style={{ minHeight: '400px' }}>\n                <div className=\"text-center\">\n                    <Spinner animation=\"border\" role=\"status\" className=\"mb-3\" />\n                    <div>{t('loading_referral_data')}</div>\n                </div>\n            </Container>\n        );\n    }\n\n    if (error) {\n        return (\n            <Container>\n                <Alert variant=\"danger\">\n                    {error}\n                </Alert>\n            </Container>\n        );\n    }\n\n    return (\n        <Container>\n            <Row className=\"mb-4\">\n                <Col>\n                    <h2>{t('referral_relationships')}</h2>\n                    <p className=\"text-muted\">{t('referral_tree_description')}</p>\n                </Col>\n            </Row>\n\n            <Row className=\"mb-3\">\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Row className=\"align-items-end\">\n                                <Col md={4}>\n                                    <Form.Group>\n                                        <Form.Label>{t('search_users')}</Form.Label>\n                                        <Form.Control\n                                            type=\"text\"\n                                            placeholder={t('search_by_email_or_id')}\n                                            value={searchTerm}\n                                            onChange={(e) => setSearchTerm(e.target.value)}\n                                        />\n                                    </Form.Group>\n                                </Col>\n                                <Col md={4}>\n                                    <div className=\"d-flex gap-2\">\n                                        <Button\n                                            variant=\"outline-primary\"\n                                            size=\"sm\"\n                                            onClick={expandAll}\n                                        >\n                                            <FaExpandArrowsAlt className=\"me-1\" />\n                                            {t('expand_all')}\n                                        </Button>\n                                        <Button\n                                            variant=\"outline-secondary\"\n                                            size=\"sm\"\n                                            onClick={collapseAll}\n                                        >\n                                            <FaCompressArrowsAlt className=\"me-1\" />\n                                            {t('collapse_all')}\n                                        </Button>\n                                    </div>\n                                </Col>\n                            </Row>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            <Row className=\"mb-3\">\n                <Col md={4}>\n                    <Card className=\"bg-primary text-white\">\n                        <Card.Body>\n                            <h5>{t('total_users')}</h5>\n                            <h3>{getTotalUsers(filteredTree)}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={4}>\n                    <Card className=\"bg-success text-white\">\n                        <Card.Body>\n                            <h5>{t('root_users')}</h5>\n                            <h3>{getRootUsersCount()}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={4}>\n                    <Card className=\"bg-info text-white\">\n                        <Card.Body>\n                            <h5>{t('expanded_nodes')}</h5>\n                            <h3>{expandedNodes.size}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Header>\n                            <h5 className=\"mb-0\">{t('referral_tree')}</h5>\n                            <small className=\"text-muted\">\n                                {t('click_to_expand_collapse')}\n                            </small>\n                        </Card.Header>\n                        <Card.Body style={{ maxHeight: '600px', overflowY: 'auto' }}>\n                            {filteredTree.length === 0 ? (\n                                <div className=\"text-center text-muted py-4\">\n                                    {searchTerm ? t('no_search_results') : t('no_referral_data')}\n                                </div>\n                            ) : (\n                                <div>\n                                    {filteredTree.map(node => renderTreeNode(node))}\n                                </div>\n                            )}\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default Recommend;\n", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH4 = divWithClassName('h4');\nDivStyledAsH4.displayName = 'DivStyledAsH4';\nconst AlertHeading = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH4,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'alert-heading');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nAlertHeading.displayName = 'AlertHeading';\nexport default AlertHeading;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Anchor from '@restart/ui/Anchor';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst AlertLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = Anchor,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'alert-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nAlertLink.displayName = 'AlertLink';\nexport default AlertLink;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useUncontrolled } from 'uncontrollable';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AlertHeading from './AlertHeading';\nimport AlertLink from './AlertLink';\nimport Fade from './Fade';\nimport CloseButton from './CloseButton';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst Alert = /*#__PURE__*/React.forwardRef((uncontrolledProps, ref) => {\n  const {\n    bsPrefix,\n    show = true,\n    closeLabel = 'Close alert',\n    closeVariant,\n    className,\n    children,\n    variant = 'primary',\n    onClose,\n    dismissible,\n    transition = Fade,\n    ...props\n  } = useUncontrolled(uncontrolledProps, {\n    show: 'onClose'\n  });\n  const prefix = useBootstrapPrefix(bsPrefix, 'alert');\n  const handleClose = useEventCallback(e => {\n    if (onClose) {\n      onClose(false, e);\n    }\n  });\n  const Transition = transition === true ? Fade : transition;\n  const alert = /*#__PURE__*/_jsxs(\"div\", {\n    role: \"alert\",\n    ...(!Transition ? props : undefined),\n    ref: ref,\n    className: classNames(className, prefix, variant && `${prefix}-${variant}`, dismissible && `${prefix}-dismissible`),\n    children: [dismissible && /*#__PURE__*/_jsx(CloseButton, {\n      onClick: handleClose,\n      \"aria-label\": closeLabel,\n      variant: closeVariant\n    }), children]\n  });\n  if (!Transition) return show ? alert : null;\n  return /*#__PURE__*/_jsx(Transition, {\n    unmountOnExit: true,\n    ...props,\n    ref: undefined,\n    in: show,\n    children: alert\n  });\n});\nAlert.displayName = 'Alert';\nexport default Object.assign(Alert, {\n  Link: AlertLink,\n  Heading: AlertHeading\n});"], "names": ["Row", "React", "_ref", "ref", "bsPrefix", "className", "as", "Component", "props", "decoratedBsPrefix", "useBootstrapPrefix", "breakpoints", "useBootstrapBreakpoints", "minBreakpoint", "useBootstrapMinBreakpoint", "sizePrefix", "classes", "for<PERSON>ach", "brkPoint", "propValue", "cols", "infix", "push", "_jsx", "classNames", "displayName", "Spinner", "variant", "animation", "size", "bsSpinnerPrefix", "Recommend", "t", "useTranslation", "loading", "setLoading", "useState", "referralTree", "setReferralTree", "filteredTree", "setFilteredTree", "expandedNodes", "setExpandedNodes", "Set", "error", "setError", "searchTerm", "setSearchTerm", "useEffect", "async", "supabase", "getSupabase", "currentUser", "data", "user", "auth", "getUser", "agentProfile", "agent<PERSON><PERSON>r", "from", "select", "eq", "id", "single", "console", "customerProfiles", "profileError", "limit", "userIds", "map", "p", "user_id", "filter", "Boolean", "usersData", "usersError", "in", "customerUsers", "fetchReferrers", "level", "arguments", "length", "undefined", "maxLevel", "referrerData", "referrerError", "referrers", "nextLevelIds", "u", "referred_by", "nextLevelReferrers", "initialReferrerIds", "uniqueUsers", "index", "self", "findIndex", "tree", "buildReferralTree", "err", "_currentUser", "log", "fallbackUser", "simpleCustomers", "simpleError", "simpleUsers", "c", "users", "children", "fallbackErr", "fetchReferralData", "trim", "filterTree", "nodes", "node", "matchesSearch", "email", "toLowerCase", "includes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "userMap", "Map", "rootUsers", "set", "parent", "get", "getRoleIcon", "role", "FaUsers", "FaUser", "renderTreeNode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isExpanded", "has", "paddingLeft", "_jsxs", "style", "cursor", "onClick", "userId", "newExpanded", "delete", "add", "toggleNode", "FaChevronDown", "FaChevronRight", "invite_code", "child", "Container", "minHeight", "<PERSON><PERSON>", "Col", "Card", "Body", "md", "Form", "Group", "Label", "Control", "type", "placeholder", "value", "onChange", "e", "target", "<PERSON><PERSON>", "expandAll", "allNodeIds", "collectNodeIds", "FaExpandArrowsAlt", "collapseAll", "FaCompressArrowsAlt", "total", "countNodes", "nodeList", "getTotalUsers", "Header", "maxHeight", "overflowY", "DivStyledAsH4", "divWithClassName", "AlertHeading", "AlertLink", "<PERSON><PERSON>", "uncontrolledProps", "show", "<PERSON><PERSON><PERSON><PERSON>", "closeVariant", "onClose", "dismissible", "transition", "Fade", "useUncontrolled", "prefix", "handleClose", "useEventCallback", "Transition", "alert", "CloseButton", "unmountOnExit", "Object", "assign", "Link", "Heading"], "sourceRoot": ""}