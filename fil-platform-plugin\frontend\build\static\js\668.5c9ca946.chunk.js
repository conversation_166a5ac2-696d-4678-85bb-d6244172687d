"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[668],{668:(e,s,r)=>{r.r(s),r.d(s,{default:()=>f});var a=r(5043),d=r(3519),l=r(1072),c=r(8602),t=r(8628),n=r(4282),o=r(4117),i=r(1283),m=r(4312),x=r(579);const f=()=>{const{t:e}=(0,o.Bd)(),[s,r]=(0,a.useState)(null),[f,h]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{(async()=>{const e=(0,m.b)();if(!e)return;h(!0);const{data:{user:s}}=await e.auth.getUser();if(!s)return void h(!1);const{data:a,error:d}=await e.from("maker_profiles").select("*").eq("user_id",s.id).single();d?console.error("Error fetching maker profile:",d):r(a),h(!1)})()},[]),f?(0,x.jsx)("div",{children:e("loading_maker_dashboard")}):s?(0,x.jsx)("div",{style:{display:"flex"},children:(0,x.jsx)("div",{style:{margin:4,flex:1,width:"100%"},children:(0,x.jsxs)(d.A,{fluid:!0,children:[(0,x.jsx)(l.A,{className:"mb-3",children:(0,x.jsx)(c.A,{children:(0,x.jsx)("h3",{children:e("maker_dashboard")})})}),(0,x.jsxs)(l.A,{children:[(0,x.jsx)(c.A,{md:4,children:(0,x.jsx)(t.A,{className:"card-box card-primary mb-3",children:(0,x.jsxs)(t.A.Body,{children:[(0,x.jsx)(t.A.Title,{children:e("domain")}),(0,x.jsx)("h3",{children:s.domain||"N/A"})]})})}),(0,x.jsx)(c.A,{md:4,children:(0,x.jsx)(t.A,{className:"card-box card-success mb-3",children:(0,x.jsxs)(t.A.Body,{children:[(0,x.jsx)(t.A.Title,{children:e("support_email")}),(0,x.jsx)("h3",{children:s.support_email||"N/A"})]})})}),(0,x.jsx)(c.A,{md:4,children:(0,x.jsx)(t.A,{className:"card-box card-info mb-3",children:(0,x.jsxs)(t.A.Body,{children:[(0,x.jsx)(t.A.Title,{children:e("sms_signature")}),(0,x.jsx)("h3",{children:s.sms_signature||"N/A"})]})})})]}),(0,x.jsxs)(l.A,{className:"mt-4",children:[(0,x.jsx)(c.A,{md:6,className:"text-center",children:(0,x.jsx)(t.A,{className:"card-box card-primary",children:(0,x.jsxs)(t.A.Body,{children:[(0,x.jsx)("h4",{children:e("product_management")}),(0,x.jsx)("p",{children:e("manage_your_products")}),(0,x.jsx)(n.A,{as:i.N_,to:"/maker/products",children:e("enter_product_list")})]})})}),(0,x.jsx)(c.A,{md:6,className:"text-center",children:(0,x.jsx)(t.A,{className:"card-box card-success",children:(0,x.jsxs)(t.A.Body,{children:[(0,x.jsx)("h4",{children:e("order_management")}),(0,x.jsx)("p",{children:e("all_orders")}),(0,x.jsx)(n.A,{as:i.N_,to:"/maker/orders",children:e("enter_order_list")})]})})})]})]})})}):(0,x.jsx)("div",{className:"alert alert-warning",children:e("not_maker")})}},1072:(e,s,r)=>{r.d(s,{A:()=>o});var a=r(8139),d=r.n(a),l=r(5043),c=r(7852),t=r(579);const n=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:l="div",...n}=e;const o=(0,c.oU)(r,"row"),i=(0,c.gy)(),m=(0,c.Jm)(),x=`${o}-cols`,f=[];return i.forEach(e=>{const s=n[e];let r;delete n[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const a=e!==m?`-${e}`:"";null!=r&&f.push(`${x}${a}-${r}`)}),(0,t.jsx)(l,{ref:s,...n,className:d()(a,o,...f)})});n.displayName="Row";const o=n},8602:(e,s,r)=>{r.d(s,{A:()=>o});var a=r(8139),d=r.n(a),l=r(5043),c=r(7852),t=r(579);const n=l.forwardRef((e,s)=>{const[{className:r,...a},{as:l="div",bsPrefix:n,spans:o}]=function(e){let{as:s,bsPrefix:r,className:a,...l}=e;r=(0,c.oU)(r,"col");const t=(0,c.gy)(),n=(0,c.Jm)(),o=[],i=[];return t.forEach(e=>{const s=l[e];let a,d,c;delete l[e],"object"===typeof s&&null!=s?({span:a,offset:d,order:c}=s):a=s;const t=e!==n?`-${e}`:"";a&&o.push(!0===a?`${r}${t}`:`${r}${t}-${a}`),null!=c&&i.push(`order${t}-${c}`),null!=d&&i.push(`offset${t}-${d}`)}),[{...l,className:d()(a,...o,...i)},{as:s,bsPrefix:r,spans:o}]}(e);return(0,t.jsx)(l,{...a,ref:s,className:d()(r,!o.length&&n)})});n.displayName="Col";const o=n},8628:(e,s,r)=>{r.d(s,{A:()=>U});var a=r(8139),d=r.n(a),l=r(5043),c=r(7852),t=r(579);const n=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="div",...n}=e;return a=(0,c.oU)(a,"card-body"),(0,t.jsx)(l,{ref:s,className:d()(r,a),...n})});n.displayName="CardBody";const o=n,i=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="div",...n}=e;return a=(0,c.oU)(a,"card-footer"),(0,t.jsx)(l,{ref:s,className:d()(r,a),...n})});i.displayName="CardFooter";const m=i;var x=r(1778);const f=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:n="div",...o}=e;const i=(0,c.oU)(r,"card-header"),m=(0,l.useMemo)(()=>({cardHeaderBsPrefix:i}),[i]);return(0,t.jsx)(x.A.Provider,{value:m,children:(0,t.jsx)(n,{ref:s,...o,className:d()(a,i)})})});f.displayName="CardHeader";const h=f,u=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,variant:l,as:n="img",...o}=e;const i=(0,c.oU)(r,"card-img");return(0,t.jsx)(n,{ref:s,className:d()(l?`${i}-${l}`:i,a),...o})});u.displayName="CardImg";const j=u,N=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="div",...n}=e;return a=(0,c.oU)(a,"card-img-overlay"),(0,t.jsx)(l,{ref:s,className:d()(r,a),...n})});N.displayName="CardImgOverlay";const b=N,p=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="a",...n}=e;return a=(0,c.oU)(a,"card-link"),(0,t.jsx)(l,{ref:s,className:d()(r,a),...n})});p.displayName="CardLink";const y=p;var A=r(4488);const g=(0,A.A)("h6"),v=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l=g,...n}=e;return a=(0,c.oU)(a,"card-subtitle"),(0,t.jsx)(l,{ref:s,className:d()(r,a),...n})});v.displayName="CardSubtitle";const _=v,w=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="p",...n}=e;return a=(0,c.oU)(a,"card-text"),(0,t.jsx)(l,{ref:s,className:d()(r,a),...n})});w.displayName="CardText";const $=w,P=(0,A.A)("h5"),k=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l=P,...n}=e;return a=(0,c.oU)(a,"card-title"),(0,t.jsx)(l,{ref:s,className:d()(r,a),...n})});k.displayName="CardTitle";const C=k,R=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,bg:l,text:n,border:i,body:m=!1,children:x,as:f="div",...h}=e;const u=(0,c.oU)(r,"card");return(0,t.jsx)(f,{ref:s,...h,className:d()(a,u,l&&`bg-${l}`,n&&`text-${n}`,i&&`border-${i}`),children:m?(0,t.jsx)(o,{children:x}):x})});R.displayName="Card";const U=Object.assign(R,{Img:j,Title:C,Subtitle:_,Body:o,Link:y,Text:$,Header:h,Footer:m,ImgOverlay:b})}}]);
//# sourceMappingURL=668.5c9ca946.chunk.js.map