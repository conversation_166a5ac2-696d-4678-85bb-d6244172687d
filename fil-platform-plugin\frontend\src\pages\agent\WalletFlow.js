import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const WalletFlow = () => {
    const { t } = useTranslation();
    const [transactions, setTransactions] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchWalletFlow = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            try {
                // First, get the agent profile to verify the user is an agent
                const { data: agentProfile, error: agentError } = await supabase
                    .from('agent_profiles')
                    .select('user_id, maker_id')
                    .eq('user_id', user.id)
                    .single();

                if (agentError) {
                    console.error('Error fetching agent profile:', agentError);
                    setLoading(false);
                    return;
                }

                // Get all customers under this agent
                const { data: customerProfiles, error: customerError } = await supabase
                    .from('customer_profiles')
                    .select('user_id')
                    .eq('agent_id', user.id);

                if (customerError) {
                    console.error('Error fetching customer profiles:', customerError);
                    setLoading(false);
                    return;
                }

                const customerUserIds = customerProfiles.map(cp => cp.user_id).filter(Boolean);

                if (customerUserIds.length === 0) {
                    setTransactions([]);
                    setLoading(false);
                    return;
                }

                // Fetch transactions for these customers with user information
                const { data, error } = await supabase
                    .from('transactions')
                    .select(`
                        id,
                        tx_date,
                        sender_user_id,
                        receiver_user_id,
                        amount_net,
                        tx_type,
                        filecoin_msg_id,
                        agent_id,
                        created_at,
                        sender:users!sender_user_id (
                            email
                        ),
                        receiver:users!receiver_user_id (
                            email
                        )
                    `)
                    .or(`sender_user_id.in.(${customerUserIds.join(',')}),receiver_user_id.in.(${customerUserIds.join(',')})`)
                    .order('tx_date', { ascending: false })
                    .limit(100); // Limit to prevent too many results

                if (error) {
                    console.error('Error fetching wallet flow:', error);
                } else {
                    // Process transactions to add balance information and determine user context
                    const processedTransactions = data.map(transaction => {
                        const isCustomerSender = customerUserIds.includes(transaction.sender_user_id);
                        const isCustomerReceiver = customerUserIds.includes(transaction.receiver_user_id);
                        
                        let customerEmail = '';
                        let amount = transaction.amount_net;
                        let source = transaction.tx_type || '-';
                        
                        if (isCustomerSender && isCustomerReceiver) {
                            // Both sender and receiver are customers under this agent
                            customerEmail = `${transaction.sender?.email} → ${transaction.receiver?.email}`;
                        } else if (isCustomerSender) {
                            // Customer is sender (outgoing transaction)
                            customerEmail = transaction.sender?.email || '-';
                            amount = -Math.abs(amount); // Make it negative for outgoing
                        } else if (isCustomerReceiver) {
                            // Customer is receiver (incoming transaction)
                            customerEmail = transaction.receiver?.email || '-';
                            amount = Math.abs(amount); // Make it positive for incoming
                        }

                        return {
                            ...transaction,
                            customer_email: customerEmail,
                            display_amount: amount,
                            fil_source: source,
                            balance_before: 0, // TODO: Calculate from user_assets history
                            balance_after: 0   // TODO: Calculate from user_assets history
                        };
                    });

                    setTransactions(processedTransactions);
                }
            } catch (error) {
                console.error('Error in fetchWalletFlow:', error);
            }
            setLoading(false);
        };

        fetchWalletFlow();
    }, []);

    const formatAmount = (amount) => {
        if (amount === null || amount === undefined) return '0.000000';
        const num = parseFloat(amount);
        return num >= 0 ? `+${num.toFixed(6)}` : num.toFixed(6);
    };

    const getAmountColor = (amount) => {
        if (amount === null || amount === undefined) return 'text-muted';
        return parseFloat(amount) >= 0 ? 'text-success' : 'text-danger';
    };

    if (loading) {
        return <div>{t('loading_wallet_flow')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('wallet_flow')}</h2>
            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('transaction_amount')}</th>
                                        <th>{t('fil_source')}</th>
                                        <th>{t('balance_before')}</th>
                                        <th>{t('balance_after')}</th>
                                        <th>{t('username')}</th>
                                        <th>{t('tx_date')}</th>
                                        <th>{t('tx_type')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {transactions.length === 0 ? (
                                        <tr>
                                            <td colSpan="7" className="text-center">{t('no_wallet_flow_available')}</td>
                                        </tr>
                                    ) : (
                                        transactions.map((transaction) => (
                                            <tr key={transaction.id}>
                                                <td className={getAmountColor(transaction.display_amount)}>
                                                    <strong>{formatAmount(transaction.display_amount)}</strong>
                                                </td>
                                                <td>{transaction.fil_source}</td>
                                                <td>{transaction.balance_before.toFixed(6)}</td>
                                                <td>{transaction.balance_after.toFixed(6)}</td>
                                                <td>{transaction.customer_email}</td>
                                                <td>{new Date(transaction.tx_date).toLocaleString()}</td>
                                                <td>
                                                    <Badge bg="info">
                                                        {transaction.tx_type || '-'}
                                                    </Badge>
                                                </td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default WalletFlow;
