import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge, Button, Modal, Form, Alert } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const MakerMiners = () => {
    const { t } = useTranslation();
    const [miners, setMiners] = useState([]);
    const [loading, setLoading] = useState(true);

    // Edit modal states
    const [showEditModal, setShowEditModal] = useState(false);
    const [editingMiner, setEditingMiner] = useState(null);
    const [editFormData, setEditFormData] = useState({
        category: '',
        filecoin_miner_id: '',
        sector_size: '',
        effective_until: ''
    });

    // Delete modal states
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [deletingMiner, setDeletingMiner] = useState(null);

    // Alert states
    const [alert, setAlert] = useState({ show: false, type: '', message: '' });

    useEffect(() => {
        const fetchMiners = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // Fetch miners associated with products from this maker
            const { data, error } = await supabase
                .from('miners')
                .select(`
                    id,
                    category,
                    filecoin_miner_id,
                    sector_size,
                    effective_until,
                    created_at,
                    updated_at,
                    facilities (
                        name
                    )
                `)
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Error fetching miners:', error);
            } else {
                setMiners(data);
            }
            setLoading(false);
        };

        fetchMiners();
    }, []);

    // Handle edit button click
    const handleEditClick = (miner) => {
        setEditingMiner(miner);
        setEditFormData({
            category: miner.category || '',
            filecoin_miner_id: miner.filecoin_miner_id || '',
            sector_size: miner.sector_size || '',
            effective_until: miner.effective_until || ''
        });
        setShowEditModal(true);
    };

    // Handle delete button click
    const handleDeleteClick = (miner) => {
        setDeletingMiner(miner);
        setShowDeleteModal(true);
    };

    // Handle edit form submission
    const handleEditSubmit = async (e) => {
        e.preventDefault();
        const supabase = getSupabase();
        if (!supabase || !editingMiner) return;

        try {
            const { error } = await supabase
                .from('miners')
                .update({
                    category: editFormData.category,
                    filecoin_miner_id: editFormData.filecoin_miner_id,
                    sector_size: editFormData.sector_size,
                    effective_until: editFormData.effective_until,
                    updated_at: new Date().toISOString()
                })
                .eq('id', editingMiner.id);

            if (error) throw error;

            // Update local state
            setMiners(miners.map(miner =>
                miner.id === editingMiner.id
                    ? { ...miner, ...editFormData, updated_at: new Date().toISOString() }
                    : miner
            ));

            setAlert({ show: true, type: 'success', message: t('item_updated_successfully') });
            setShowEditModal(false);
            setEditingMiner(null);
        } catch (error) {
            console.error('Error updating miner:', error);
            setAlert({ show: true, type: 'danger', message: t('failed_to_update_item') + ': ' + error.message });
        }
    };

    // Handle delete confirmation
    const handleDeleteConfirm = async () => {
        const supabase = getSupabase();
        if (!supabase || !deletingMiner) return;

        try {
            const { error } = await supabase
                .from('miners')
                .delete()
                .eq('id', deletingMiner.id);

            if (error) throw error;

            // Update local state
            setMiners(miners.filter(miner => miner.id !== deletingMiner.id));

            setAlert({ show: true, type: 'success', message: t('item_deleted_successfully') });
            setShowDeleteModal(false);
            setDeletingMiner(null);
        } catch (error) {
            console.error('Error deleting miner:', error);
            setAlert({ show: true, type: 'danger', message: t('failed_to_delete_item') + ': ' + error.message });
        }
    };

    // Handle form input changes
    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setEditFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    // Close alert
    const closeAlert = () => {
        setAlert({ show: false, type: '', message: '' });
    };

    if (loading) {
        return <div>{t('loading_miners')}</div>;
    }

    return (
        <Container>
            {alert.show && (
                <Alert variant={alert.type} dismissible onClose={closeAlert} className="mb-4">
                    {alert.message}
                </Alert>
            )}
            <h2 className="mb-4">{t('all_miners')}</h2>
                <Row>
                    <Col>
                        <Card>
                            <Card.Body>
                                <Table striped bordered hover responsive>
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>{t('category')}</th>
                                            <th>{t('facility')}</th>
                                            <th>{t('miner_id')}</th>
                                            <th>{t('effective_until')}</th>
                                            <th>{t('created_at')}</th>
                                            <th>{t('updated_at')}</th>
                                            <th>{t('actions')}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {miners.length === 0 ? (
                                            <tr>
                                                <td colSpan="9" className="text-center">{t('no_miners_available')}</td>
                                            </tr>
                                        ) : (
                                            miners.map(miner => (
                                                <tr key={miner.id}>
                                                    <td>{miner.id.substring(0, 8)}...</td>
                                                    <td>{miner.category}</td>
                                                    <td>{miner.facilities?.name || '-'}</td>
                                                    <td>{miner.filecoin_miner_id}</td>
                                                    <td>{miner.sector_size}</td>
                                                    <td>{miner.effective_until}</td>
                                                    <td>{new Date(miner.created_at).toLocaleString()}</td>
                                                    <td>{new Date(miner.updated_at).toLocaleString()}</td>
                                                    <td>
                                                        <Button
                                                            variant="info"
                                                            size="sm"
                                                            className="me-2"
                                                            onClick={() => handleEditClick(miner)}
                                                        >
                                                            {t('edit')}
                                                        </Button>
                                                        <Button
                                                            variant="danger"
                                                            size="sm"
                                                            onClick={() => handleDeleteClick(miner)}
                                                        >
                                                            {t('delete')}
                                                        </Button>
                                                    </td>
                                                </tr>
                                            ))
                                        )}
                                    </tbody>
                                </Table>
                            </Card.Body>
                        </Card>
                    </Col>
                </Row>

            {/* Edit Modal */}
            <Modal show={showEditModal} onHide={() => setShowEditModal(false)} size="lg">
                <Modal.Header closeButton>
                    <Modal.Title>{t('edit_miner')}</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Form onSubmit={handleEditSubmit}>
                        <Form.Group className="mb-3">
                            <Form.Label>{t('category')}</Form.Label>
                            <Form.Control
                                type="text"
                                name="category"
                                value={editFormData.category}
                                onChange={handleInputChange}
                                required
                            />
                        </Form.Group>
                        <Form.Group className="mb-3">
                            <Form.Label>{t('miner_id')}</Form.Label>
                            <Form.Control
                                type="text"
                                name="filecoin_miner_id"
                                value={editFormData.filecoin_miner_id}
                                onChange={handleInputChange}
                                required
                            />
                        </Form.Group>
                        <Form.Group className="mb-3">
                            <Form.Label>{t('sector_size')}</Form.Label>
                            <Form.Control
                                type="text"
                                name="sector_size"
                                value={editFormData.sector_size}
                                onChange={handleInputChange}
                                required
                            />
                        </Form.Group>
                        <Form.Group className="mb-3">
                            <Form.Label>{t('effective_until')}</Form.Label>
                            <Form.Control
                                type="date"
                                name="effective_until"
                                value={editFormData.effective_until}
                                onChange={handleInputChange}
                                required
                            />
                        </Form.Group>
                        <div className="d-flex justify-content-end">
                            <Button variant="secondary" className="me-2" onClick={() => setShowEditModal(false)}>
                                {t('cancel')}
                            </Button>
                            <Button variant="primary" type="submit">
                                {t('save_changes')}
                            </Button>
                        </div>
                    </Form>
                </Modal.Body>
            </Modal>

            {/* Delete Confirmation Modal */}
            <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
                <Modal.Header closeButton>
                    <Modal.Title>{t('confirm_delete')}</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <p>{t('delete_confirmation')}</p>
                    {deletingMiner && (
                        <p><strong>{t('miner_id')}: {deletingMiner.filecoin_miner_id}</strong></p>
                    )}
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
                        {t('cancel')}
                    </Button>
                    <Button variant="danger" onClick={handleDeleteConfirm}>
                        {t('confirm')}
                    </Button>
                </Modal.Footer>
            </Modal>
        </Container>
    );
};

export default MakerMiners;
