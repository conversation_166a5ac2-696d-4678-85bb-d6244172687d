#!/usr/bin/env node

/**
 * Filfox.info Data Scraper
 * 
 * This script scrapes Block Height and 24h Average Mining Reward from filfox.info
 * and stores the data in the network_stats table.
 * 
 * Runs daily at 2:00 AM Tokyo time (JST)
 */

const { createClient } = require('@supabase/supabase-js');
const puppeteer = require('puppeteer');
require('dotenv').config();

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY; // Use service key for server-side operations
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Logging function
function log(message) {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] ${message}`);
}

// Error logging function
function logError(message, error) {
    const timestamp = new Date().toISOString();
    console.error(`[${timestamp}] ERROR: ${message}`, error);
}

/**
 * Scrape data from filfox.info with retry mechanism
 */
async function scrapeFilfoxData(maxRetries = 3) {
    let browser;
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            log(`Starting browser (attempt ${attempt}/${maxRetries})...`);
            browser = await puppeteer.launch({
                headless: true,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--single-process',
                    '--disable-gpu',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            });

            const page = await browser.newPage();

            // Set viewport and user agent
            await page.setViewport({ width: 1920, height: 1080 });
            await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

            log('Navigating to filfox.info...');
            await page.goto('https://filfox.info/en', {
                waitUntil: 'networkidle2',
                timeout: 60000
            });

            // Wait for the page to load completely
            await page.waitForTimeout(5000);

            log('Extracting data from page...');

            // Extract Block Height and 24h Average Mining Reward
            const data = await page.evaluate(() => {
                let blockHeight = null;
                let avgMiningReward = null;

                // More specific selectors for filfox.info
                // Try to find block height in various ways
                const heightSelectors = [
                    '[data-testid*="height"]',
                    '[class*="height"]',
                    '[class*="block"]',
                    '.overview-item',
                    '.stat-item',
                    '.metric'
                ];

                for (let selector of heightSelectors) {
                    const elements = document.querySelectorAll(selector);
                    for (let element of elements) {
                        const text = element.textContent || '';
                        const numbers = text.match(/[\d,]+/g);
                        if (numbers) {
                            for (let num of numbers) {
                                const height = parseInt(num.replace(/,/g, ''));
                                if (height > 2000000 && height < 10000000) { // Reasonable block height range
                                    blockHeight = height;
                                    break;
                                }
                            }
                        }
                        if (blockHeight) break;
                    }
                    if (blockHeight) break;
                }

                // Try to find mining reward
                const rewardSelectors = [
                    '[data-testid*="reward"]',
                    '[class*="reward"]',
                    '[class*="mining"]',
                    '.overview-item',
                    '.stat-item',
                    '.metric'
                ];

                for (let selector of rewardSelectors) {
                    const elements = document.querySelectorAll(selector);
                    for (let element of elements) {
                        const text = element.textContent || '';
                        // Look for FIL amounts
                        const filMatches = text.match(/(\d+\.?\d*)\s*FIL/gi);
                        if (filMatches) {
                            for (let match of filMatches) {
                                const amount = parseFloat(match.replace(/[^\d.]/g, ''));
                                if (amount > 0.001 && amount < 100) { // Reasonable reward range
                                    avgMiningReward = amount;
                                    break;
                                }
                            }
                        }
                        if (avgMiningReward) break;
                    }
                    if (avgMiningReward) break;
                }

                // Fallback: search all text content
                if (!blockHeight || !avgMiningReward) {
                    const allText = document.body.textContent || '';

                    if (!blockHeight) {
                        const heightMatches = allText.match(/height[:\s]*(\d{7,8})/gi);
                        if (heightMatches) {
                            const height = parseInt(heightMatches[0].replace(/\D/g, ''));
                            if (height > 2000000) blockHeight = height;
                        }
                    }

                    if (!avgMiningReward) {
                        const rewardMatches = allText.match(/(\d+\.?\d*)\s*FIL/gi);
                        if (rewardMatches) {
                            for (let match of rewardMatches) {
                                const amount = parseFloat(match.replace(/[^\d.]/g, ''));
                                if (amount > 0.001 && amount < 100) {
                                    avgMiningReward = amount;
                                    break;
                                }
                            }
                        }
                    }
                }

                return {
                    blockHeight,
                    avgMiningReward,
                    pageTitle: document.title,
                    url: window.location.href,
                    pageContent: document.body.textContent.substring(0, 1000) // First 1000 chars for debugging
                };
            });

            log(`Scraped data: Block Height: ${data.blockHeight}, Avg Mining Reward: ${data.avgMiningReward}`);

            if (!data.blockHeight && !data.avgMiningReward) {
                throw new Error('Failed to extract any data from filfox.info. Page content: ' + data.pageContent);
            }

            if (!data.blockHeight) {
                log('Warning: Block height not found, using fallback value');
                data.blockHeight = 0; // or get from another source
            }

            if (!data.avgMiningReward) {
                log('Warning: Average mining reward not found, using fallback value');
                data.avgMiningReward = 0; // or get from another source
            }

            return data;

        } catch (error) {
            lastError = error;
            logError(`Scraping attempt ${attempt} failed`, error);

            if (browser) {
                await browser.close();
                browser = null;
            }

            if (attempt < maxRetries) {
                const delay = attempt * 5000; // Exponential backoff
                log(`Waiting ${delay}ms before retry...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        } finally {
            if (browser) {
                await browser.close();
                log('Browser closed');
            }
        }
    }

    throw new Error(`Failed to scrape data after ${maxRetries} attempts. Last error: ${lastError.message}`);
}

/**
 * Store data in Supabase database
 */
async function storeNetworkStats(blockHeight, avgMiningReward) {
    try {
        const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
        
        log(`Storing network stats for date: ${today}`);
        
        const { data, error } = await supabase
            .from('network_stats')
            .upsert({
                stat_date: today,
                blockchain_height: blockHeight,
                fil_per_tib: avgMiningReward
            }, {
                onConflict: 'stat_date'
            });

        if (error) {
            throw error;
        }

        log('Network stats stored successfully');
        return data;

    } catch (error) {
        logError('Error storing network stats', error);
        throw error;
    }
}

/**
 * Main execution function
 */
async function main() {
    try {
        log('=== Filfox Scraper Started ===');
        
        // Check environment variables
        if (!supabaseUrl || !supabaseServiceKey) {
            throw new Error('Missing required environment variables: SUPABASE_URL or SUPABASE_SERVICE_KEY');
        }

        // Scrape data from filfox.info
        const scrapedData = await scrapeFilfoxData();
        
        // Store data in database
        await storeNetworkStats(scrapedData.blockHeight, scrapedData.avgMiningReward);
        
        log('=== Filfox Scraper Completed Successfully ===');
        process.exit(0);

    } catch (error) {
        logError('Scraper failed', error);
        process.exit(1);
    }
}

// Run the scraper
if (require.main === module) {
    main();
}

module.exports = { scrapeFilfoxData, storeNetworkStats };
