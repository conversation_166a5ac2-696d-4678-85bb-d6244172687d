"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[132],{1072:(e,r,s)=>{s.d(r,{A:()=>l});var a=s(8139),t=s.n(a),d=s(5043),n=s(7852),i=s(579);const c=d.forwardRef((e,r)=>{let{bsPrefix:s,className:a,as:d="div",...c}=e;const l=(0,n.oU)(s,"row"),o=(0,n.gy)(),f=(0,n.Jm)(),x=`${l}-cols`,h=[];return o.forEach(e=>{const r=c[e];let s;delete c[e],null!=r&&"object"===typeof r?({cols:s}=r):s=r;const a=e!==f?`-${e}`:"";null!=s&&h.push(`${x}${a}-${s}`)}),(0,i.jsx)(d,{ref:r,...c,className:t()(a,l,...h)})});c.displayName="Row";const l=c},4063:(e,r,s)=>{s.d(r,{A:()=>l});var a=s(8139),t=s.n(a),d=s(5043),n=s(7852),i=s(579);const c=d.forwardRef((e,r)=>{let{bsPrefix:s,bg:a="primary",pill:d=!1,text:c,className:l,as:o="span",...f}=e;const x=(0,n.oU)(s,"badge");return(0,i.jsx)(o,{ref:r,...f,className:t()(l,x,d&&"rounded-pill",c&&`text-${c}`,a&&`bg-${a}`)})});c.displayName="Badge";const l=c},4196:(e,r,s)=>{s.d(r,{A:()=>l});var a=s(8139),t=s.n(a),d=s(5043),n=s(7852),i=s(579);const c=d.forwardRef((e,r)=>{let{bsPrefix:s,className:a,striped:d,bordered:c,borderless:l,hover:o,size:f,variant:x,responsive:h,...u}=e;const p=(0,n.oU)(s,"table"),m=t()(a,p,x&&`${p}-${x}`,f&&`${p}-${f}`,d&&`${p}-${"string"===typeof d?`striped-${d}`:"striped"}`,c&&`${p}-bordered`,l&&`${p}-borderless`,o&&`${p}-hover`),j=(0,i.jsx)("table",{...u,className:m,ref:r});if(h){let e=`${p}-responsive`;return"string"===typeof h&&(e=`${e}-${h}`),(0,i.jsx)("div",{className:e,children:j})}return j});c.displayName="Table";const l=c},7132:(e,r,s)=>{s.r(r),s.d(r,{default:()=>h});var a=s(5043),t=s(4063),d=s(3519),n=s(1072),i=s(8602),c=s(8628),l=s(4196),o=s(4312),f=s(4117),x=s(579);const h=()=>{const{t:e}=(0,f.Bd)(),[r,s]=(0,a.useState)([]),[h,u]=(0,a.useState)(!0);(0,a.useEffect)(()=>{(async()=>{const e=(0,o.b)();if(!e)return;u(!0);const{data:{user:r}}=await e.auth.getUser();if(!r)return void u(!1);const{data:a,error:t}=await e.from("agent_profiles").select("maker_id").eq("user_id",r.id).single();if(t)return console.error("Error fetching agent profile:",t),void u(!1);if(!a||!a.maker_id)return console.error("Agent profile not found or no maker_id"),void u(!1);const{data:d,error:n}=await e.from("capacity_requests").select("\n                    id,\n                    product_category,\n                    added_capacity,\n                    capacity_before,\n                    capacity_after,\n                    status,\n                    description,\n                    review_reply,\n                    requested_at,\n                    reviewed_at,\n                    maker_profiles (\n                        domain,\n                        users (\n                            email\n                        )\n                    ),\n                    users (\n                        email,\n                        role\n                    )\n                ").eq("maker_id",a.maker_id).order("requested_at",{ascending:!1});n?console.error("Error fetching capacity requests:",n):s(d),u(!1)})()},[]);const p=r=>{switch(r){case"approved":return(0,x.jsx)(t.A,{bg:"success",children:e("approved")});case"pending":return(0,x.jsx)(t.A,{bg:"warning",children:e("pending_review")});case"rejected":return(0,x.jsx)(t.A,{bg:"danger",children:e("rejected")});case"under_review":return(0,x.jsx)(t.A,{bg:"info",children:e("under_review")});default:return(0,x.jsx)(t.A,{bg:"secondary",children:r||"-"})}};return h?(0,x.jsx)("div",{children:e("loading_capacity_requests")}):(0,x.jsxs)(d.A,{children:[(0,x.jsx)("h2",{className:"mb-4",children:e("capacity_expansion_request")}),(0,x.jsx)(n.A,{children:(0,x.jsx)(i.A,{children:(0,x.jsx)(c.A,{children:(0,x.jsx)(c.A.Body,{children:(0,x.jsxs)(l.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,x.jsx)("thead",{children:(0,x.jsxs)("tr",{children:[(0,x.jsx)("th",{children:e("request_id")}),(0,x.jsx)("th",{children:e("maker")}),(0,x.jsx)("th",{children:e("requested_by")}),(0,x.jsx)("th",{children:e("product_category")}),(0,x.jsx)("th",{children:e("added_capacity")}),(0,x.jsx)("th",{children:e("capacity_before")}),(0,x.jsx)("th",{children:e("capacity_after")}),(0,x.jsx)("th",{children:e("status")}),(0,x.jsx)("th",{children:e("description")}),(0,x.jsx)("th",{children:e("review_reply")}),(0,x.jsx)("th",{children:e("requested_at")}),(0,x.jsx)("th",{children:e("reviewed_at")})]})}),(0,x.jsx)("tbody",{children:0===r.length?(0,x.jsx)("tr",{children:(0,x.jsx)("td",{colSpan:"12",className:"text-center",children:e("no_capacity_requests_available")})}):r.map(e=>{var r,s,a,d,n,i,c;return(0,x.jsxs)("tr",{children:[(0,x.jsx)("td",{children:e.id}),(0,x.jsx)("td",{children:(0,x.jsxs)("div",{children:[(0,x.jsx)("div",{children:(null===(r=e.maker_profiles)||void 0===r?void 0:r.domain)||"-"}),(0,x.jsx)("small",{className:"text-muted",children:(null===(s=e.maker_profiles)||void 0===s||null===(a=s.users)||void 0===a?void 0:a.email)||"-"})]})}),(0,x.jsx)("td",{children:(null===(d=e.users)||void 0===d?void 0:d.email)||"-"}),(0,x.jsx)("td",{children:(0,x.jsx)(t.A,{bg:"spot"===e.product_category?"primary":"secondary",children:e.product_category||"-"})}),(0,x.jsx)("td",{children:(null===(n=e.added_capacity)||void 0===n?void 0:n.toFixed(2))||"0.00"}),(0,x.jsx)("td",{children:(null===(i=e.capacity_before)||void 0===i?void 0:i.toFixed(2))||"0.00"}),(0,x.jsx)("td",{children:(null===(c=e.capacity_after)||void 0===c?void 0:c.toFixed(2))||"0.00"}),(0,x.jsx)("td",{children:p(e.status)}),(0,x.jsx)("td",{children:(0,x.jsx)("div",{style:{maxWidth:"200px",wordWrap:"break-word"},children:e.description||"-"})}),(0,x.jsx)("td",{children:(0,x.jsx)("div",{style:{maxWidth:"200px",wordWrap:"break-word"},children:e.review_reply||"-"})}),(0,x.jsx)("td",{children:new Date(e.requested_at).toLocaleString()}),(0,x.jsx)("td",{children:e.reviewed_at?new Date(e.reviewed_at).toLocaleString():"-"})]},e.id)})})]})})})})})]})}},8602:(e,r,s)=>{s.d(r,{A:()=>l});var a=s(8139),t=s.n(a),d=s(5043),n=s(7852),i=s(579);const c=d.forwardRef((e,r)=>{const[{className:s,...a},{as:d="div",bsPrefix:c,spans:l}]=function(e){let{as:r,bsPrefix:s,className:a,...d}=e;s=(0,n.oU)(s,"col");const i=(0,n.gy)(),c=(0,n.Jm)(),l=[],o=[];return i.forEach(e=>{const r=d[e];let a,t,n;delete d[e],"object"===typeof r&&null!=r?({span:a,offset:t,order:n}=r):a=r;const i=e!==c?`-${e}`:"";a&&l.push(!0===a?`${s}${i}`:`${s}${i}-${a}`),null!=n&&o.push(`order${i}-${n}`),null!=t&&o.push(`offset${i}-${t}`)}),[{...d,className:t()(a,...l,...o)},{as:r,bsPrefix:s,spans:l}]}(e);return(0,i.jsx)(d,{...a,ref:r,className:t()(s,!l.length&&c)})});c.displayName="Col";const l=c},8628:(e,r,s)=>{s.d(r,{A:()=>q});var a=s(8139),t=s.n(a),d=s(5043),n=s(7852),i=s(579);const c=d.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:d="div",...c}=e;return a=(0,n.oU)(a,"card-body"),(0,i.jsx)(d,{ref:r,className:t()(s,a),...c})});c.displayName="CardBody";const l=c,o=d.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:d="div",...c}=e;return a=(0,n.oU)(a,"card-footer"),(0,i.jsx)(d,{ref:r,className:t()(s,a),...c})});o.displayName="CardFooter";const f=o;var x=s(1778);const h=d.forwardRef((e,r)=>{let{bsPrefix:s,className:a,as:c="div",...l}=e;const o=(0,n.oU)(s,"card-header"),f=(0,d.useMemo)(()=>({cardHeaderBsPrefix:o}),[o]);return(0,i.jsx)(x.A.Provider,{value:f,children:(0,i.jsx)(c,{ref:r,...l,className:t()(a,o)})})});h.displayName="CardHeader";const u=h,p=d.forwardRef((e,r)=>{let{bsPrefix:s,className:a,variant:d,as:c="img",...l}=e;const o=(0,n.oU)(s,"card-img");return(0,i.jsx)(c,{ref:r,className:t()(d?`${o}-${d}`:o,a),...l})});p.displayName="CardImg";const m=p,j=d.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:d="div",...c}=e;return a=(0,n.oU)(a,"card-img-overlay"),(0,i.jsx)(d,{ref:r,className:t()(s,a),...c})});j.displayName="CardImgOverlay";const v=j,y=d.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:d="a",...c}=e;return a=(0,n.oU)(a,"card-link"),(0,i.jsx)(d,{ref:r,className:t()(s,a),...c})});y.displayName="CardLink";const b=y;var N=s(4488);const _=(0,N.A)("h6"),g=d.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:d=_,...c}=e;return a=(0,n.oU)(a,"card-subtitle"),(0,i.jsx)(d,{ref:r,className:t()(s,a),...c})});g.displayName="CardSubtitle";const w=g,$=d.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:d="p",...c}=e;return a=(0,n.oU)(a,"card-text"),(0,i.jsx)(d,{ref:r,className:t()(s,a),...c})});$.displayName="CardText";const A=$,k=(0,N.A)("h5"),P=d.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:d=k,...c}=e;return a=(0,n.oU)(a,"card-title"),(0,i.jsx)(d,{ref:r,className:t()(s,a),...c})});P.displayName="CardTitle";const R=P,U=d.forwardRef((e,r)=>{let{bsPrefix:s,className:a,bg:d,text:c,border:o,body:f=!1,children:x,as:h="div",...u}=e;const p=(0,n.oU)(s,"card");return(0,i.jsx)(h,{ref:r,...u,className:t()(a,p,d&&`bg-${d}`,c&&`text-${c}`,o&&`border-${o}`),children:f?(0,i.jsx)(l,{children:x}):x})});U.displayName="Card";const q=Object.assign(U,{Img:m,Title:R,Subtitle:w,Body:l,Link:b,Text:A,Header:u,Footer:f,ImgOverlay:v})}}]);
//# sourceMappingURL=132.8d91b775.chunk.js.map