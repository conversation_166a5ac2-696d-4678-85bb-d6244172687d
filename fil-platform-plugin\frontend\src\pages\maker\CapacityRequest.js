import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const CapacityRequest = () => {
    const { t } = useTranslation();
    const [requests, setRequests] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchRequests = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // Fetch capacity requests with maker and requester information
            const { data, error } = await supabase
                .from('capacity_requests')
                .select(`
                    id,
                    product_category,
                    added_capacity,
                    capacity_before,
                    capacity_after,
                    status,
                    description,
                    review_reply,
                    requested_at,
                    reviewed_at,
                    maker_profiles (
                        domain,
                        users (
                            email
                        )
                    ),
                    users (
                        email,
                        role
                    )
                `)
                .order('requested_at', { ascending: false });

            if (error) {
                console.error('Error fetching capacity requests:', error);
            } else {
                setRequests(data);
            }
            setLoading(false);
        };

        fetchRequests();
    }, []);

    const getStatusBadge = (status) => {
        switch (status) {
            case 'approved':
                return <Badge bg="success">{t('approved')}</Badge>;
            case 'pending':
                return <Badge bg="warning">{t('pending_review')}</Badge>;
            case 'rejected':
                return <Badge bg="danger">{t('rejected')}</Badge>;
            case 'under_review':
                return <Badge bg="info">{t('under_review')}</Badge>;
            default:
                return <Badge bg="secondary">{status || '-'}</Badge>;
        }
    };

    if (loading) {
        return <div>{t('loading_capacity_requests')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('capacity_expansion_request')}</h2>
            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('request_id')}</th>
                                        <th>{t('maker')}</th>
                                        <th>{t('requested_by')}</th>
                                        <th>{t('product_category')}</th>
                                        <th>{t('added_capacity')}</th>
                                        <th>{t('capacity_before')}</th>
                                        <th>{t('capacity_after')}</th>
                                        <th>{t('status')}</th>
                                        <th>{t('description')}</th>
                                        <th>{t('review_reply')}</th>
                                        <th>{t('requested_at')}</th>
                                        <th>{t('reviewed_at')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {requests.length === 0 ? (
                                        <tr>
                                            <td colSpan="12" className="text-center">{t('no_capacity_requests_available')}</td>
                                        </tr>
                                    ) : (
                                        requests.map(request => (
                                            <tr key={request.id}>
                                                <td>{request.id}</td>
                                                <td>
                                                    <div>
                                                        <div>{request.maker_profiles?.domain || '-'}</div>
                                                        <small className="text-muted">
                                                            {request.maker_profiles?.users?.email || '-'}
                                                        </small>
                                                    </div>
                                                </td>
                                                <td>{request.users?.email || '-'}</td>
                                                <td>
                                                    <Badge bg={request.product_category === 'spot' ? 'primary' : 'secondary'}>
                                                        {request.product_category || '-'}
                                                    </Badge>
                                                </td>
                                                <td>{request.added_capacity?.toFixed(2) || '0.00'}</td>
                                                <td>{request.capacity_before?.toFixed(2) || '0.00'}</td>
                                                <td>{request.capacity_after?.toFixed(2) || '0.00'}</td>
                                                <td>{getStatusBadge(request.status)}</td>
                                                <td>
                                                    <div style={{ maxWidth: '200px', wordWrap: 'break-word' }}>
                                                        {request.description || '-'}
                                                    </div>
                                                </td>
                                                <td>
                                                    <div style={{ maxWidth: '200px', wordWrap: 'break-word' }}>
                                                        {request.review_reply || '-'}
                                                    </div>
                                                </td>
                                                <td>{new Date(request.requested_at).toLocaleString()}</td>
                                                <td>{request.reviewed_at ? new Date(request.reviewed_at).toLocaleString() : '-'}</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default CapacityRequest;
