{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Container,Row,Col,Card,Table,Badge}from'react-bootstrap';import{getSupabase}from'../../supabaseClient';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AgentMemberList=()=>{const{t}=useTranslation();const[members,setMembers]=useState([]);const[loading,setLoading]=useState(true);useEffect(()=>{const fetchMembers=async()=>{const supabase=getSupabase();if(!supabase)return;setLoading(true);const{data:{user}}=await supabase.auth.getUser();if(!user){setLoading(false);return;}// Step 1: 查询 customer_profiles\nconst{data:customers,error:profileError}=await supabase.from('customer_profiles').select('user_id, real_name, verify_status').eq('agent_id',user.id).order('created_at',{ascending:false});if(profileError||!customers){console.error('Error fetching customer_profiles:',profileError);setLoading(false);return;}// Step 2: 查询 users 表\nconst userIds=customers.map(c=>c.user_id).filter(Boolean);const{data:userInfoList,error:userError}=await supabase.from('users').select('id, email, created_at, referred_by::uuid').in('id',userIds);if(userError){console.error('Error fetching users:',userError);}// Step 3: 合并结果\nconst usersMap=new Map((userInfoList||[]).map(u=>[u.id,u]));const enrichedMembers=customers.map(c=>({...c,users:usersMap.get(c.user_id)||{}}));setMembers(enrichedMembers);setLoading(false);};fetchMembers();},[]);if(loading){return/*#__PURE__*/_jsx(\"div\",{children:t('loading_members')});}return/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"mb-4\",children:t('my_subordinates')}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Table,{striped:true,bordered:true,hover:true,responsive:true,children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:t('user_id')}),/*#__PURE__*/_jsx(\"th\",{children:t('email')}),/*#__PURE__*/_jsx(\"th\",{children:t('real_name')}),/*#__PURE__*/_jsx(\"th\",{children:t('kyc_status')}),/*#__PURE__*/_jsx(\"th\",{children:t('registration_time')})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:members.length===0?/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:\"5\",className:\"text-center\",children:t('no_subordinates')})}):members.map(member=>{var _member$users,_member$users2;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsxs(\"td\",{children:[member.user_id.substring(0,8),\"...\"]}),/*#__PURE__*/_jsx(\"td\",{children:((_member$users=member.users)===null||_member$users===void 0?void 0:_member$users.email)||'N/A'}),/*#__PURE__*/_jsx(\"td\",{children:member.real_name||'N/A'}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(Badge,{bg:member.verify_status==='approved'?'success':'warning',children:member.verify_status})}),/*#__PURE__*/_jsx(\"td\",{children:new Date((_member$users2=member.users)===null||_member$users2===void 0?void 0:_member$users2.created_at).toLocaleString()})]},member.user_id);})})]})})})})})]});};export default AgentMemberList;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Table", "Badge", "getSupabase", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "AgentMemberList", "t", "members", "setMembers", "loading", "setLoading", "fetchMembers", "supabase", "data", "user", "auth", "getUser", "customers", "error", "profileError", "from", "select", "eq", "id", "order", "ascending", "console", "userIds", "map", "c", "user_id", "filter", "Boolean", "userInfoList", "userError", "in", "usersMap", "Map", "u", "enrichedMembers", "users", "get", "children", "className", "Body", "striped", "bordered", "hover", "responsive", "length", "colSpan", "member", "_member$users", "_member$users2", "substring", "email", "real_name", "bg", "verify_status", "Date", "created_at", "toLocaleString"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/agent/AgentMemberList.js"], "sourcesContent": ["\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst AgentMemberList = () => {\n    const { t } = useTranslation();\n    const [members, setMembers] = useState([]);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        const fetchMembers = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setLoading(false);\n                return;\n            }\n\n            // Step 1: 查询 customer_profiles\n            const { data: customers, error: profileError } = await supabase\n                .from('customer_profiles')\n                .select('user_id, real_name, verify_status')\n                .eq('agent_id', user.id)\n                .order('created_at', { ascending: false });\n\n            if (profileError || !customers) {\n                console.error('Error fetching customer_profiles:', profileError);\n                setLoading(false);\n                return;\n            }\n\n            // Step 2: 查询 users 表\n            const userIds = customers.map(c => c.user_id).filter(Boolean);\n\n            const { data: userInfoList, error: userError } = await supabase\n                .from('users')\n                .select('id, email, created_at, referred_by::uuid')\n                .in('id', userIds);\n\n            if (userError) {\n                console.error('Error fetching users:', userError);\n            }\n\n            // Step 3: 合并结果\n            const usersMap = new Map((userInfoList || []).map(u => [u.id, u]));\n\n            const enrichedMembers = customers.map(c => ({\n                ...c,\n                users: usersMap.get(c.user_id) || {}\n            }));\n\n            setMembers(enrichedMembers);\n            setLoading(false);\n        };\n\n\n        fetchMembers();\n    }, []);\n\n    if (loading) {\n        return <div>{t('loading_members')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('my_subordinates')}</h2>\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('user_id')}</th>\n                                        <th>{t('email')}</th>\n                                        <th>{t('real_name')}</th>\n                                        <th>{t('kyc_status')}</th>\n                                        <th>{t('registration_time')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {members.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"5\" className=\"text-center\">{t('no_subordinates')}</td>\n                                        </tr>\n                                    ) : (\n                                        members.map(member => (\n                                            <tr key={member.user_id}>\n                                                <td>{member.user_id.substring(0, 8)}...</td>\n                                                <td>{member.users?.email || 'N/A'}</td>\n                                                <td>{member.real_name || 'N/A'}</td>\n                                                <td><Badge bg={member.verify_status === 'approved' ? 'success' : 'warning'}>{member.verify_status}</Badge></td>\n                                                <td>{new Date(member.users?.created_at).toLocaleString()}</td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default AgentMemberList;\n"], "mappings": "AACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,KAAK,CAAEC,KAAK,KAAQ,iBAAiB,CACzE,OAASC,WAAW,KAAQ,sBAAsB,CAClD,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC1B,KAAM,CAAEC,CAAE,CAAC,CAAGN,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACO,OAAO,CAAEC,UAAU,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACkB,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CAE5CC,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAmB,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC7B,KAAM,CAAAC,QAAQ,CAAGb,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACa,QAAQ,CAAE,OAEfF,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEG,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CAExD,GAAI,CAACF,IAAI,CAAE,CACPJ,UAAU,CAAC,KAAK,CAAC,CACjB,OACJ,CAEA;AACA,KAAM,CAAEG,IAAI,CAAEI,SAAS,CAAEC,KAAK,CAAEC,YAAa,CAAC,CAAG,KAAM,CAAAP,QAAQ,CAC1DQ,IAAI,CAAC,mBAAmB,CAAC,CACzBC,MAAM,CAAC,mCAAmC,CAAC,CAC3CC,EAAE,CAAC,UAAU,CAAER,IAAI,CAACS,EAAE,CAAC,CACvBC,KAAK,CAAC,YAAY,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAE9C,GAAIN,YAAY,EAAI,CAACF,SAAS,CAAE,CAC5BS,OAAO,CAACR,KAAK,CAAC,mCAAmC,CAAEC,YAAY,CAAC,CAChET,UAAU,CAAC,KAAK,CAAC,CACjB,OACJ,CAEA;AACA,KAAM,CAAAiB,OAAO,CAAGV,SAAS,CAACW,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACC,OAAO,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAE7D,KAAM,CAAEnB,IAAI,CAAEoB,YAAY,CAAEf,KAAK,CAAEgB,SAAU,CAAC,CAAG,KAAM,CAAAtB,QAAQ,CAC1DQ,IAAI,CAAC,OAAO,CAAC,CACbC,MAAM,CAAC,0CAA0C,CAAC,CAClDc,EAAE,CAAC,IAAI,CAAER,OAAO,CAAC,CAEtB,GAAIO,SAAS,CAAE,CACXR,OAAO,CAACR,KAAK,CAAC,uBAAuB,CAAEgB,SAAS,CAAC,CACrD,CAEA;AACA,KAAM,CAAAE,QAAQ,CAAG,GAAI,CAAAC,GAAG,CAAC,CAACJ,YAAY,EAAI,EAAE,EAAEL,GAAG,CAACU,CAAC,EAAI,CAACA,CAAC,CAACf,EAAE,CAAEe,CAAC,CAAC,CAAC,CAAC,CAElE,KAAM,CAAAC,eAAe,CAAGtB,SAAS,CAACW,GAAG,CAACC,CAAC,GAAK,CACxC,GAAGA,CAAC,CACJW,KAAK,CAAEJ,QAAQ,CAACK,GAAG,CAACZ,CAAC,CAACC,OAAO,CAAC,EAAI,CAAC,CACvC,CAAC,CAAC,CAAC,CAEHtB,UAAU,CAAC+B,eAAe,CAAC,CAC3B7B,UAAU,CAAC,KAAK,CAAC,CACrB,CAAC,CAGDC,YAAY,CAAC,CAAC,CAClB,CAAC,CAAE,EAAE,CAAC,CAEN,GAAIF,OAAO,CAAE,CACT,mBAAOP,IAAA,QAAAwC,QAAA,CAAMpC,CAAC,CAAC,iBAAiB,CAAC,CAAM,CAAC,CAC5C,CAEA,mBACIF,KAAA,CAACX,SAAS,EAAAiD,QAAA,eACNxC,IAAA,OAAIyC,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAEpC,CAAC,CAAC,iBAAiB,CAAC,CAAK,CAAC,cAChDJ,IAAA,CAACR,GAAG,EAAAgD,QAAA,cACAxC,IAAA,CAACP,GAAG,EAAA+C,QAAA,cACAxC,IAAA,CAACN,IAAI,EAAA8C,QAAA,cACDxC,IAAA,CAACN,IAAI,CAACgD,IAAI,EAAAF,QAAA,cACNtC,KAAA,CAACP,KAAK,EAACgD,OAAO,MAACC,QAAQ,MAACC,KAAK,MAACC,UAAU,MAAAN,QAAA,eACpCxC,IAAA,UAAAwC,QAAA,cACItC,KAAA,OAAAsC,QAAA,eACIxC,IAAA,OAAAwC,QAAA,CAAKpC,CAAC,CAAC,SAAS,CAAC,CAAK,CAAC,cACvBJ,IAAA,OAAAwC,QAAA,CAAKpC,CAAC,CAAC,OAAO,CAAC,CAAK,CAAC,cACrBJ,IAAA,OAAAwC,QAAA,CAAKpC,CAAC,CAAC,WAAW,CAAC,CAAK,CAAC,cACzBJ,IAAA,OAAAwC,QAAA,CAAKpC,CAAC,CAAC,YAAY,CAAC,CAAK,CAAC,cAC1BJ,IAAA,OAAAwC,QAAA,CAAKpC,CAAC,CAAC,mBAAmB,CAAC,CAAK,CAAC,EACjC,CAAC,CACF,CAAC,cACRJ,IAAA,UAAAwC,QAAA,CACKnC,OAAO,CAAC0C,MAAM,GAAK,CAAC,cACjB/C,IAAA,OAAAwC,QAAA,cACIxC,IAAA,OAAIgD,OAAO,CAAC,GAAG,CAACP,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAEpC,CAAC,CAAC,iBAAiB,CAAC,CAAK,CAAC,CACnE,CAAC,CAELC,OAAO,CAACqB,GAAG,CAACuB,MAAM,OAAAC,aAAA,CAAAC,cAAA,oBACdjD,KAAA,OAAAsC,QAAA,eACItC,KAAA,OAAAsC,QAAA,EAAKS,MAAM,CAACrB,OAAO,CAACwB,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,KAAG,EAAI,CAAC,cAC5CpD,IAAA,OAAAwC,QAAA,CAAK,EAAAU,aAAA,CAAAD,MAAM,CAACX,KAAK,UAAAY,aAAA,iBAAZA,aAAA,CAAcG,KAAK,GAAI,KAAK,CAAK,CAAC,cACvCrD,IAAA,OAAAwC,QAAA,CAAKS,MAAM,CAACK,SAAS,EAAI,KAAK,CAAK,CAAC,cACpCtD,IAAA,OAAAwC,QAAA,cAAIxC,IAAA,CAACJ,KAAK,EAAC2D,EAAE,CAAEN,MAAM,CAACO,aAAa,GAAK,UAAU,CAAG,SAAS,CAAG,SAAU,CAAAhB,QAAA,CAAES,MAAM,CAACO,aAAa,CAAQ,CAAC,CAAI,CAAC,cAC/GxD,IAAA,OAAAwC,QAAA,CAAK,GAAI,CAAAiB,IAAI,EAAAN,cAAA,CAACF,MAAM,CAACX,KAAK,UAAAa,cAAA,iBAAZA,cAAA,CAAcO,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC,CAAK,CAAC,GALzDV,MAAM,CAACrB,OAMZ,CAAC,EACR,CACJ,CACE,CAAC,EACL,CAAC,CACD,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,EACC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAAzB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}