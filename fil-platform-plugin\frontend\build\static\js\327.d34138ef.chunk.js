"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[327],{1072:(e,r,s)=>{s.d(r,{A:()=>o});var a=s(8139),n=s.n(a),t=s(5043),d=s(7852),i=s(579);const l=t.forwardRef((e,r)=>{let{bsPrefix:s,className:a,as:t="div",...l}=e;const o=(0,d.oU)(s,"row"),c=(0,d.gy)(),u=(0,d.Jm)(),m=`${o}-cols`,h=[];return c.forEach(e=>{const r=l[e];let s;delete l[e],null!=r&&"object"===typeof r?({cols:s}=r):s=r;const a=e!==u?`-${e}`:"";null!=s&&h.push(`${m}${a}-${s}`)}),(0,i.jsx)(t,{ref:r,...l,className:n()(a,o,...h)})});l.displayName="Row";const o=l},2663:(e,r,s)=>{s.d(r,{Tj:()=>n,mf:()=>t});var a=s(5043);function n(e,r){let s=0;return a.Children.map(e,e=>a.isValidElement(e)?r(e,s++):e)}function t(e,r){return a.Children.toArray(e).some(e=>a.isValidElement(e)&&e.type===r)}},4063:(e,r,s)=>{s.d(r,{A:()=>o});var a=s(8139),n=s.n(a),t=s(5043),d=s(7852),i=s(579);const l=t.forwardRef((e,r)=>{let{bsPrefix:s,bg:a="primary",pill:t=!1,text:l,className:o,as:c="span",...u}=e;const m=(0,d.oU)(s,"badge");return(0,i.jsx)(c,{ref:r,...u,className:n()(o,m,t&&"rounded-pill",l&&`text-${l}`,a&&`bg-${a}`)})});l.displayName="Badge";const o=l},4196:(e,r,s)=>{s.d(r,{A:()=>o});var a=s(8139),n=s.n(a),t=s(5043),d=s(7852),i=s(579);const l=t.forwardRef((e,r)=>{let{bsPrefix:s,className:a,striped:t,bordered:l,borderless:o,hover:c,size:u,variant:m,responsive:h,...x}=e;const f=(0,d.oU)(s,"table"),b=n()(a,f,m&&`${f}-${m}`,u&&`${f}-${u}`,t&&`${f}-${"string"===typeof t?`striped-${t}`:"striped"}`,l&&`${f}-bordered`,o&&`${f}-borderless`,c&&`${f}-hover`),v=(0,i.jsx)("table",{...x,className:b,ref:r});if(h){let e=`${f}-responsive`;return"string"===typeof h&&(e=`${e}-${h}`),(0,i.jsx)("div",{className:e,children:v})}return v});l.displayName="Table";const o=l},6327:(e,r,s)=>{s.r(r),s.d(r,{default:()=>y});var a=s(5043),n=s(4063),t=s(3519),d=s(1072),i=s(8602),l=s(8628),o=s(4196),c=s(8139),u=s.n(c),m=s(7852),h=s(2663),x=s(579);function f(e,r,s){const a=(e-r)/(s-r)*100;return Math.round(1e3*a)/1e3}function b(e,r){let{min:s,now:a,max:n,label:t,visuallyHidden:d,striped:i,animated:l,className:o,style:c,variant:m,bsPrefix:h,...b}=e;return(0,x.jsx)("div",{ref:r,...b,role:"progressbar",className:u()(o,`${h}-bar`,{[`bg-${m}`]:m,[`${h}-bar-animated`]:l,[`${h}-bar-striped`]:l||i}),style:{width:`${f(a,s,n)}%`,...c},"aria-valuenow":a,"aria-valuemin":s,"aria-valuemax":n,children:d?(0,x.jsx)("span",{className:"visually-hidden",children:t}):t})}const v=a.forwardRef((e,r)=>{let{isChild:s=!1,...n}=e;const t={min:0,max:100,animated:!1,visuallyHidden:!1,striped:!1,...n};if(t.bsPrefix=(0,m.oU)(t.bsPrefix,"progress"),s)return b(t,r);const{min:d,now:i,max:l,label:o,visuallyHidden:c,striped:f,animated:v,bsPrefix:p,variant:j,className:N,children:y,...g}=t;return(0,x.jsx)("div",{ref:r,...g,className:u()(N,p),children:y?(0,h.Tj)(y,e=>(0,a.cloneElement)(e,{isChild:!0})):b({min:d,now:i,max:l,label:o,visuallyHidden:c,striped:f,animated:v,bsPrefix:p,variant:j},r)})});v.displayName="ProgressBar";const p=v;var j=s(4312),N=s(4117);const y=()=>{const{t:e}=(0,N.Bd)(),[r,s]=(0,a.useState)([]),[c,u]=(0,a.useState)(!0);(0,a.useEffect)(()=>{(async()=>{const e=(0,j.b)();if(!e)return;u(!0);const{data:{user:r}}=await e.auth.getUser();if(!r)return void u(!1);const{data:a,error:n}=await e.from("order_distributions").select("\n                    id,\n                    share_amount,\n                    reward_amount,\n                    fee_amount,\n                    progress,\n                    created_at,\n                    distribution_batches (\n                        id,\n                        currency_code,\n                        batch_amount,\n                        per_share_amount,\n                        status,\n                        distributed_at,\n                        products (\n                            name,\n                            category\n                        )\n                    ),\n                    orders (\n                        id,\n                        cid,\n                        shares,\n                        products (\n                            name,\n                            category\n                        )\n                    ),\n                    customer_profiles (\n                        real_name,\n                        users (\n                            email\n                        )\n                    )\n                ").order("created_at",{ascending:!1});n?console.error("Error fetching order distributions:",n):s(a),u(!1)})()},[]);const m=r=>{switch(r){case"completed":return(0,x.jsx)(n.A,{bg:"success",children:e("completed")});case"pending":return(0,x.jsx)(n.A,{bg:"warning",children:e("pending")});case"processing":return(0,x.jsx)(n.A,{bg:"info",children:e("processing")});default:return(0,x.jsx)(n.A,{bg:"secondary",children:r||"-"})}};return c?(0,x.jsx)("div",{children:e("loading_order_distributions")}):(0,x.jsxs)(t.A,{children:[(0,x.jsx)("h2",{className:"mb-4",children:e("order_distributions")}),(0,x.jsx)(d.A,{children:(0,x.jsx)(i.A,{children:(0,x.jsx)(l.A,{children:(0,x.jsx)(l.A.Body,{children:(0,x.jsxs)(o.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,x.jsx)("thead",{children:(0,x.jsxs)("tr",{children:[(0,x.jsx)("th",{children:e("distribution_id")}),(0,x.jsx)("th",{children:e("batch_id")}),(0,x.jsx)("th",{children:e("order_id")}),(0,x.jsx)("th",{children:e("customer")}),(0,x.jsx)("th",{children:e("product_name")}),(0,x.jsx)("th",{children:e("currency_code")}),(0,x.jsx)("th",{children:e("share_amount")}),(0,x.jsx)("th",{children:e("reward_amount")}),(0,x.jsx)("th",{children:e("fee_amount")}),(0,x.jsx)("th",{children:e("progress")}),(0,x.jsx)("th",{children:e("batch_status")}),(0,x.jsx)("th",{children:e("created_at")})]})}),(0,x.jsx)("tbody",{children:0===r.length?(0,x.jsx)("tr",{children:(0,x.jsx)("td",{colSpan:"12",className:"text-center",children:e("no_order_distributions_available")})}):r.map(e=>{var r,s,a,n,t,d,i,l,o,c,u,h,f,b,v;return(0,x.jsxs)("tr",{children:[(0,x.jsx)("td",{children:e.id}),(0,x.jsx)("td",{children:(null===(r=e.distribution_batches)||void 0===r?void 0:r.id)||"-"}),(0,x.jsx)("td",{children:(null===(s=e.orders)||void 0===s?void 0:s.cid)||(null===(a=e.orders)||void 0===a?void 0:a.id)||"-"}),(0,x.jsx)("td",{children:(0,x.jsxs)("div",{children:[(0,x.jsx)("div",{children:(null===(n=e.customer_profiles)||void 0===n?void 0:n.real_name)||"-"}),(0,x.jsx)("small",{className:"text-muted",children:(null===(t=e.customer_profiles)||void 0===t||null===(d=t.users)||void 0===d?void 0:d.email)||"-"})]})}),(0,x.jsx)("td",{children:(null===(i=e.orders)||void 0===i||null===(l=i.products)||void 0===l?void 0:l.name)||(null===(o=e.distribution_batches)||void 0===o||null===(c=o.products)||void 0===c?void 0:c.name)||"-"}),(0,x.jsx)("td",{children:(null===(u=e.distribution_batches)||void 0===u?void 0:u.currency_code)||"-"}),(0,x.jsx)("td",{children:(null===(h=e.share_amount)||void 0===h?void 0:h.toFixed(2))||"0.00"}),(0,x.jsx)("td",{children:(null===(f=e.reward_amount)||void 0===f?void 0:f.toFixed(6))||"0.000000"}),(0,x.jsx)("td",{children:(null===(b=e.fee_amount)||void 0===b?void 0:b.toFixed(6))||"0.000000"}),(0,x.jsx)("td",{children:(0,x.jsx)("div",{children:(0,x.jsx)(p,{now:100*e.progress,label:`${(100*e.progress).toFixed(1)}%`,style:{minWidth:"100px"}})})}),(0,x.jsx)("td",{children:m(null===(v=e.distribution_batches)||void 0===v?void 0:v.status)}),(0,x.jsx)("td",{children:new Date(e.created_at).toLocaleString()})]},e.id)})})]})})})})})]})}},8602:(e,r,s)=>{s.d(r,{A:()=>o});var a=s(8139),n=s.n(a),t=s(5043),d=s(7852),i=s(579);const l=t.forwardRef((e,r)=>{const[{className:s,...a},{as:t="div",bsPrefix:l,spans:o}]=function(e){let{as:r,bsPrefix:s,className:a,...t}=e;s=(0,d.oU)(s,"col");const i=(0,d.gy)(),l=(0,d.Jm)(),o=[],c=[];return i.forEach(e=>{const r=t[e];let a,n,d;delete t[e],"object"===typeof r&&null!=r?({span:a,offset:n,order:d}=r):a=r;const i=e!==l?`-${e}`:"";a&&o.push(!0===a?`${s}${i}`:`${s}${i}-${a}`),null!=d&&c.push(`order${i}-${d}`),null!=n&&c.push(`offset${i}-${n}`)}),[{...t,className:n()(a,...o,...c)},{as:r,bsPrefix:s,spans:o}]}(e);return(0,i.jsx)(t,{...a,ref:r,className:n()(s,!o.length&&l)})});l.displayName="Col";const o=l},8628:(e,r,s)=>{s.d(r,{A:()=>k});var a=s(8139),n=s.n(a),t=s(5043),d=s(7852),i=s(579);const l=t.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:t="div",...l}=e;return a=(0,d.oU)(a,"card-body"),(0,i.jsx)(t,{ref:r,className:n()(s,a),...l})});l.displayName="CardBody";const o=l,c=t.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:t="div",...l}=e;return a=(0,d.oU)(a,"card-footer"),(0,i.jsx)(t,{ref:r,className:n()(s,a),...l})});c.displayName="CardFooter";const u=c;var m=s(1778);const h=t.forwardRef((e,r)=>{let{bsPrefix:s,className:a,as:l="div",...o}=e;const c=(0,d.oU)(s,"card-header"),u=(0,t.useMemo)(()=>({cardHeaderBsPrefix:c}),[c]);return(0,i.jsx)(m.A.Provider,{value:u,children:(0,i.jsx)(l,{ref:r,...o,className:n()(a,c)})})});h.displayName="CardHeader";const x=h,f=t.forwardRef((e,r)=>{let{bsPrefix:s,className:a,variant:t,as:l="img",...o}=e;const c=(0,d.oU)(s,"card-img");return(0,i.jsx)(l,{ref:r,className:n()(t?`${c}-${t}`:c,a),...o})});f.displayName="CardImg";const b=f,v=t.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:t="div",...l}=e;return a=(0,d.oU)(a,"card-img-overlay"),(0,i.jsx)(t,{ref:r,className:n()(s,a),...l})});v.displayName="CardImgOverlay";const p=v,j=t.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:t="a",...l}=e;return a=(0,d.oU)(a,"card-link"),(0,i.jsx)(t,{ref:r,className:n()(s,a),...l})});j.displayName="CardLink";const N=j;var y=s(4488);const g=(0,y.A)("h6"),_=t.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:t=g,...l}=e;return a=(0,d.oU)(a,"card-subtitle"),(0,i.jsx)(t,{ref:r,className:n()(s,a),...l})});_.displayName="CardSubtitle";const $=_,w=t.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:t="p",...l}=e;return a=(0,d.oU)(a,"card-text"),(0,i.jsx)(t,{ref:r,className:n()(s,a),...l})});w.displayName="CardText";const P=w,A=(0,y.A)("h5"),C=t.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:t=A,...l}=e;return a=(0,d.oU)(a,"card-title"),(0,i.jsx)(t,{ref:r,className:n()(s,a),...l})});C.displayName="CardTitle";const R=C,U=t.forwardRef((e,r)=>{let{bsPrefix:s,className:a,bg:t,text:l,border:c,body:u=!1,children:m,as:h="div",...x}=e;const f=(0,d.oU)(s,"card");return(0,i.jsx)(h,{ref:r,...x,className:n()(a,f,t&&`bg-${t}`,l&&`text-${l}`,c&&`border-${c}`),children:u?(0,i.jsx)(o,{children:m}):m})});U.displayName="Card";const k=Object.assign(U,{Img:b,Title:R,Subtitle:$,Body:o,Link:N,Text:P,Header:x,Footer:u,ImgOverlay:p})}}]);
//# sourceMappingURL=327.d34138ef.chunk.js.map