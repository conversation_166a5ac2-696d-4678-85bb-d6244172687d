import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { I18nextProvider } from 'react-i18next';
import i18n from '../../i18n';
import MakerMiners from './MakerMiners';
import MakerFacilities from './MakerFacilities';

// Mock the supabaseClient
const mockSupabase = {
  auth: {
    getUser: jest.fn(() => Promise.resolve({ data: { user: { id: 'test-user' } } }))
  },
  from: jest.fn(() => ({
    select: jest.fn(() => ({
      order: jest.fn(() => Promise.resolve({
        data: [
          {
            id: 'test-miner-1',
            category: 'test-category',
            filecoin_miner_id: 'f01234',
            sector_size: '32GiB',
            effective_until: '2024-12-31',
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-01T00:00:00Z',
            facilities: { name: 'Test Facility' }
          }
        ],
        error: null
      }))
    })),
    update: jest.fn(() => ({
      eq: jest.fn(() => Promise.resolve({ error: null }))
    })),
    delete: jest.fn(() => ({
      eq: jest.fn(() => Promise.resolve({ error: null }))
    }))
  }))
};

jest.mock('../../supabaseClient', () => ({
  getSupabase: jest.fn(() => mockSupabase)
}));

const renderWithProviders = (component) => {
  return render(
    <BrowserRouter>
      <I18nextProvider i18n={i18n}>
        {component}
      </I18nextProvider>
    </BrowserRouter>
  );
};

describe('Edit/Delete Functionality', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('MakerMiners renders edit and delete buttons', async () => {
    renderWithProviders(<MakerMiners />);
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('f01234')).toBeInTheDocument();
    });

    // Check if edit and delete buttons are present
    const editButtons = screen.getAllByText('Edit');
    const deleteButtons = screen.getAllByText('Delete');
    
    expect(editButtons.length).toBeGreaterThan(0);
    expect(deleteButtons.length).toBeGreaterThan(0);
  });

  test('Edit button opens modal for MakerMiners', async () => {
    renderWithProviders(<MakerMiners />);
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('f01234')).toBeInTheDocument();
    });

    // Click edit button
    const editButton = screen.getAllByText('Edit')[0];
    fireEvent.click(editButton);

    // Check if modal opens
    await waitFor(() => {
      expect(screen.getByText('Edit Miner')).toBeInTheDocument();
    });
  });

  test('Delete button opens confirmation modal for MakerMiners', async () => {
    renderWithProviders(<MakerMiners />);
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('f01234')).toBeInTheDocument();
    });

    // Click delete button
    const deleteButton = screen.getAllByText('Delete')[0];
    fireEvent.click(deleteButton);

    // Check if confirmation modal opens
    await waitFor(() => {
      expect(screen.getByText('Confirm Delete')).toBeInTheDocument();
    });
  });
});
