[{"D:\\New_System\\fil-platform-plugin\\frontend\\src\\index.js": "1", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\App.js": "2", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\i18n.js": "3", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\supabaseClient.js": "4", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\LoginPage.js": "5", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerOrderListPage.js": "6", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Dashboard.js": "7", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\RecommendPage.js": "8", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerProductListPage.js": "9", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\KycPage.js": "10", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyGainsPage.js": "11", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyAccountPage.js": "12", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\WalletPage.js": "13", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\OrderListPage.js": "14", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\ProductListPage.js": "15", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\Dashboard.js": "16", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Dashboard.js": "17", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentProductListPage.js": "18", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerMiners.js": "19", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerFacilities.js": "20", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerSnapshots.js": "21", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Transactions.js": "22", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerEarnings.js": "23", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\OrderReports.js": "24", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\OrderDistributions.js": "25", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CustomerAssets.js": "26", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\NetworkStats.js": "27", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CoinBatches.js": "28", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\ManualDeposits.js": "29", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CapacityRequest.js": "30", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\DebugAgent.js": "31", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Recommend.js": "32", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\NetworkStats.js": "33", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\CapacityRequest.js": "34", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Members.js": "35"}, {"size": 408, "mtime": 1751951658003, "results": "36", "hashOfConfig": "37"}, {"size": 16980, "mtime": 1752203384110, "results": "38", "hashOfConfig": "37"}, {"size": 47853, "mtime": 1752200646964, "results": "39", "hashOfConfig": "37"}, {"size": 1212, "mtime": 1751873051207, "results": "40", "hashOfConfig": "37"}, {"size": 3250, "mtime": 1751953679420, "results": "41", "hashOfConfig": "37"}, {"size": 4791, "mtime": 1751960448046, "results": "42", "hashOfConfig": "37"}, {"size": 3994, "mtime": 1752113564124, "results": "43", "hashOfConfig": "37"}, {"size": 4610, "mtime": 1751946228349, "results": "44", "hashOfConfig": "37"}, {"size": 5273, "mtime": 1751960463052, "results": "45", "hashOfConfig": "37"}, {"size": 8598, "mtime": 1751939997191, "results": "46", "hashOfConfig": "37"}, {"size": 4230, "mtime": 1751940026705, "results": "47", "hashOfConfig": "37"}, {"size": 1735, "mtime": 1751940008151, "results": "48", "hashOfConfig": "37"}, {"size": 4711, "mtime": 1752120569812, "results": "49", "hashOfConfig": "37"}, {"size": 4495, "mtime": 1751940037703, "results": "50", "hashOfConfig": "37"}, {"size": 3655, "mtime": 1751948557098, "results": "51", "hashOfConfig": "37"}, {"size": 4863, "mtime": 1751950041198, "results": "52", "hashOfConfig": "37"}, {"size": 9840, "mtime": 1752203448761, "results": "53", "hashOfConfig": "37"}, {"size": 4027, "mtime": 1752200506827, "results": "54", "hashOfConfig": "37"}, {"size": 13565, "mtime": 1752120431667, "results": "55", "hashOfConfig": "37"}, {"size": 10902, "mtime": 1752120494989, "results": "56", "hashOfConfig": "37"}, {"size": 4650, "mtime": 1752111918233, "results": "57", "hashOfConfig": "37"}, {"size": 5979, "mtime": 1752111934504, "results": "58", "hashOfConfig": "37"}, {"size": 4355, "mtime": 1752111904775, "results": "59", "hashOfConfig": "37"}, {"size": 6983, "mtime": 1752115841378, "results": "60", "hashOfConfig": "37"}, {"size": 7638, "mtime": 1752115874068, "results": "61", "hashOfConfig": "37"}, {"size": 4798, "mtime": 1752121974201, "results": "62", "hashOfConfig": "37"}, {"size": 3286, "mtime": 1752114312287, "results": "63", "hashOfConfig": "37"}, {"size": 6196, "mtime": 1752114298217, "results": "64", "hashOfConfig": "37"}, {"size": 7110, "mtime": 1752120864192, "results": "65", "hashOfConfig": "37"}, {"size": 7223, "mtime": 1752119937411, "results": "66", "hashOfConfig": "37"}, {"size": 7467, "mtime": 1752122455950, "results": "67", "hashOfConfig": "37"}, {"size": 19408, "mtime": 1752203822368, "results": "68", "hashOfConfig": "37"}, {"size": 3082, "mtime": 1752196922906, "results": "69", "hashOfConfig": "37"}, {"size": 7923, "mtime": 1752196766555, "results": "70", "hashOfConfig": "37"}, {"size": 15555, "mtime": 1752204350731, "results": "71", "hashOfConfig": "37"}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ji7irk", {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\New_System\\fil-platform-plugin\\frontend\\src\\index.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\App.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\i18n.js", ["177", "178", "179", "180", "181", "182", "183", "184", "185", "186", "187", "188", "189", "190", "191"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\supabaseClient.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\LoginPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerOrderListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Dashboard.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\RecommendPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerProductListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\KycPage.js", ["192"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyGainsPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyAccountPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\WalletPage.js", ["193"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\OrderListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\ProductListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\Dashboard.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Dashboard.js", ["194"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentProductListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerMiners.js", ["195"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerFacilities.js", ["196"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerSnapshots.js", ["197"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Transactions.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerEarnings.js", ["198"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\OrderReports.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\OrderDistributions.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CustomerAssets.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\NetworkStats.js", ["199"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CoinBatches.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\ManualDeposits.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CapacityRequest.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\DebugAgent.js", ["200", "201"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Recommend.js", ["202", "203", "204"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\NetworkStats.js", ["205"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\CapacityRequest.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Members.js", ["206", "207"], [], {"ruleId": "208", "severity": 1, "message": "209", "line": 181, "column": 7, "nodeType": "210", "messageId": "211", "endLine": 181, "endColumn": 18}, {"ruleId": "208", "severity": 1, "message": "212", "line": 183, "column": 7, "nodeType": "210", "messageId": "211", "endLine": 183, "endColumn": 23}, {"ruleId": "208", "severity": 1, "message": "213", "line": 294, "column": 7, "nodeType": "210", "messageId": "211", "endLine": 294, "endColumn": 17}, {"ruleId": "208", "severity": 1, "message": "214", "line": 341, "column": 7, "nodeType": "210", "messageId": "211", "endLine": 341, "endColumn": 26}, {"ruleId": "208", "severity": 1, "message": "215", "line": 342, "column": 7, "nodeType": "210", "messageId": "211", "endLine": 342, "endColumn": 27}, {"ruleId": "208", "severity": 1, "message": "209", "line": 530, "column": 7, "nodeType": "210", "messageId": "211", "endLine": 530, "endColumn": 18}, {"ruleId": "208", "severity": 1, "message": "212", "line": 532, "column": 7, "nodeType": "210", "messageId": "211", "endLine": 532, "endColumn": 23}, {"ruleId": "208", "severity": 1, "message": "213", "line": 643, "column": 7, "nodeType": "210", "messageId": "211", "endLine": 643, "endColumn": 17}, {"ruleId": "208", "severity": 1, "message": "214", "line": 690, "column": 7, "nodeType": "210", "messageId": "211", "endLine": 690, "endColumn": 26}, {"ruleId": "208", "severity": 1, "message": "215", "line": 691, "column": 7, "nodeType": "210", "messageId": "211", "endLine": 691, "endColumn": 27}, {"ruleId": "208", "severity": 1, "message": "209", "line": 879, "column": 7, "nodeType": "210", "messageId": "211", "endLine": 879, "endColumn": 18}, {"ruleId": "208", "severity": 1, "message": "212", "line": 881, "column": 7, "nodeType": "210", "messageId": "211", "endLine": 881, "endColumn": 23}, {"ruleId": "208", "severity": 1, "message": "213", "line": 992, "column": 7, "nodeType": "210", "messageId": "211", "endLine": 992, "endColumn": 17}, {"ruleId": "208", "severity": 1, "message": "214", "line": 1039, "column": 7, "nodeType": "210", "messageId": "211", "endLine": 1039, "endColumn": 26}, {"ruleId": "208", "severity": 1, "message": "215", "line": 1040, "column": 7, "nodeType": "210", "messageId": "211", "endLine": 1040, "endColumn": 27}, {"ruleId": "216", "severity": 1, "message": "217", "line": 49, "column": 8, "nodeType": "218", "endLine": 49, "endColumn": 10, "suggestions": "219"}, {"ruleId": "220", "severity": 1, "message": "221", "line": 3, "column": 44, "nodeType": "222", "messageId": "223", "endLine": 3, "endColumn": 50}, {"ruleId": "220", "severity": 1, "message": "224", "line": 23, "column": 46, "nodeType": "222", "messageId": "223", "endLine": 23, "endColumn": 57}, {"ruleId": "220", "severity": 1, "message": "225", "line": 2, "column": 44, "nodeType": "222", "messageId": "223", "endLine": 2, "endColumn": 49}, {"ruleId": "220", "severity": 1, "message": "225", "line": 2, "column": 44, "nodeType": "222", "messageId": "223", "endLine": 2, "endColumn": 49}, {"ruleId": "220", "severity": 1, "message": "225", "line": 2, "column": 44, "nodeType": "222", "messageId": "223", "endLine": 2, "endColumn": 49}, {"ruleId": "220", "severity": 1, "message": "225", "line": 2, "column": 44, "nodeType": "222", "messageId": "223", "endLine": 2, "endColumn": 49}, {"ruleId": "220", "severity": 1, "message": "225", "line": 2, "column": 44, "nodeType": "222", "messageId": "223", "endLine": 2, "endColumn": 49}, {"ruleId": "220", "severity": 1, "message": "226", "line": 7, "column": 13, "nodeType": "222", "messageId": "223", "endLine": 7, "endColumn": 14}, {"ruleId": "220", "severity": 1, "message": "227", "line": 90, "column": 21, "nodeType": "222", "messageId": "223", "endLine": 90, "endColumn": 25}, {"ruleId": "220", "severity": 1, "message": "228", "line": 3, "column": 98, "nodeType": "222", "messageId": "223", "endLine": 3, "endColumn": 106}, {"ruleId": "220", "severity": 1, "message": "229", "line": 25, "column": 17, "nodeType": "222", "messageId": "223", "endLine": 25, "endColumn": 26}, {"ruleId": "220", "severity": 1, "message": "230", "line": 40, "column": 31, "nodeType": "222", "messageId": "223", "endLine": 40, "endColumn": 43}, {"ruleId": "220", "severity": 1, "message": "225", "line": 2, "column": 44, "nodeType": "222", "messageId": "223", "endLine": 2, "endColumn": 49}, {"ruleId": "220", "severity": 1, "message": "231", "line": 2, "column": 77, "nodeType": "222", "messageId": "223", "endLine": 2, "endColumn": 85}, {"ruleId": "220", "severity": 1, "message": "232", "line": 44, "column": 23, "nodeType": "222", "messageId": "223", "endLine": 44, "endColumn": 30}, "no-dupe-keys", "Duplicate key 'no_assets'.", "ObjectExpression", "unexpected", "Duplicate key 'my_invite_code'.", "Duplicate key 'batch_id'.", "Duplicate key 'member_management'.", "Duplicate key 'product_management'.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 't'. Either include it or remove the dependency array.", "ArrayExpression", ["233"], "no-unused-vars", "'Button' is defined but never used.", "Identifier", "unusedVar", "'memberError' is assigned a value but never used.", "'Badge' is defined but never used.", "'t' is assigned a value but never used.", "'data' is assigned a value but never used.", "'FaSearch' is defined but never used.", "'customers' is assigned a value but never used.", "'agentProfile' is assigned a value but never used.", "'Dropdown' is defined but never used.", "'userIds' is assigned a value but never used.", {"desc": "234", "fix": "235"}, "Update the dependencies array to be: [t]", {"range": "236", "text": "237"}, [1977, 1979], "[t]"]