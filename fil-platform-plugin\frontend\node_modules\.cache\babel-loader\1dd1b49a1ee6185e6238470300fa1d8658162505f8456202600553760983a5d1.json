{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Container,Row,Col,Card,Table,Badge}from'react-bootstrap';import{getSupabase}from'../../supabaseClient';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const OrderReports=()=>{const{t}=useTranslation();const[orders,setOrders]=useState([]);const[loading,setLoading]=useState(true);useEffect(()=>{const fetchOrders=async()=>{const supabase=getSupabase();if(!supabase)return;setLoading(true);const{data:{user}}=await supabase.auth.getUser();if(!user){setLoading(false);return;// User not logged in\n}// First get the agent profile for the current user\nconst{data:agentProfile,error:agentError}=await supabase.from('agent_profiles').select('user_id').eq('user_id',user.id).single();if(agentError||!agentProfile){console.error('Error fetching agent profile:',agentError);setLoading(false);return;}// Fetch orders for this specific agent with related information\nconst{data,error}=await supabase.from('orders').select(`\n                    id,\n                    cid,\n                    shares,\n                    proof_image_url,\n                    storage_cost,\n                    pledge_cost,\n                    total_rate,\n                    tech_fee_pct,\n                    sales_fee_pct,\n                    ops_fee_pct,\n                    start_at,\n                    end_at,\n                    review_status,\n                    created_at,\n                    updated_at,\n                    products (\n                        name,\n                        category,\n                        price\n                    ),\n                    customer_profiles (\n                        real_name,\n                        users (\n                            email\n                        )\n                    )\n                `).eq('agent_id',agentProfile.user_id).order('created_at',{ascending:false});if(error){console.error('Error fetching orders:',error);}else{setOrders(data);}setLoading(false);};fetchOrders();},[]);const getStatusBadge=status=>{switch(status){case'approved':return/*#__PURE__*/_jsx(Badge,{bg:\"success\",children:t('approved')});case'pending':return/*#__PURE__*/_jsx(Badge,{bg:\"warning\",children:t('pending_review')});case'rejected':return/*#__PURE__*/_jsx(Badge,{bg:\"danger\",children:t('rejected')});default:return/*#__PURE__*/_jsx(Badge,{bg:\"secondary\",children:status||'-'});}};if(loading){return/*#__PURE__*/_jsx(\"div\",{children:t('loading_order_reports')});}return/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"mb-4\",children:t('order_list')}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Table,{striped:true,bordered:true,hover:true,responsive:true,children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:t('order_id')}),/*#__PURE__*/_jsx(\"th\",{children:t('product_name')}),/*#__PURE__*/_jsx(\"th\",{children:t('customer')}),/*#__PURE__*/_jsx(\"th\",{children:t('shares')}),/*#__PURE__*/_jsx(\"th\",{children:t('storage_cost')}),/*#__PURE__*/_jsx(\"th\",{children:t('pledge_cost')}),/*#__PURE__*/_jsx(\"th\",{children:t('total_rate')}),/*#__PURE__*/_jsx(\"th\",{children:t('sales_commission')}),/*#__PURE__*/_jsx(\"th\",{children:t('start_date')}),/*#__PURE__*/_jsx(\"th\",{children:t('end_date')}),/*#__PURE__*/_jsx(\"th\",{children:t('review_status')}),/*#__PURE__*/_jsx(\"th\",{children:t('created_at')})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:orders.length===0?/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:\"12\",className:\"text-center\",children:t('no_orders_available')})}):orders.map(order=>{var _order$products,_order$customer_profi,_order$customer_profi2,_order$customer_profi3,_order$shares,_order$storage_cost,_order$pledge_cost,_order$total_rate;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:order.cid||order.id}),/*#__PURE__*/_jsx(\"td\",{children:((_order$products=order.products)===null||_order$products===void 0?void 0:_order$products.name)||'-'}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{children:((_order$customer_profi=order.customer_profiles)===null||_order$customer_profi===void 0?void 0:_order$customer_profi.real_name)||'-'}),/*#__PURE__*/_jsx(\"small\",{className:\"text-muted\",children:((_order$customer_profi2=order.customer_profiles)===null||_order$customer_profi2===void 0?void 0:(_order$customer_profi3=_order$customer_profi2.users)===null||_order$customer_profi3===void 0?void 0:_order$customer_profi3.email)||'-'})]})}),/*#__PURE__*/_jsx(\"td\",{children:((_order$shares=order.shares)===null||_order$shares===void 0?void 0:_order$shares.toFixed(2))||'0.00'}),/*#__PURE__*/_jsx(\"td\",{children:((_order$storage_cost=order.storage_cost)===null||_order$storage_cost===void 0?void 0:_order$storage_cost.toFixed(6))||'0.000000'}),/*#__PURE__*/_jsx(\"td\",{children:((_order$pledge_cost=order.pledge_cost)===null||_order$pledge_cost===void 0?void 0:_order$pledge_cost.toFixed(6))||'0.000000'}),/*#__PURE__*/_jsxs(\"td\",{children:[((_order$total_rate=order.total_rate)===null||_order$total_rate===void 0?void 0:_order$total_rate.toFixed(4))||'0.0000',\"%\"]}),/*#__PURE__*/_jsx(\"td\",{children:order.sales_fee_pct?`${order.sales_fee_pct.toFixed(2)}%`:'-'}),/*#__PURE__*/_jsx(\"td\",{children:order.start_at||'-'}),/*#__PURE__*/_jsx(\"td\",{children:order.end_at||'-'}),/*#__PURE__*/_jsx(\"td\",{children:getStatusBadge(order.review_status)}),/*#__PURE__*/_jsx(\"td\",{children:new Date(order.created_at).toLocaleString()})]},order.id);})})]})})})})})]});};export default OrderReports;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Table", "Badge", "getSupabase", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "OrderReports", "t", "orders", "setOrders", "loading", "setLoading", "fetchOrders", "supabase", "data", "user", "auth", "getUser", "agentProfile", "error", "agent<PERSON><PERSON>r", "from", "select", "eq", "id", "single", "console", "user_id", "order", "ascending", "getStatusBadge", "status", "bg", "children", "className", "Body", "striped", "bordered", "hover", "responsive", "length", "colSpan", "map", "_order$products", "_order$customer_profi", "_order$customer_profi2", "_order$customer_profi3", "_order$shares", "_order$storage_cost", "_order$pledge_cost", "_order$total_rate", "cid", "products", "name", "customer_profiles", "real_name", "users", "email", "shares", "toFixed", "storage_cost", "pledge_cost", "total_rate", "sales_fee_pct", "start_at", "end_at", "review_status", "Date", "created_at", "toLocaleString"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/agent/OrderReports.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst OrderReports = () => {\n    const { t } = useTranslation();\n    const [orders, setOrders] = useState([]);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        const fetchOrders = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setLoading(false);\n                return; // User not logged in\n            }\n\n            // First get the agent profile for the current user\n            const { data: agentProfile, error: agentError } = await supabase\n                .from('agent_profiles')\n                .select('user_id')\n                .eq('user_id', user.id)\n                .single();\n\n            if (agentError || !agentProfile) {\n                console.error('Error fetching agent profile:', agentError);\n                setLoading(false);\n                return;\n            }\n\n            // Fetch orders for this specific agent with related information\n            const { data, error } = await supabase\n                .from('orders')\n                .select(`\n                    id,\n                    cid,\n                    shares,\n                    proof_image_url,\n                    storage_cost,\n                    pledge_cost,\n                    total_rate,\n                    tech_fee_pct,\n                    sales_fee_pct,\n                    ops_fee_pct,\n                    start_at,\n                    end_at,\n                    review_status,\n                    created_at,\n                    updated_at,\n                    products (\n                        name,\n                        category,\n                        price\n                    ),\n                    customer_profiles (\n                        real_name,\n                        users (\n                            email\n                        )\n                    )\n                `)\n                .eq('agent_id', agentProfile.user_id)\n                .order('created_at', { ascending: false });\n\n            if (error) {\n                console.error('Error fetching orders:', error);\n            } else {\n                setOrders(data);\n            }\n            setLoading(false);\n        };\n\n        fetchOrders();\n    }, []);\n\n    const getStatusBadge = (status) => {\n        switch (status) {\n            case 'approved':\n                return <Badge bg=\"success\">{t('approved')}</Badge>;\n            case 'pending':\n                return <Badge bg=\"warning\">{t('pending_review')}</Badge>;\n            case 'rejected':\n                return <Badge bg=\"danger\">{t('rejected')}</Badge>;\n            default:\n                return <Badge bg=\"secondary\">{status || '-'}</Badge>;\n        }\n    };\n\n    if (loading) {\n        return <div>{t('loading_order_reports')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('order_list')}</h2>\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('order_id')}</th>\n                                        <th>{t('product_name')}</th>\n                                        <th>{t('customer')}</th>\n                                        <th>{t('shares')}</th>\n                                        <th>{t('storage_cost')}</th>\n                                        <th>{t('pledge_cost')}</th>\n                                        <th>{t('total_rate')}</th>\n                                        <th>{t('sales_commission')}</th>\n                                        <th>{t('start_date')}</th>\n                                        <th>{t('end_date')}</th>\n                                        <th>{t('review_status')}</th>\n                                        <th>{t('created_at')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {orders.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"12\" className=\"text-center\">{t('no_orders_available')}</td>\n                                        </tr>\n                                    ) : (\n                                        orders.map(order => (\n                                            <tr key={order.id}>\n                                                <td>{order.cid || order.id}</td>\n                                                <td>{order.products?.name || '-'}</td>\n                                                <td>\n                                                    <div>\n                                                        <div>{order.customer_profiles?.real_name || '-'}</div>\n                                                        <small className=\"text-muted\">\n                                                            {order.customer_profiles?.users?.email || '-'}\n                                                        </small>\n                                                    </div>\n                                                </td>\n                                                <td>{order.shares?.toFixed(2) || '0.00'}</td>\n                                                <td>{order.storage_cost?.toFixed(6) || '0.000000'}</td>\n                                                <td>{order.pledge_cost?.toFixed(6) || '0.000000'}</td>\n                                                <td>{order.total_rate?.toFixed(4) || '0.0000'}%</td>\n                                                <td>\n                                                    {order.sales_fee_pct ?\n                                                        `${order.sales_fee_pct.toFixed(2)}%` :\n                                                        '-'\n                                                    }\n                                                </td>\n                                                <td>{order.start_at || '-'}</td>\n                                                <td>{order.end_at || '-'}</td>\n                                                <td>{getStatusBadge(order.review_status)}</td>\n                                                <td>{new Date(order.created_at).toLocaleString()}</td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default OrderReports;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,KAAK,CAAEC,KAAK,KAAQ,iBAAiB,CACzE,OAASC,WAAW,KAAQ,sBAAsB,CAClD,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACvB,KAAM,CAAEC,CAAE,CAAC,CAAGN,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACO,MAAM,CAAEC,SAAS,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAACkB,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CAE5CC,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAmB,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC5B,KAAM,CAAAC,QAAQ,CAAGb,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACa,QAAQ,CAAE,OAEfF,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEG,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CAExD,GAAI,CAACF,IAAI,CAAE,CACPJ,UAAU,CAAC,KAAK,CAAC,CACjB,OAAQ;AACZ,CAEA;AACA,KAAM,CAAEG,IAAI,CAAEI,YAAY,CAAEC,KAAK,CAAEC,UAAW,CAAC,CAAG,KAAM,CAAAP,QAAQ,CAC3DQ,IAAI,CAAC,gBAAgB,CAAC,CACtBC,MAAM,CAAC,SAAS,CAAC,CACjBC,EAAE,CAAC,SAAS,CAAER,IAAI,CAACS,EAAE,CAAC,CACtBC,MAAM,CAAC,CAAC,CAEb,GAAIL,UAAU,EAAI,CAACF,YAAY,CAAE,CAC7BQ,OAAO,CAACP,KAAK,CAAC,+BAA+B,CAAEC,UAAU,CAAC,CAC1DT,UAAU,CAAC,KAAK,CAAC,CACjB,OACJ,CAEA;AACA,KAAM,CAAEG,IAAI,CAAEK,KAAM,CAAC,CAAG,KAAM,CAAAN,QAAQ,CACjCQ,IAAI,CAAC,QAAQ,CAAC,CACdC,MAAM,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,CAAC,CACDC,EAAE,CAAC,UAAU,CAAEL,YAAY,CAACS,OAAO,CAAC,CACpCC,KAAK,CAAC,YAAY,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAE9C,GAAIV,KAAK,CAAE,CACPO,OAAO,CAACP,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAClD,CAAC,IAAM,CACHV,SAAS,CAACK,IAAI,CAAC,CACnB,CACAH,UAAU,CAAC,KAAK,CAAC,CACrB,CAAC,CAEDC,WAAW,CAAC,CAAC,CACjB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAkB,cAAc,CAAIC,MAAM,EAAK,CAC/B,OAAQA,MAAM,EACV,IAAK,UAAU,CACX,mBAAO5B,IAAA,CAACJ,KAAK,EAACiC,EAAE,CAAC,SAAS,CAAAC,QAAA,CAAE1B,CAAC,CAAC,UAAU,CAAC,CAAQ,CAAC,CACtD,IAAK,SAAS,CACV,mBAAOJ,IAAA,CAACJ,KAAK,EAACiC,EAAE,CAAC,SAAS,CAAAC,QAAA,CAAE1B,CAAC,CAAC,gBAAgB,CAAC,CAAQ,CAAC,CAC5D,IAAK,UAAU,CACX,mBAAOJ,IAAA,CAACJ,KAAK,EAACiC,EAAE,CAAC,QAAQ,CAAAC,QAAA,CAAE1B,CAAC,CAAC,UAAU,CAAC,CAAQ,CAAC,CACrD,QACI,mBAAOJ,IAAA,CAACJ,KAAK,EAACiC,EAAE,CAAC,WAAW,CAAAC,QAAA,CAAEF,MAAM,EAAI,GAAG,CAAQ,CAAC,CAC5D,CACJ,CAAC,CAED,GAAIrB,OAAO,CAAE,CACT,mBAAOP,IAAA,QAAA8B,QAAA,CAAM1B,CAAC,CAAC,uBAAuB,CAAC,CAAM,CAAC,CAClD,CAEA,mBACIF,KAAA,CAACX,SAAS,EAAAuC,QAAA,eACN9B,IAAA,OAAI+B,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAE1B,CAAC,CAAC,YAAY,CAAC,CAAK,CAAC,cAC3CJ,IAAA,CAACR,GAAG,EAAAsC,QAAA,cACA9B,IAAA,CAACP,GAAG,EAAAqC,QAAA,cACA9B,IAAA,CAACN,IAAI,EAAAoC,QAAA,cACD9B,IAAA,CAACN,IAAI,CAACsC,IAAI,EAAAF,QAAA,cACN5B,KAAA,CAACP,KAAK,EAACsC,OAAO,MAACC,QAAQ,MAACC,KAAK,MAACC,UAAU,MAAAN,QAAA,eACpC9B,IAAA,UAAA8B,QAAA,cACI5B,KAAA,OAAA4B,QAAA,eACI9B,IAAA,OAAA8B,QAAA,CAAK1B,CAAC,CAAC,UAAU,CAAC,CAAK,CAAC,cACxBJ,IAAA,OAAA8B,QAAA,CAAK1B,CAAC,CAAC,cAAc,CAAC,CAAK,CAAC,cAC5BJ,IAAA,OAAA8B,QAAA,CAAK1B,CAAC,CAAC,UAAU,CAAC,CAAK,CAAC,cACxBJ,IAAA,OAAA8B,QAAA,CAAK1B,CAAC,CAAC,QAAQ,CAAC,CAAK,CAAC,cACtBJ,IAAA,OAAA8B,QAAA,CAAK1B,CAAC,CAAC,cAAc,CAAC,CAAK,CAAC,cAC5BJ,IAAA,OAAA8B,QAAA,CAAK1B,CAAC,CAAC,aAAa,CAAC,CAAK,CAAC,cAC3BJ,IAAA,OAAA8B,QAAA,CAAK1B,CAAC,CAAC,YAAY,CAAC,CAAK,CAAC,cAC1BJ,IAAA,OAAA8B,QAAA,CAAK1B,CAAC,CAAC,kBAAkB,CAAC,CAAK,CAAC,cAChCJ,IAAA,OAAA8B,QAAA,CAAK1B,CAAC,CAAC,YAAY,CAAC,CAAK,CAAC,cAC1BJ,IAAA,OAAA8B,QAAA,CAAK1B,CAAC,CAAC,UAAU,CAAC,CAAK,CAAC,cACxBJ,IAAA,OAAA8B,QAAA,CAAK1B,CAAC,CAAC,eAAe,CAAC,CAAK,CAAC,cAC7BJ,IAAA,OAAA8B,QAAA,CAAK1B,CAAC,CAAC,YAAY,CAAC,CAAK,CAAC,EAC1B,CAAC,CACF,CAAC,cACRJ,IAAA,UAAA8B,QAAA,CACKzB,MAAM,CAACgC,MAAM,GAAK,CAAC,cAChBrC,IAAA,OAAA8B,QAAA,cACI9B,IAAA,OAAIsC,OAAO,CAAC,IAAI,CAACP,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAE1B,CAAC,CAAC,qBAAqB,CAAC,CAAK,CAAC,CACxE,CAAC,CAELC,MAAM,CAACkC,GAAG,CAACd,KAAK,OAAAe,eAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,aAAA,CAAAC,mBAAA,CAAAC,kBAAA,CAAAC,iBAAA,oBACZ7C,KAAA,OAAA4B,QAAA,eACI9B,IAAA,OAAA8B,QAAA,CAAKL,KAAK,CAACuB,GAAG,EAAIvB,KAAK,CAACJ,EAAE,CAAK,CAAC,cAChCrB,IAAA,OAAA8B,QAAA,CAAK,EAAAU,eAAA,CAAAf,KAAK,CAACwB,QAAQ,UAAAT,eAAA,iBAAdA,eAAA,CAAgBU,IAAI,GAAI,GAAG,CAAK,CAAC,cACtClD,IAAA,OAAA8B,QAAA,cACI5B,KAAA,QAAA4B,QAAA,eACI9B,IAAA,QAAA8B,QAAA,CAAM,EAAAW,qBAAA,CAAAhB,KAAK,CAAC0B,iBAAiB,UAAAV,qBAAA,iBAAvBA,qBAAA,CAAyBW,SAAS,GAAI,GAAG,CAAM,CAAC,cACtDpD,IAAA,UAAO+B,SAAS,CAAC,YAAY,CAAAD,QAAA,CACxB,EAAAY,sBAAA,CAAAjB,KAAK,CAAC0B,iBAAiB,UAAAT,sBAAA,kBAAAC,sBAAA,CAAvBD,sBAAA,CAAyBW,KAAK,UAAAV,sBAAA,iBAA9BA,sBAAA,CAAgCW,KAAK,GAAI,GAAG,CAC1C,CAAC,EACP,CAAC,CACN,CAAC,cACLtD,IAAA,OAAA8B,QAAA,CAAK,EAAAc,aAAA,CAAAnB,KAAK,CAAC8B,MAAM,UAAAX,aAAA,iBAAZA,aAAA,CAAcY,OAAO,CAAC,CAAC,CAAC,GAAI,MAAM,CAAK,CAAC,cAC7CxD,IAAA,OAAA8B,QAAA,CAAK,EAAAe,mBAAA,CAAApB,KAAK,CAACgC,YAAY,UAAAZ,mBAAA,iBAAlBA,mBAAA,CAAoBW,OAAO,CAAC,CAAC,CAAC,GAAI,UAAU,CAAK,CAAC,cACvDxD,IAAA,OAAA8B,QAAA,CAAK,EAAAgB,kBAAA,CAAArB,KAAK,CAACiC,WAAW,UAAAZ,kBAAA,iBAAjBA,kBAAA,CAAmBU,OAAO,CAAC,CAAC,CAAC,GAAI,UAAU,CAAK,CAAC,cACtDtD,KAAA,OAAA4B,QAAA,EAAK,EAAAiB,iBAAA,CAAAtB,KAAK,CAACkC,UAAU,UAAAZ,iBAAA,iBAAhBA,iBAAA,CAAkBS,OAAO,CAAC,CAAC,CAAC,GAAI,QAAQ,CAAC,GAAC,EAAI,CAAC,cACpDxD,IAAA,OAAA8B,QAAA,CACKL,KAAK,CAACmC,aAAa,CAChB,GAAGnC,KAAK,CAACmC,aAAa,CAACJ,OAAO,CAAC,CAAC,CAAC,GAAG,CACpC,GAAG,CAEP,CAAC,cACLxD,IAAA,OAAA8B,QAAA,CAAKL,KAAK,CAACoC,QAAQ,EAAI,GAAG,CAAK,CAAC,cAChC7D,IAAA,OAAA8B,QAAA,CAAKL,KAAK,CAACqC,MAAM,EAAI,GAAG,CAAK,CAAC,cAC9B9D,IAAA,OAAA8B,QAAA,CAAKH,cAAc,CAACF,KAAK,CAACsC,aAAa,CAAC,CAAK,CAAC,cAC9C/D,IAAA,OAAA8B,QAAA,CAAK,GAAI,CAAAkC,IAAI,CAACvC,KAAK,CAACwC,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC,CAAK,CAAC,GAxBjDzC,KAAK,CAACJ,EAyBX,CAAC,EACR,CACJ,CACE,CAAC,EACL,CAAC,CACD,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,EACC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAAlB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}