"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[453],{1072:(e,r,s)=>{s.d(r,{A:()=>l});var a=s(8139),t=s.n(a),d=s(5043),n=s(7852),c=s(579);const i=d.forwardRef((e,r)=>{let{bsPrefix:s,className:a,as:d="div",...i}=e;const l=(0,n.oU)(s,"row"),o=(0,n.gy)(),x=(0,n.Jm)(),f=`${l}-cols`,h=[];return o.forEach(e=>{const r=i[e];let s;delete i[e],null!=r&&"object"===typeof r?({cols:s}=r):s=r;const a=e!==x?`-${e}`:"";null!=s&&h.push(`${f}${a}-${s}`)}),(0,c.jsx)(d,{ref:r,...i,className:t()(a,l,...h)})});i.displayName="Row";const l=i},4063:(e,r,s)=>{s.d(r,{A:()=>l});var a=s(8139),t=s.n(a),d=s(5043),n=s(7852),c=s(579);const i=d.forwardRef((e,r)=>{let{bsPrefix:s,bg:a="primary",pill:d=!1,text:i,className:l,as:o="span",...x}=e;const f=(0,n.oU)(s,"badge");return(0,c.jsx)(o,{ref:r,...x,className:t()(l,f,d&&"rounded-pill",i&&`text-${i}`,a&&`bg-${a}`)})});i.displayName="Badge";const l=i},4196:(e,r,s)=>{s.d(r,{A:()=>l});var a=s(8139),t=s.n(a),d=s(5043),n=s(7852),c=s(579);const i=d.forwardRef((e,r)=>{let{bsPrefix:s,className:a,striped:d,bordered:i,borderless:l,hover:o,size:x,variant:f,responsive:h,...u}=e;const p=(0,n.oU)(s,"table"),m=t()(a,p,f&&`${p}-${f}`,x&&`${p}-${x}`,d&&`${p}-${"string"===typeof d?`striped-${d}`:"striped"}`,i&&`${p}-bordered`,l&&`${p}-borderless`,o&&`${p}-hover`),j=(0,c.jsx)("table",{...u,className:m,ref:r});if(h){let e=`${p}-responsive`;return"string"===typeof h&&(e=`${e}-${h}`),(0,c.jsx)("div",{className:e,children:j})}return j});i.displayName="Table";const l=i},6453:(e,r,s)=>{s.r(r),s.d(r,{default:()=>h});var a=s(5043),t=s(4063),d=s(3519),n=s(1072),c=s(8602),i=s(8628),l=s(4196),o=s(4312),x=s(4117),f=s(579);const h=()=>{const{t:e}=(0,x.Bd)(),[r,s]=(0,a.useState)([]),[h,u]=(0,a.useState)(!0);(0,a.useEffect)(()=>{(async()=>{const e=(0,o.b)();if(!e)return;u(!0);const{data:{user:r}}=await e.auth.getUser();if(!r)return void u(!1);const{data:a,error:t}=await e.from("capacity_requests").select("\n                    id,\n                    product_category,\n                    added_capacity,\n                    capacity_before,\n                    capacity_after,\n                    status,\n                    description,\n                    review_reply,\n                    requested_at,\n                    reviewed_at,\n                    maker_profiles (\n                        domain,\n                        users (\n                            email\n                        )\n                    ),\n                    users (\n                        email,\n                        role\n                    )\n                ").order("requested_at",{ascending:!1});t?console.error("Error fetching capacity requests:",t):s(a),u(!1)})()},[]);const p=r=>{switch(r){case"approved":return(0,f.jsx)(t.A,{bg:"success",children:e("approved")});case"pending":return(0,f.jsx)(t.A,{bg:"warning",children:e("pending_review")});case"rejected":return(0,f.jsx)(t.A,{bg:"danger",children:e("rejected")});case"under_review":return(0,f.jsx)(t.A,{bg:"info",children:e("under_review")});default:return(0,f.jsx)(t.A,{bg:"secondary",children:r||"-"})}};return h?(0,f.jsx)("div",{children:e("loading_capacity_requests")}):(0,f.jsxs)(d.A,{children:[(0,f.jsx)("h2",{className:"mb-4",children:e("capacity_expansion_request")}),(0,f.jsx)(n.A,{children:(0,f.jsx)(c.A,{children:(0,f.jsx)(i.A,{children:(0,f.jsx)(i.A.Body,{children:(0,f.jsxs)(l.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,f.jsx)("thead",{children:(0,f.jsxs)("tr",{children:[(0,f.jsx)("th",{children:e("request_id")}),(0,f.jsx)("th",{children:e("maker")}),(0,f.jsx)("th",{children:e("requested_by")}),(0,f.jsx)("th",{children:e("product_category")}),(0,f.jsx)("th",{children:e("added_capacity")}),(0,f.jsx)("th",{children:e("capacity_before")}),(0,f.jsx)("th",{children:e("capacity_after")}),(0,f.jsx)("th",{children:e("status")}),(0,f.jsx)("th",{children:e("description")}),(0,f.jsx)("th",{children:e("review_reply")}),(0,f.jsx)("th",{children:e("requested_at")}),(0,f.jsx)("th",{children:e("reviewed_at")})]})}),(0,f.jsx)("tbody",{children:0===r.length?(0,f.jsx)("tr",{children:(0,f.jsx)("td",{colSpan:"12",className:"text-center",children:e("no_capacity_requests_available")})}):r.map(e=>{var r,s,a,d,n,c,i;return(0,f.jsxs)("tr",{children:[(0,f.jsx)("td",{children:e.id}),(0,f.jsx)("td",{children:(0,f.jsxs)("div",{children:[(0,f.jsx)("div",{children:(null===(r=e.maker_profiles)||void 0===r?void 0:r.domain)||"-"}),(0,f.jsx)("small",{className:"text-muted",children:(null===(s=e.maker_profiles)||void 0===s||null===(a=s.users)||void 0===a?void 0:a.email)||"-"})]})}),(0,f.jsx)("td",{children:(null===(d=e.users)||void 0===d?void 0:d.email)||"-"}),(0,f.jsx)("td",{children:(0,f.jsx)(t.A,{bg:"spot"===e.product_category?"primary":"secondary",children:e.product_category||"-"})}),(0,f.jsx)("td",{children:(null===(n=e.added_capacity)||void 0===n?void 0:n.toFixed(2))||"0.00"}),(0,f.jsx)("td",{children:(null===(c=e.capacity_before)||void 0===c?void 0:c.toFixed(2))||"0.00"}),(0,f.jsx)("td",{children:(null===(i=e.capacity_after)||void 0===i?void 0:i.toFixed(2))||"0.00"}),(0,f.jsx)("td",{children:p(e.status)}),(0,f.jsx)("td",{children:(0,f.jsx)("div",{style:{maxWidth:"200px",wordWrap:"break-word"},children:e.description||"-"})}),(0,f.jsx)("td",{children:(0,f.jsx)("div",{style:{maxWidth:"200px",wordWrap:"break-word"},children:e.review_reply||"-"})}),(0,f.jsx)("td",{children:new Date(e.requested_at).toLocaleString()}),(0,f.jsx)("td",{children:e.reviewed_at?new Date(e.reviewed_at).toLocaleString():"-"})]},e.id)})})]})})})})})]})}},8602:(e,r,s)=>{s.d(r,{A:()=>l});var a=s(8139),t=s.n(a),d=s(5043),n=s(7852),c=s(579);const i=d.forwardRef((e,r)=>{const[{className:s,...a},{as:d="div",bsPrefix:i,spans:l}]=function(e){let{as:r,bsPrefix:s,className:a,...d}=e;s=(0,n.oU)(s,"col");const c=(0,n.gy)(),i=(0,n.Jm)(),l=[],o=[];return c.forEach(e=>{const r=d[e];let a,t,n;delete d[e],"object"===typeof r&&null!=r?({span:a,offset:t,order:n}=r):a=r;const c=e!==i?`-${e}`:"";a&&l.push(!0===a?`${s}${c}`:`${s}${c}-${a}`),null!=n&&o.push(`order${c}-${n}`),null!=t&&o.push(`offset${c}-${t}`)}),[{...d,className:t()(a,...l,...o)},{as:r,bsPrefix:s,spans:l}]}(e);return(0,c.jsx)(d,{...a,ref:r,className:t()(s,!l.length&&i)})});i.displayName="Col";const l=i},8628:(e,r,s)=>{s.d(r,{A:()=>C});var a=s(8139),t=s.n(a),d=s(5043),n=s(7852),c=s(579);const i=d.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:d="div",...i}=e;return a=(0,n.oU)(a,"card-body"),(0,c.jsx)(d,{ref:r,className:t()(s,a),...i})});i.displayName="CardBody";const l=i,o=d.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:d="div",...i}=e;return a=(0,n.oU)(a,"card-footer"),(0,c.jsx)(d,{ref:r,className:t()(s,a),...i})});o.displayName="CardFooter";const x=o;var f=s(1778);const h=d.forwardRef((e,r)=>{let{bsPrefix:s,className:a,as:i="div",...l}=e;const o=(0,n.oU)(s,"card-header"),x=(0,d.useMemo)(()=>({cardHeaderBsPrefix:o}),[o]);return(0,c.jsx)(f.A.Provider,{value:x,children:(0,c.jsx)(i,{ref:r,...l,className:t()(a,o)})})});h.displayName="CardHeader";const u=h,p=d.forwardRef((e,r)=>{let{bsPrefix:s,className:a,variant:d,as:i="img",...l}=e;const o=(0,n.oU)(s,"card-img");return(0,c.jsx)(i,{ref:r,className:t()(d?`${o}-${d}`:o,a),...l})});p.displayName="CardImg";const m=p,j=d.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:d="div",...i}=e;return a=(0,n.oU)(a,"card-img-overlay"),(0,c.jsx)(d,{ref:r,className:t()(s,a),...i})});j.displayName="CardImgOverlay";const v=j,y=d.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:d="a",...i}=e;return a=(0,n.oU)(a,"card-link"),(0,c.jsx)(d,{ref:r,className:t()(s,a),...i})});y.displayName="CardLink";const b=y;var N=s(4488);const g=(0,N.A)("h6"),_=d.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:d=g,...i}=e;return a=(0,n.oU)(a,"card-subtitle"),(0,c.jsx)(d,{ref:r,className:t()(s,a),...i})});_.displayName="CardSubtitle";const w=_,$=d.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:d="p",...i}=e;return a=(0,n.oU)(a,"card-text"),(0,c.jsx)(d,{ref:r,className:t()(s,a),...i})});$.displayName="CardText";const A=$,P=(0,N.A)("h5"),R=d.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:d=P,...i}=e;return a=(0,n.oU)(a,"card-title"),(0,c.jsx)(d,{ref:r,className:t()(s,a),...i})});R.displayName="CardTitle";const U=R,k=d.forwardRef((e,r)=>{let{bsPrefix:s,className:a,bg:d,text:i,border:o,body:x=!1,children:f,as:h="div",...u}=e;const p=(0,n.oU)(s,"card");return(0,c.jsx)(h,{ref:r,...u,className:t()(a,p,d&&`bg-${d}`,i&&`text-${i}`,o&&`border-${o}`),children:x?(0,c.jsx)(l,{children:f}):f})});k.displayName="Card";const C=Object.assign(k,{Img:m,Title:U,Subtitle:w,Body:l,Link:b,Text:A,Header:u,Footer:x,ImgOverlay:v})}}]);
//# sourceMappingURL=453.a19caa15.chunk.js.map