"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[511],{511:(e,s,r)=>{r.r(s),r.d(s,{default:()=>h});var a=r(5043),t=r(3519),l=r(1072),d=r(8602),n=r(8628),o=r(4196),c=r(4312),i=r(4117),f=r(579);const h=()=>{const{t:e}=(0,i.Bd)(),[s,r]=(0,a.useState)([]),[h,x]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{(async()=>{const e=(0,c.b)();if(!e)return;x(!0);const{data:{user:s}}=await e.auth.getUser();if(!s)return void x(!1);const{data:a,error:t}=await e.from("network_stats").select("\n                    stat_date,\n                    blockchain_height,\n                    fil_per_tib\n                ").order("stat_date",{ascending:!1});t?console.error("Error fetching network stats:",t):r(a),x(!1)})()},[]),h?(0,f.jsx)("div",{children:e("loading_network_stats")}):(0,f.jsxs)(t.A,{children:[(0,f.jsx)("h2",{className:"mb-4",children:e("network_stats")}),(0,f.jsx)(l.A,{children:(0,f.jsx)(d.A,{children:(0,f.jsx)(n.A,{children:(0,f.jsx)(n.A.Body,{children:(0,f.jsxs)(o.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,f.jsx)("thead",{children:(0,f.jsxs)("tr",{children:[(0,f.jsx)("th",{children:e("stat_date")}),(0,f.jsx)("th",{children:e("blockchain_height")}),(0,f.jsx)("th",{children:e("fil_per_tib")})]})}),(0,f.jsx)("tbody",{children:0===s.length?(0,f.jsx)("tr",{children:(0,f.jsx)("td",{colSpan:"3",className:"text-center",children:e("no_network_stats_available")})}):s.map((e,s)=>(0,f.jsxs)("tr",{children:[(0,f.jsx)("td",{children:new Date(e.stat_date).toLocaleDateString()}),(0,f.jsx)("td",{children:e.blockchain_height?e.blockchain_height.toLocaleString():"-"}),(0,f.jsxs)("td",{children:[e.fil_per_tib?Number(e.fil_per_tib).toFixed(8):"0"," FIL/TiB"]})]},`${e.stat_date}`))})]})})})})})]})}},1072:(e,s,r)=>{r.d(s,{A:()=>c});var a=r(8139),t=r.n(a),l=r(5043),d=r(7852),n=r(579);const o=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:l="div",...o}=e;const c=(0,d.oU)(r,"row"),i=(0,d.gy)(),f=(0,d.Jm)(),h=`${c}-cols`,x=[];return i.forEach(e=>{const s=o[e];let r;delete o[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const a=e!==f?`-${e}`:"";null!=r&&x.push(`${h}${a}-${r}`)}),(0,n.jsx)(l,{ref:s,...o,className:t()(a,c,...x)})});o.displayName="Row";const c=o},4196:(e,s,r)=>{r.d(s,{A:()=>c});var a=r(8139),t=r.n(a),l=r(5043),d=r(7852),n=r(579);const o=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,striped:l,bordered:o,borderless:c,hover:i,size:f,variant:h,responsive:x,...m}=e;const b=(0,d.oU)(r,"table"),N=t()(a,b,h&&`${b}-${h}`,f&&`${b}-${f}`,l&&`${b}-${"string"===typeof l?`striped-${l}`:"striped"}`,o&&`${b}-bordered`,c&&`${b}-borderless`,i&&`${b}-hover`),u=(0,n.jsx)("table",{...m,className:N,ref:s});if(x){let e=`${b}-responsive`;return"string"===typeof x&&(e=`${e}-${x}`),(0,n.jsx)("div",{className:e,children:u})}return u});o.displayName="Table";const c=o},8602:(e,s,r)=>{r.d(s,{A:()=>c});var a=r(8139),t=r.n(a),l=r(5043),d=r(7852),n=r(579);const o=l.forwardRef((e,s)=>{const[{className:r,...a},{as:l="div",bsPrefix:o,spans:c}]=function(e){let{as:s,bsPrefix:r,className:a,...l}=e;r=(0,d.oU)(r,"col");const n=(0,d.gy)(),o=(0,d.Jm)(),c=[],i=[];return n.forEach(e=>{const s=l[e];let a,t,d;delete l[e],"object"===typeof s&&null!=s?({span:a,offset:t,order:d}=s):a=s;const n=e!==o?`-${e}`:"";a&&c.push(!0===a?`${r}${n}`:`${r}${n}-${a}`),null!=d&&i.push(`order${n}-${d}`),null!=t&&i.push(`offset${n}-${t}`)}),[{...l,className:t()(a,...c,...i)},{as:s,bsPrefix:r,spans:c}]}(e);return(0,n.jsx)(l,{...a,ref:s,className:t()(r,!c.length&&o)})});o.displayName="Col";const c=o},8628:(e,s,r)=>{r.d(s,{A:()=>C});var a=r(8139),t=r.n(a),l=r(5043),d=r(7852),n=r(579);const o=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="div",...o}=e;return a=(0,d.oU)(a,"card-body"),(0,n.jsx)(l,{ref:s,className:t()(r,a),...o})});o.displayName="CardBody";const c=o,i=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="div",...o}=e;return a=(0,d.oU)(a,"card-footer"),(0,n.jsx)(l,{ref:s,className:t()(r,a),...o})});i.displayName="CardFooter";const f=i;var h=r(1778);const x=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:o="div",...c}=e;const i=(0,d.oU)(r,"card-header"),f=(0,l.useMemo)(()=>({cardHeaderBsPrefix:i}),[i]);return(0,n.jsx)(h.A.Provider,{value:f,children:(0,n.jsx)(o,{ref:s,...c,className:t()(a,i)})})});x.displayName="CardHeader";const m=x,b=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,variant:l,as:o="img",...c}=e;const i=(0,d.oU)(r,"card-img");return(0,n.jsx)(o,{ref:s,className:t()(l?`${i}-${l}`:i,a),...c})});b.displayName="CardImg";const N=b,u=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="div",...o}=e;return a=(0,d.oU)(a,"card-img-overlay"),(0,n.jsx)(l,{ref:s,className:t()(r,a),...o})});u.displayName="CardImgOverlay";const p=u,j=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="a",...o}=e;return a=(0,d.oU)(a,"card-link"),(0,n.jsx)(l,{ref:s,className:t()(r,a),...o})});j.displayName="CardLink";const $=j;var v=r(4488);const y=(0,v.A)("h6"),g=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l=y,...o}=e;return a=(0,d.oU)(a,"card-subtitle"),(0,n.jsx)(l,{ref:s,className:t()(r,a),...o})});g.displayName="CardSubtitle";const w=g,_=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="p",...o}=e;return a=(0,d.oU)(a,"card-text"),(0,n.jsx)(l,{ref:s,className:t()(r,a),...o})});_.displayName="CardText";const P=_,k=(0,v.A)("h5"),R=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l=k,...o}=e;return a=(0,d.oU)(a,"card-title"),(0,n.jsx)(l,{ref:s,className:t()(r,a),...o})});R.displayName="CardTitle";const U=R,A=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,bg:l,text:o,border:i,body:f=!1,children:h,as:x="div",...m}=e;const b=(0,d.oU)(r,"card");return(0,n.jsx)(x,{ref:s,...m,className:t()(a,b,l&&`bg-${l}`,o&&`text-${o}`,i&&`border-${i}`),children:f?(0,n.jsx)(c,{children:h}):h})});A.displayName="Card";const C=Object.assign(A,{Img:N,Title:U,Subtitle:w,Body:c,Link:$,Text:P,Header:m,Footer:f,ImgOverlay:p})}}]);
//# sourceMappingURL=511.77e44c43.chunk.js.map