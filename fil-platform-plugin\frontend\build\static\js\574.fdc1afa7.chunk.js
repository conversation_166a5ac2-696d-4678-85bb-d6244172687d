"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[574],{1072:(e,s,r)=>{r.d(s,{A:()=>c});var a=r(8139),t=r.n(a),n=r(5043),l=r(7852),d=r(579);const i=n.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:n="div",...i}=e;const c=(0,l.oU)(r,"row"),o=(0,l.gy)(),f=(0,l.Jm)(),h=`${c}-cols`,m=[];return o.forEach(e=>{const s=i[e];let r;delete i[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const a=e!==f?`-${e}`:"";null!=r&&m.push(`${h}${a}-${r}`)}),(0,d.jsx)(n,{ref:s,...i,className:t()(a,c,...m)})});i.displayName="Row";const c=i},1574:(e,s,r)=>{r.r(s),r.d(s,{default:()=>h});var a=r(5043),t=r(3519),n=r(1072),l=r(8602),d=r(8628),i=r(4196),c=r(4312),o=r(4117),f=r(579);const h=()=>{const{t:e}=(0,o.Bd)(),[s,r]=(0,a.useState)([]),[h,m]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{(async()=>{const e=(0,c.b)();if(!e)return;m(!0);const{data:{user:s}}=await e.auth.getUser();if(!s)return void m(!1);const{data:a,error:t}=await e.from("miner_daily_snapshots").select("\n                    miner_id,\n                    snapshot_date,\n                    blockchain_height,\n                    power,\n                    available_balance,\n                    pledge_locked,\n                    balance,\n                    miners (\n                        filecoin_miner_id,\n                        category,\n                        facilities (\n                            name\n                        )\n                    )\n                ").order("snapshot_date",{ascending:!1});t?console.error("Error fetching miner snapshots:",t):r(a),m(!1)})()},[]),h?(0,f.jsx)("div",{children:e("loading_snapshots")}):(0,f.jsxs)(t.A,{children:[(0,f.jsx)("h2",{className:"mb-4",children:e("miner_snapshots")}),(0,f.jsx)(n.A,{children:(0,f.jsx)(l.A,{children:(0,f.jsx)(d.A,{children:(0,f.jsx)(d.A.Body,{children:(0,f.jsxs)(i.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,f.jsx)("thead",{children:(0,f.jsxs)("tr",{children:[(0,f.jsx)("th",{children:e("miner_id")}),(0,f.jsx)("th",{children:e("facility")}),(0,f.jsx)("th",{children:e("snapshot_date")}),(0,f.jsx)("th",{children:e("blockchain_height")}),(0,f.jsx)("th",{children:e("power")}),(0,f.jsx)("th",{children:e("available_balance")}),(0,f.jsx)("th",{children:e("pledge_locked")}),(0,f.jsx)("th",{children:e("balance")})]})}),(0,f.jsx)("tbody",{children:0===s.length?(0,f.jsx)("tr",{children:(0,f.jsx)("td",{colSpan:"8",className:"text-center",children:e("no_snapshots_available")})}):s.map((e,s)=>{var r,a,t;return(0,f.jsxs)("tr",{children:[(0,f.jsx)("td",{children:(null===(r=e.miners)||void 0===r?void 0:r.filecoin_miner_id)||"-"}),(0,f.jsx)("td",{children:(null===(a=e.miners)||void 0===a||null===(t=a.facilities)||void 0===t?void 0:t.name)||"-"}),(0,f.jsx)("td",{children:new Date(e.snapshot_date).toLocaleDateString()}),(0,f.jsx)("td",{children:e.blockchain_height||"-"}),(0,f.jsxs)("td",{children:[e.power?Number(e.power).toFixed(2):"0"," TiB"]}),(0,f.jsxs)("td",{children:[e.available_balance?Number(e.available_balance).toFixed(6):"0"," FIL"]}),(0,f.jsxs)("td",{children:[e.pledge_locked?Number(e.pledge_locked).toFixed(6):"0"," FIL"]}),(0,f.jsxs)("td",{children:[e.balance?Number(e.balance).toFixed(6):"0"," FIL"]})]},`${e.miner_id}-${e.snapshot_date}`)})})]})})})})})]})}},4196:(e,s,r)=>{r.d(s,{A:()=>c});var a=r(8139),t=r.n(a),n=r(5043),l=r(7852),d=r(579);const i=n.forwardRef((e,s)=>{let{bsPrefix:r,className:a,striped:n,bordered:i,borderless:c,hover:o,size:f,variant:h,responsive:m,...x}=e;const b=(0,l.oU)(r,"table"),p=t()(a,b,h&&`${b}-${h}`,f&&`${b}-${f}`,n&&`${b}-${"string"===typeof n?`striped-${n}`:"striped"}`,i&&`${b}-bordered`,c&&`${b}-borderless`,o&&`${b}-hover`),u=(0,d.jsx)("table",{...x,className:p,ref:s});if(m){let e=`${b}-responsive`;return"string"===typeof m&&(e=`${e}-${m}`),(0,d.jsx)("div",{className:e,children:u})}return u});i.displayName="Table";const c=i},8602:(e,s,r)=>{r.d(s,{A:()=>c});var a=r(8139),t=r.n(a),n=r(5043),l=r(7852),d=r(579);const i=n.forwardRef((e,s)=>{const[{className:r,...a},{as:n="div",bsPrefix:i,spans:c}]=function(e){let{as:s,bsPrefix:r,className:a,...n}=e;r=(0,l.oU)(r,"col");const d=(0,l.gy)(),i=(0,l.Jm)(),c=[],o=[];return d.forEach(e=>{const s=n[e];let a,t,l;delete n[e],"object"===typeof s&&null!=s?({span:a,offset:t,order:l}=s):a=s;const d=e!==i?`-${e}`:"";a&&c.push(!0===a?`${r}${d}`:`${r}${d}-${a}`),null!=l&&o.push(`order${d}-${l}`),null!=t&&o.push(`offset${d}-${t}`)}),[{...n,className:t()(a,...c,...o)},{as:s,bsPrefix:r,spans:c}]}(e);return(0,d.jsx)(n,{...a,ref:s,className:t()(r,!c.length&&i)})});i.displayName="Col";const c=i},8628:(e,s,r)=>{r.d(s,{A:()=>C});var a=r(8139),t=r.n(a),n=r(5043),l=r(7852),d=r(579);const i=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n="div",...i}=e;return a=(0,l.oU)(a,"card-body"),(0,d.jsx)(n,{ref:s,className:t()(r,a),...i})});i.displayName="CardBody";const c=i,o=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n="div",...i}=e;return a=(0,l.oU)(a,"card-footer"),(0,d.jsx)(n,{ref:s,className:t()(r,a),...i})});o.displayName="CardFooter";const f=o;var h=r(1778);const m=n.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:i="div",...c}=e;const o=(0,l.oU)(r,"card-header"),f=(0,n.useMemo)(()=>({cardHeaderBsPrefix:o}),[o]);return(0,d.jsx)(h.A.Provider,{value:f,children:(0,d.jsx)(i,{ref:s,...c,className:t()(a,o)})})});m.displayName="CardHeader";const x=m,b=n.forwardRef((e,s)=>{let{bsPrefix:r,className:a,variant:n,as:i="img",...c}=e;const o=(0,l.oU)(r,"card-img");return(0,d.jsx)(i,{ref:s,className:t()(n?`${o}-${n}`:o,a),...c})});b.displayName="CardImg";const p=b,u=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n="div",...i}=e;return a=(0,l.oU)(a,"card-img-overlay"),(0,d.jsx)(n,{ref:s,className:t()(r,a),...i})});u.displayName="CardImgOverlay";const j=u,N=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n="a",...i}=e;return a=(0,l.oU)(a,"card-link"),(0,d.jsx)(n,{ref:s,className:t()(r,a),...i})});N.displayName="CardLink";const v=N;var $=r(4488);const y=(0,$.A)("h6"),g=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n=y,...i}=e;return a=(0,l.oU)(a,"card-subtitle"),(0,d.jsx)(n,{ref:s,className:t()(r,a),...i})});g.displayName="CardSubtitle";const _=g,w=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n="p",...i}=e;return a=(0,l.oU)(a,"card-text"),(0,d.jsx)(n,{ref:s,className:t()(r,a),...i})});w.displayName="CardText";const P=w,k=(0,$.A)("h5"),R=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n=k,...i}=e;return a=(0,l.oU)(a,"card-title"),(0,d.jsx)(n,{ref:s,className:t()(r,a),...i})});R.displayName="CardTitle";const U=R,A=n.forwardRef((e,s)=>{let{bsPrefix:r,className:a,bg:n,text:i,border:o,body:f=!1,children:h,as:m="div",...x}=e;const b=(0,l.oU)(r,"card");return(0,d.jsx)(m,{ref:s,...x,className:t()(a,b,n&&`bg-${n}`,i&&`text-${i}`,o&&`border-${o}`),children:f?(0,d.jsx)(c,{children:h}):h})});A.displayName="Card";const C=Object.assign(A,{Img:p,Title:U,Subtitle:_,Body:c,Link:v,Text:P,Header:x,Footer:f,ImgOverlay:j})}}]);
//# sourceMappingURL=574.fdc1afa7.chunk.js.map