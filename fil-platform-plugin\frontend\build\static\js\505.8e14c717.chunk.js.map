{"version": 3, "file": "static/js/505.8e14c717.chunk.js", "mappings": "uOAMA,MAyPA,EAzPuBA,KACnB,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAAcC,IAAmBC,EAAAA,EAAAA,UAAS,OAC1CC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCG,EAAgBC,IAAqBJ,EAAAA,EAAAA,UAAS,CACjDK,oBAAqB,EACrBC,mBAAoB,EACpBC,YAAa,EACbC,aAAc,EACdC,YAAa,EACbC,iBAAkB,IAmHtB,OA3CAC,EAAAA,EAAAA,WAAU,KACiBC,WACnB,MAAMC,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EAAU,OAEfX,GAAW,GACX,MAAQa,MAAM,KAAEC,UAAiBH,EAASI,KAAKC,UAE/C,IAAKF,EAGD,OAFAG,QAAQC,IAAI,sBACZlB,GAAW,GAIfiB,QAAQC,IAAI,gBAAiBJ,EAAKK,IAClCF,QAAQC,IAAI,+BAAgCE,aAAaC,QAAQ,cAGjE,MAAQR,KAAMS,EAAaC,MAAOC,SAAuBb,EACpDc,KAAK,kBACLC,OAAO,KACPC,GAAG,UAAWb,EAAKK,IACnBS,SAEL,GAAIJ,EAKA,OAJAP,QAAQM,MAAM,gCAAiCC,GAC/CP,QAAQM,MAAM,WAAYT,EAAKK,IAC/BF,QAAQM,MAAM,iBAAkBC,QAChCxB,GAAW,GAIfiB,QAAQC,IAAI,sBAAuBI,GACnCzB,EAAgByB,QAtGIZ,OAAOC,EAAUkB,EAAaC,KACtD,IAEI,MAAQjB,KAAMkB,EAAYR,MAAOS,SAAsBrB,EAClDc,KAAK,qBACLC,OAAO,UAAW,CAAEO,MAAO,UAC3BN,GAAG,WAAYE,IAGZhB,KAAMqB,EAAWX,MAAOY,SAAqBxB,EAChDc,KAAK,UACLC,OAAO,udAePC,GAAG,WAAYE,IAGZhB,KAAMuB,EAAab,MAAOc,SAAuB1B,EACpDc,KAAK,YACLC,OAAO,6BACPC,GAAG,WAAYG,GAEpB,IAAIQ,EAAQ,CACRnC,oBAAqB,EACrBC,mBAAoB,EACpBC,YAAa0B,EAAaA,EAAWQ,OAAS,EAC9CjC,aAAc,EACdC,YAAa,EACbC,iBAAkB,GAIlB0B,IAAcC,GACdD,EAAUM,QAAQC,IACd,MAAMC,GAAkBD,EAAME,cAAgB,IAAMF,EAAMG,cAAgB,GAAK,IACzEC,GAAiBJ,EAAME,cAAgB,IAAMF,EAAMK,aAAe,GAAK,IAC7ER,EAAMnC,qBAAuBuC,EAC7BJ,EAAMlC,oBAAsByC,IAKhCT,IAAgBC,IAChBD,EAAYI,QAAQO,IAChBT,EAAMhC,cAAgByC,EAAQC,cAAgB,EAC9CV,EAAM/B,aAAewC,EAAQE,aAAe,IAEhDX,EAAM9B,iBAAmB8B,EAAMhC,aAAegC,EAAM/B,aAGxDL,EAAkBoC,EAEtB,CAAE,MAAOf,GACLN,QAAQM,MAAM,kCAAmCA,EACrD,GAuCU2B,CAAoBvC,EAAUG,EAAKK,GAAIG,EAAY6B,UACzDnD,GAAW,IAGfoD,IACD,IAECrD,GACOsD,EAAAA,EAAAA,KAAA,OAAAC,SAAM5D,EAAE,6BAGdE,GAKD2D,EAAAA,EAAAA,MAACC,EAAAA,EAAS,CAACC,OAAK,EAAAH,SAAA,EACZD,EAAAA,EAAAA,KAACK,EAAAA,EAAG,CAACC,UAAU,OAAML,UACjBD,EAAAA,EAAAA,KAACO,EAAAA,EAAG,CAAAN,UACAD,EAAAA,EAAAA,KAAA,MAAAC,SAAK5D,EAAE,0BAKf6D,EAAAA,EAAAA,MAACG,EAAAA,EAAG,CAAAJ,SAAA,EACAD,EAAAA,EAAAA,KAACO,EAAAA,EAAG,CAACC,GAAI,EAAEP,UACPD,EAAAA,EAAAA,KAACS,EAAAA,EAAI,CAACH,UAAU,OAAML,UAClBC,EAAAA,EAAAA,MAACO,EAAAA,EAAKC,KAAI,CAAAT,SAAA,EACND,EAAAA,EAAAA,KAACS,EAAAA,EAAKE,MAAK,CAAAV,SAAE5D,EAAE,iBACf2D,EAAAA,EAAAA,KAAA,MAAAC,SAAK1D,EAAaqE,YAAc,gBAI5CZ,EAAAA,EAAAA,KAACO,EAAAA,EAAG,CAACC,GAAI,EAAEP,UACPD,EAAAA,EAAAA,KAACS,EAAAA,EAAI,CAACH,UAAU,OAAML,UAClBC,EAAAA,EAAAA,MAACO,EAAAA,EAAKC,KAAI,CAAAT,SAAA,EACND,EAAAA,EAAAA,KAACS,EAAAA,EAAKE,MAAK,CAAAV,SAAE5D,EAAE,sBACf2D,EAAAA,EAAAA,KAAA,MAAAC,SAAK1D,EAAasE,eAAkD,IAA9BtE,EAAasE,eAAhB,IAA0C,gBAIzFb,EAAAA,EAAAA,KAACO,EAAAA,EAAG,CAACC,GAAI,EAAEP,UACPD,EAAAA,EAAAA,KAACS,EAAAA,EAAI,CAACH,UAAU,OAAML,UAClBC,EAAAA,EAAAA,MAACO,EAAAA,EAAKC,KAAI,CAAAT,SAAA,EACND,EAAAA,EAAAA,KAACS,EAAAA,EAAKE,MAAK,CAAAV,SAAE5D,EAAE,iBACf2D,EAAAA,EAAAA,KAAA,MAAAC,SAAK1D,EAAauE,YAAc,mBAOhDZ,EAAAA,EAAAA,MAACG,EAAAA,EAAG,CAAAJ,SAAA,EACAD,EAAAA,EAAAA,KAACO,EAAAA,EAAG,CAACC,GAAI,EAAEP,UACPD,EAAAA,EAAAA,KAACS,EAAAA,EAAI,CAACH,UAAU,OAAML,UAClBC,EAAAA,EAAAA,MAACO,EAAAA,EAAKC,KAAI,CAAAT,SAAA,EACND,EAAAA,EAAAA,KAACS,EAAAA,EAAKE,MAAK,CAAAV,SAAE5D,EAAE,4BACf6D,EAAAA,EAAAA,MAAA,MAAAD,SAAA,CAAKrD,EAAeE,oBAAoBiE,QAAQ,GAAG,kBAI/Df,EAAAA,EAAAA,KAACO,EAAAA,EAAG,CAACC,GAAI,EAAEP,UACPD,EAAAA,EAAAA,KAACS,EAAAA,EAAI,CAACH,UAAU,OAAML,UAClBC,EAAAA,EAAAA,MAACO,EAAAA,EAAKC,KAAI,CAAAT,SAAA,EACND,EAAAA,EAAAA,KAACS,EAAAA,EAAKE,MAAK,CAAAV,SAAE5D,EAAE,2BACf6D,EAAAA,EAAAA,MAAA,MAAAD,SAAA,CAAKrD,EAAeG,mBAAmBgE,QAAQ,GAAG,qBAOlEb,EAAAA,EAAAA,MAACG,EAAAA,EAAG,CAAAJ,SAAA,EACAD,EAAAA,EAAAA,KAACO,EAAAA,EAAG,CAACC,GAAI,EAAEP,UACPD,EAAAA,EAAAA,KAACS,EAAAA,EAAI,CAACH,UAAU,OAAML,UAClBC,EAAAA,EAAAA,MAACO,EAAAA,EAAKC,KAAI,CAAAT,SAAA,EACND,EAAAA,EAAAA,KAACS,EAAAA,EAAKE,MAAK,CAAAV,SAAE5D,EAAE,mBACf2D,EAAAA,EAAAA,KAAA,MAAAC,SAAKrD,EAAeI,sBAIhCgD,EAAAA,EAAAA,KAACO,EAAAA,EAAG,CAACC,GAAI,EAAEP,UACPD,EAAAA,EAAAA,KAACS,EAAAA,EAAI,CAACH,UAAU,OAAML,UAClBC,EAAAA,EAAAA,MAACO,EAAAA,EAAKC,KAAI,CAAAT,SAAA,EACND,EAAAA,EAAAA,KAACS,EAAAA,EAAKE,MAAK,CAAAV,SAAE5D,EAAE,oBACf6D,EAAAA,EAAAA,MAAA,MAAAD,SAAA,CAAKrD,EAAeK,aAAa8D,QAAQ,GAAG,kBAIxDf,EAAAA,EAAAA,KAACO,EAAAA,EAAG,CAACC,GAAI,EAAEP,UACPD,EAAAA,EAAAA,KAACS,EAAAA,EAAI,CAACH,UAAU,OAAML,UAClBC,EAAAA,EAAAA,MAACO,EAAAA,EAAKC,KAAI,CAAAT,SAAA,EACND,EAAAA,EAAAA,KAACS,EAAAA,EAAKE,MAAK,CAAAV,SAAE5D,EAAE,mBACf6D,EAAAA,EAAAA,MAAA,MAAAD,SAAA,CAAKrD,EAAeM,YAAY6D,QAAQ,GAAG,kBAIvDf,EAAAA,EAAAA,KAACO,EAAAA,EAAG,CAACC,GAAI,EAAEP,UACPD,EAAAA,EAAAA,KAACS,EAAAA,EAAI,CAACH,UAAU,iBAAgBL,UAC5BC,EAAAA,EAAAA,MAACO,EAAAA,EAAKC,KAAI,CAAAT,SAAA,EACND,EAAAA,EAAAA,KAACS,EAAAA,EAAKE,MAAK,CAAAV,SAAE5D,EAAE,wBACf6D,EAAAA,EAAAA,MAAA,MAAAD,SAAA,CAAKrD,EAAeO,iBAAiB4D,QAAQ,GAAG,qBAMhEb,EAAAA,EAAAA,MAACG,EAAAA,EAAG,CAACC,UAAU,OAAML,SAAA,EACjBD,EAAAA,EAAAA,KAACO,EAAAA,EAAG,CAACC,GAAI,EAAGF,UAAU,cAAaL,UAC/BD,EAAAA,EAAAA,KAACS,EAAAA,EAAI,CAAAR,UACDC,EAAAA,EAAAA,MAACO,EAAAA,EAAKC,KAAI,CAAAT,SAAA,EACND,EAAAA,EAAAA,KAAA,MAAAC,SAAK5D,EAAE,wBACP2D,EAAAA,EAAAA,KAAA,KAAAC,SAAI5D,EAAE,6BACN2D,EAAAA,EAAAA,KAACgB,EAAAA,EAAM,CAACC,GAAIC,EAAAA,GAAMC,GAAG,qBAAoBlB,SAAE5D,EAAE,+BAIzD2D,EAAAA,EAAAA,KAACO,EAAAA,EAAG,CAACC,GAAI,EAAGF,UAAU,cAAaL,UAC/BD,EAAAA,EAAAA,KAACS,EAAAA,EAAI,CAAAR,UACDC,EAAAA,EAAAA,MAACO,EAAAA,EAAKC,KAAI,CAAAT,SAAA,EACND,EAAAA,EAAAA,KAAA,MAAAC,SAAK5D,EAAE,yBACP2D,EAAAA,EAAAA,KAAA,KAAAC,SAAI5D,EAAE,uBACN2D,EAAAA,EAAAA,KAACgB,EAAAA,EAAM,CAACC,GAAIC,EAAAA,GAAMC,GAAG,kBAAiBlB,SAAE5D,EAAE,yCA9GvD2D,EAAAA,EAAAA,KAAA,OAAKM,UAAU,sBAAqBL,SAAE5D,EAAE,e,sFClIvD,MAAMgE,EAAmBe,EAAAA,WAAiB,CAAAC,EAMvCC,KAAQ,IANgC,SACzCC,EAAQ,UACRjB,EAEAW,GAAIO,EAAY,SACbC,GACJJ,EACC,MAAMK,GAAoBC,EAAAA,EAAAA,IAAmBJ,EAAU,OACjDK,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChBC,EAAa,GAAGN,SAChBO,EAAU,GAehB,OAdAL,EAAYzC,QAAQ+C,IAClB,MAAMC,EAAYV,EAAMS,GAExB,IAAIE,SADGX,EAAMS,GAEI,MAAbC,GAA0C,kBAAdA,IAE5BC,QACED,GAEJC,EAAOD,EAET,MAAME,EAAQH,IAAaJ,EAAgB,IAAII,IAAa,GAChD,MAARE,GAAcH,EAAQK,KAAK,GAAGN,IAAaK,KAASD,QAEtCpC,EAAAA,EAAAA,KAAKwB,EAAW,CAClCF,IAAKA,KACFG,EACHnB,UAAWiC,IAAWjC,EAAWoB,KAAsBO,OAG3D5B,EAAImC,YAAc,MAClB,S,sFCOA,MAAMjC,EAAmBa,EAAAA,WAEzB,CAACK,EAAOH,KACN,OAAO,UACLhB,KACGmC,IAEHxB,GAAIO,EAAY,MAAK,SACrBD,EAAQ,MACRmB,IAjDG,SAAerB,GAKnB,IALoB,GACrBJ,EAAE,SACFM,EAAQ,UACRjB,KACGmB,GACJJ,EACCE,GAAWI,EAAAA,EAAAA,IAAmBJ,EAAU,OACxC,MAAMK,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChBW,EAAQ,GACRT,EAAU,GAqBhB,OApBAL,EAAYzC,QAAQ+C,IAClB,MAAMC,EAAYV,EAAMS,GAExB,IAAIS,EACAC,EACAxD,SAHGqC,EAAMS,GAIY,kBAAdC,GAAuC,MAAbA,IAEjCQ,OACAC,SACAxD,SACE+C,GAEJQ,EAAOR,EAET,MAAME,EAAQH,IAAaJ,EAAgB,IAAII,IAAa,GACxDS,GAAMD,EAAMJ,MAAc,IAATK,EAAgB,GAAGpB,IAAWc,IAAU,GAAGd,IAAWc,KAASM,KACvE,MAATvD,GAAe6C,EAAQK,KAAK,QAAQD,KAASjD,KACnC,MAAVwD,GAAgBX,EAAQK,KAAK,SAASD,KAASO,OAE9C,CAAC,IACHnB,EACHnB,UAAWiC,IAAWjC,KAAcoC,KAAUT,IAC7C,CACDhB,KACAM,WACAmB,SAEJ,CAWOG,CAAOpB,GACZ,OAAoBzB,EAAAA,EAAAA,KAAKwB,EAAW,IAC/BiB,EACHnB,IAAKA,EACLhB,UAAWiC,IAAWjC,GAAYoC,EAAMxD,QAAUqC,OAGtDhB,EAAIiC,YAAc,MAClB,S,sFC1DA,MAAMM,EAAwB1B,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9ChB,EAAS,SACTiB,EACAN,GAAIO,EAAY,SACbC,GACJJ,EAEC,OADAE,GAAWI,EAAAA,EAAAA,IAAmBJ,EAAU,cACpBvB,EAAAA,EAAAA,KAAKwB,EAAW,CAClCF,IAAKA,EACLhB,UAAWiC,IAAWjC,EAAWiB,MAC9BE,MAGPqB,EAASN,YAAc,WACvB,UCdMO,EAA0B3B,EAAAA,WAAiB,CAAAC,EAK9CC,KAAQ,IALuC,UAChDhB,EAAS,SACTiB,EACAN,GAAIO,EAAY,SACbC,GACJJ,EAEC,OADAE,GAAWI,EAAAA,EAAAA,IAAmBJ,EAAU,gBACpBvB,EAAAA,EAAAA,KAAKwB,EAAW,CAClCF,IAAKA,EACLhB,UAAWiC,IAAWjC,EAAWiB,MAC9BE,MAGPsB,EAAWP,YAAc,aACzB,U,cCZA,MAAMQ,EAA0B5B,EAAAA,WAAiB,CAAAC,EAM9CC,KAAQ,IANuC,SAChDC,EAAQ,UACRjB,EAEAW,GAAIO,EAAY,SACbC,GACJJ,EACC,MAAM4B,GAAStB,EAAAA,EAAAA,IAAmBJ,EAAU,eACtC2B,GAAeC,EAAAA,EAAAA,SAAQ,KAAM,CACjCC,mBAAoBH,IAClB,CAACA,IACL,OAAoBjD,EAAAA,EAAAA,KAAKqD,EAAAA,EAAkBC,SAAU,CACnDC,MAAOL,EACPjD,UAAuBD,EAAAA,EAAAA,KAAKwB,EAAW,CACrCF,IAAKA,KACFG,EACHnB,UAAWiC,IAAWjC,EAAW2C,SAIvCD,EAAWR,YAAc,aACzB,UCvBMgB,EAAuBpC,EAAAA,WAE7B,CAAAC,EAMGC,KAAQ,IANV,SACCC,EAAQ,UACRjB,EAAS,QACTmD,EACAxC,GAAIO,EAAY,SACbC,GACJJ,EACC,MAAM4B,GAAStB,EAAAA,EAAAA,IAAmBJ,EAAU,YAC5C,OAAoBvB,EAAAA,EAAAA,KAAKwB,EAAW,CAClCF,IAAKA,EACLhB,UAAWiC,IAAWkB,EAAU,GAAGR,KAAUQ,IAAYR,EAAQ3C,MAC9DmB,MAGP+B,EAAQhB,YAAc,UACtB,UCjBMkB,EAA8BtC,EAAAA,WAAiB,CAAAC,EAKlDC,KAAQ,IAL2C,UACpDhB,EAAS,SACTiB,EACAN,GAAIO,EAAY,SACbC,GACJJ,EAEC,OADAE,GAAWI,EAAAA,EAAAA,IAAmBJ,EAAU,qBACpBvB,EAAAA,EAAAA,KAAKwB,EAAW,CAClCF,IAAKA,EACLhB,UAAWiC,IAAWjC,EAAWiB,MAC9BE,MAGPiC,EAAelB,YAAc,iBAC7B,UCdMmB,EAAwBvC,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9ChB,EAAS,SACTiB,EACAN,GAAIO,EAAY,OACbC,GACJJ,EAEC,OADAE,GAAWI,EAAAA,EAAAA,IAAmBJ,EAAU,cACpBvB,EAAAA,EAAAA,KAAKwB,EAAW,CAClCF,IAAKA,EACLhB,UAAWiC,IAAWjC,EAAWiB,MAC9BE,MAGPkC,EAASnB,YAAc,WACvB,U,cCbA,MAAMoB,GAAgBC,EAAAA,EAAAA,GAAiB,MACjCC,EAA4B1C,EAAAA,WAAiB,CAAAC,EAKhDC,KAAQ,IALyC,UAClDhB,EAAS,SACTiB,EACAN,GAAIO,EAAYoC,KACbnC,GACJJ,EAEC,OADAE,GAAWI,EAAAA,EAAAA,IAAmBJ,EAAU,kBACpBvB,EAAAA,EAAAA,KAAKwB,EAAW,CAClCF,IAAKA,EACLhB,UAAWiC,IAAWjC,EAAWiB,MAC9BE,MAGPqC,EAAatB,YAAc,eAC3B,UChBMuB,EAAwB3C,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9ChB,EAAS,SACTiB,EACAN,GAAIO,EAAY,OACbC,GACJJ,EAEC,OADAE,GAAWI,EAAAA,EAAAA,IAAmBJ,EAAU,cACpBvB,EAAAA,EAAAA,KAAKwB,EAAW,CAClCF,IAAKA,EACLhB,UAAWiC,IAAWjC,EAAWiB,MAC9BE,MAGPsC,EAASvB,YAAc,WACvB,UCbMwB,GAAgBH,EAAAA,EAAAA,GAAiB,MACjCI,EAAyB7C,EAAAA,WAAiB,CAAAC,EAK7CC,KAAQ,IALsC,UAC/ChB,EAAS,SACTiB,EACAN,GAAIO,EAAYwC,KACbvC,GACJJ,EAEC,OADAE,GAAWI,EAAAA,EAAAA,IAAmBJ,EAAU,eACpBvB,EAAAA,EAAAA,KAAKwB,EAAW,CAClCF,IAAKA,EACLhB,UAAWiC,IAAWjC,EAAWiB,MAC9BE,MAGPwC,EAAUzB,YAAc,YACxB,UCPM/B,EAAoBW,EAAAA,WAAiB,CAAAC,EAWxCC,KAAQ,IAXiC,SAC1CC,EAAQ,UACRjB,EAAS,GACT4D,EAAE,KACFC,EAAI,OACJC,EAAM,KACNC,GAAO,EAAK,SACZpE,EAEAgB,GAAIO,EAAY,SACbC,GACJJ,EACC,MAAM4B,GAAStB,EAAAA,EAAAA,IAAmBJ,EAAU,QAC5C,OAAoBvB,EAAAA,EAAAA,KAAKwB,EAAW,CAClCF,IAAKA,KACFG,EACHnB,UAAWiC,IAAWjC,EAAW2C,EAAQiB,GAAM,MAAMA,IAAMC,GAAQ,QAAQA,IAAQC,GAAU,UAAUA,KACvGnE,SAAUoE,GAAoBrE,EAAAA,EAAAA,KAAK8C,EAAU,CAC3C7C,SAAUA,IACPA,MAGTQ,EAAK+B,YAAc,OACnB,QAAe8B,OAAOC,OAAO9D,EAAM,CACjC+D,IAAKhB,EACL7C,MAAOsD,EACPQ,SAAUX,EACVpD,KAAMoC,EACN5B,KAAMyC,EACNe,KAAMX,EACNY,OAAQ3B,EACR4B,OAAQ7B,EACR8B,WAAYnB,G", "sources": ["pages/agent/Dashboard.js", "../node_modules/react-bootstrap/esm/Row.js", "../node_modules/react-bootstrap/esm/Col.js", "../node_modules/react-bootstrap/esm/CardBody.js", "../node_modules/react-bootstrap/esm/CardFooter.js", "../node_modules/react-bootstrap/esm/CardHeader.js", "../node_modules/react-bootstrap/esm/CardImg.js", "../node_modules/react-bootstrap/esm/CardImgOverlay.js", "../node_modules/react-bootstrap/esm/CardLink.js", "../node_modules/react-bootstrap/esm/CardSubtitle.js", "../node_modules/react-bootstrap/esm/CardText.js", "../node_modules/react-bootstrap/esm/CardTitle.js", "../node_modules/react-bootstrap/esm/Card.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button } from 'react-bootstrap';\nimport { useTranslation } from 'react-i18next';\nimport { Link } from 'react-router-dom';\nimport { getSupabase } from '../../supabaseClient';\n\nconst AgentDashboard = () => {\n    const { t } = useTranslation();\n    const [agentProfile, setAgentProfile] = useState(null);\n    const [loading, setLoading] = useState(true);\n    const [dashboardStats, setDashboardStats] = useState({\n        totalTechCommission: 0,\n        totalOpsCommission: 0,\n        memberCount: 0,\n        totalStorage: 0,\n        soldStorage: 0,\n        remainingStorage: 0\n    });\n\n    const fetchDashboardStats = async (supabase, agentUserId, makerId) => {\n        try {\n            // 1. 获取会员总数 (customers under this agent)\n            const { data: memberData, error: memberError } = await supabase\n                .from('customer_profiles')\n                .select('user_id', { count: 'exact' })\n                .eq('agent_id', agentUserId);\n\n            // 2. 获取该agent的所有订单及相关产品信息\n            const { data: orderData, error: orderError } = await supabase\n                .from('orders')\n                .select(`\n                    id,\n                    shares,\n                    storage_cost,\n                    pledge_cost,\n                    tech_fee_pct,\n                    ops_fee_pct,\n                    products (\n                        total_shares,\n                        sold_shares,\n                        tech_commission_pct,\n                        ops_commission_pct,\n                        maker_id\n                    )\n                `)\n                .eq('agent_id', agentUserId);\n\n            // 3. 获取该maker的所有产品信息（用于计算总存储）\n            const { data: productData, error: productError } = await supabase\n                .from('products')\n                .select('total_shares, sold_shares')\n                .eq('maker_id', makerId);\n\n            let stats = {\n                totalTechCommission: 0,\n                totalOpsCommission: 0,\n                memberCount: memberData ? memberData.length : 0,\n                totalStorage: 0,\n                soldStorage: 0,\n                remainingStorage: 0\n            };\n\n            // 计算技术佣金和运营佣金\n            if (orderData && !orderError) {\n                orderData.forEach(order => {\n                    const techCommission = (order.storage_cost || 0) * (order.tech_fee_pct || 0) / 100;\n                    const opsCommission = (order.storage_cost || 0) * (order.ops_fee_pct || 0) / 100;\n                    stats.totalTechCommission += techCommission;\n                    stats.totalOpsCommission += opsCommission;\n                });\n            }\n\n            // 计算存储统计\n            if (productData && !productError) {\n                productData.forEach(product => {\n                    stats.totalStorage += product.total_shares || 0;\n                    stats.soldStorage += product.sold_shares || 0;\n                });\n                stats.remainingStorage = stats.totalStorage - stats.soldStorage;\n            }\n\n            setDashboardStats(stats);\n\n        } catch (error) {\n            console.error('Error fetching dashboard stats:', error);\n        }\n    };\n\n    useEffect(() => {\n        const fetchAgentData = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                console.log('No user found');\n                setLoading(false);\n                return; // User not logged in\n            }\n\n            console.log('Current user:', user.id);\n            console.log('User role from localStorage:', localStorage.getItem('user_role'));\n\n            // Fetch agent profile\n            const { data: profileData, error: profileError } = await supabase\n                .from('agent_profiles')\n                .select('*')\n                .eq('user_id', user.id)\n                .single();\n\n            if (profileError) {\n                console.error('Error fetching agent profile:', profileError);\n                console.error('User ID:', user.id);\n                console.error('Error details:', profileError);\n                setLoading(false);\n                return;\n            }\n\n            console.log('Agent profile data:', profileData);\n            setAgentProfile(profileData);\n\n            // Fetch dashboard statistics\n            await fetchDashboardStats(supabase, user.id, profileData.maker_id);\n            setLoading(false);\n        };\n\n        fetchAgentData();\n    }, []);\n\n    if (loading) {\n        return <div>{t('loading_agent_dashboard')}</div>;\n    }\n\n    if (!agentProfile) {\n        return <div className=\"alert alert-warning\">{t('not_agent')}</div>;\n    }\n\n    return (\n        <Container fluid>\n            <Row className=\"mb-3\">\n                <Col>\n                    <h2>{t('agent_dashboard')}</h2>\n                </Col>\n            </Row>\n\n            {/* 第一行：基本信息 */}\n            <Row>\n                <Col md={4}>\n                    <Card className=\"mb-3\">\n                        <Card.Body>\n                            <Card.Title>{t('brand_name')}</Card.Title>\n                            <h3>{agentProfile.brand_name || 'N/A'}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={4}>\n                    <Card className=\"mb-3\">\n                        <Card.Body>\n                            <Card.Title>{t('commission_rate')}</Card.Title>\n                            <h3>{agentProfile.commission_pct ? `${agentProfile.commission_pct * 100}%` : 'N/A'}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={4}>\n                    <Card className=\"mb-3\">\n                        <Card.Body>\n                            <Card.Title>{t('kyc_status')}</Card.Title>\n                            <h3>{agentProfile.kyc_status || 'N/A'}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            {/* 第二行：佣金统计 */}\n            <Row>\n                <Col md={6}>\n                    <Card className=\"mb-3\">\n                        <Card.Body>\n                            <Card.Title>{t('total_tech_commission')}</Card.Title>\n                            <h3>{dashboardStats.totalTechCommission.toFixed(6)} FIL</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={6}>\n                    <Card className=\"mb-3\">\n                        <Card.Body>\n                            <Card.Title>{t('total_ops_commission')}</Card.Title>\n                            <h3>{dashboardStats.totalOpsCommission.toFixed(6)} FIL</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            {/* 第三行：会员和存储统计 */}\n            <Row>\n                <Col md={3}>\n                    <Card className=\"mb-3\">\n                        <Card.Body>\n                            <Card.Title>{t('member_count')}</Card.Title>\n                            <h3>{dashboardStats.memberCount}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={3}>\n                    <Card className=\"mb-3\">\n                        <Card.Body>\n                            <Card.Title>{t('total_storage')}</Card.Title>\n                            <h3>{dashboardStats.totalStorage.toFixed(2)} TiB</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={3}>\n                    <Card className=\"mb-3\">\n                        <Card.Body>\n                            <Card.Title>{t('sold_storage')}</Card.Title>\n                            <h3>{dashboardStats.soldStorage.toFixed(2)} TiB</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={3}>\n                    <Card className=\"text-dark mb-3\">\n                        <Card.Body>\n                            <Card.Title>{t('remaining_storage')}</Card.Title>\n                            <h3>{dashboardStats.remainingStorage.toFixed(2)} TiB</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            <Row className=\"mt-4\">\n                <Col md={6} className=\"text-center\">\n                    <Card>\n                        <Card.Body>\n                            <h4>{t('member_management')}</h4>\n                            <p>{t('my_subordinate_members')}</p>\n                            <Button as={Link} to=\"/agent/member-list\">{t('enter_member_list')}</Button>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={6} className=\"text-center\">\n                    <Card>\n                        <Card.Body>\n                            <h4>{t('product_management')}</h4>\n                            <p>{t('products_on_sale')}</p>\n                            <Button as={Link} to=\"/agent/products\">{t('browse_agent_products')}</Button>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default AgentDashboard;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Row = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'row');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const sizePrefix = `${decoratedBsPrefix}-cols`;\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let cols;\n    if (propValue != null && typeof propValue === 'object') {\n      ({\n        cols\n      } = propValue);\n    } else {\n      cols = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (cols != null) classes.push(`${sizePrefix}${infix}-${cols}`);\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, decoratedBsPrefix, ...classes)\n  });\n});\nRow.displayName = 'Row';\nexport default Row;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function useCol({\n  as,\n  bsPrefix,\n  className,\n  ...props\n}) {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'col');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const spans = [];\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let span;\n    let offset;\n    let order;\n    if (typeof propValue === 'object' && propValue != null) {\n      ({\n        span,\n        offset,\n        order\n      } = propValue);\n    } else {\n      span = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (span) spans.push(span === true ? `${bsPrefix}${infix}` : `${bsPrefix}${infix}-${span}`);\n    if (order != null) classes.push(`order${infix}-${order}`);\n    if (offset != null) classes.push(`offset${infix}-${offset}`);\n  });\n  return [{\n    ...props,\n    className: classNames(className, ...spans, ...classes)\n  }, {\n    as,\n    bsPrefix,\n    spans\n  }];\n}\nconst Col = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n(props, ref) => {\n  const [{\n    className,\n    ...colProps\n  }, {\n    as: Component = 'div',\n    bsPrefix,\n    spans\n  }] = useCol(props);\n  return /*#__PURE__*/_jsx(Component, {\n    ...colProps,\n    ref: ref,\n    className: classNames(className, !spans.length && bsPrefix)\n  });\n});\nCol.displayName = 'Col';\nexport default Col;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});"], "names": ["AgentDashboard", "t", "useTranslation", "agentProfile", "setAgentProfile", "useState", "loading", "setLoading", "dashboardStats", "setDashboardStats", "totalTechCommission", "totalOpsCommission", "memberCount", "totalStorage", "soldStorage", "remainingStorage", "useEffect", "async", "supabase", "getSupabase", "data", "user", "auth", "getUser", "console", "log", "id", "localStorage", "getItem", "profileData", "error", "profileError", "from", "select", "eq", "single", "agentUserId", "makerId", "memberData", "member<PERSON><PERSON><PERSON>", "count", "orderData", "orderError", "productData", "productError", "stats", "length", "for<PERSON>ach", "order", "techCommission", "storage_cost", "tech_fee_pct", "opsCommission", "ops_fee_pct", "product", "total_shares", "sold_shares", "fetchDashboardStats", "maker_id", "fetchAgentData", "_jsx", "children", "_jsxs", "Container", "fluid", "Row", "className", "Col", "md", "Card", "Body", "Title", "brand_name", "commission_pct", "kyc_status", "toFixed", "<PERSON><PERSON>", "as", "Link", "to", "React", "_ref", "ref", "bsPrefix", "Component", "props", "decoratedBsPrefix", "useBootstrapPrefix", "breakpoints", "useBootstrapBreakpoints", "minBreakpoint", "useBootstrapMinBreakpoint", "sizePrefix", "classes", "brkPoint", "propValue", "cols", "infix", "push", "classNames", "displayName", "colProps", "spans", "span", "offset", "useCol", "CardBody", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "prefix", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "value", "CardImg", "variant", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "bg", "text", "border", "body", "Object", "assign", "Img", "Subtitle", "Text", "Header", "Footer", "ImgOverlay"], "sourceRoot": ""}