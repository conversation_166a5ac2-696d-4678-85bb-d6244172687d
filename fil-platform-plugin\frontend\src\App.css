.App {
  text-align: center;
  font-family: "Noto Sans SC", "PingFang SC", "Microsoft YaHei", "Cardo", sans-serif;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.card-box {
  border-radius: 8px;
  padding: 1rem;
  color: #333;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.05);
}

/* 替代 Bootstrap 的 bg-primary */
.card-primary {
  background-color: #f1f5f9; /* 柔和蓝灰 */
}

/* 替代 bg-success */
.card-success {
  background-color: #e2f0ea; /* 柔和绿色 */
}

/* 替代 bg-info */
.card-info {
  background-color: #edf6f9; /* 柔和青灰 */
}

/* 标题字体略深 */
.card-box .card-title {
  font-size: 0.95rem;
  color: #555;
  margin-bottom: 0.5rem;
}

