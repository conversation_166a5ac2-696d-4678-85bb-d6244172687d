import React, { useState } from 'react';
import { <PERSON>, Button, Card, Alert } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { getSupabase } from '../supabaseClient';
import { useNavigate } from 'react-router-dom';

const LoginPage = () => {
  const { t } = useTranslation();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      const supabase = getSupabase();

      /* ① 登录 */
      const { error: signError } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      if (signError) throw signError;

      /* ② 取当前用户 & role */
      const {
        data: { user },
        error: userErr,
      } = await supabase.auth.getUser();
      if (userErr) throw userErr;

      let role = user?.user_metadata?.role;

      // 如果 user_metadata 里没有 role，就去 public.users 表查询
      if (!role) {
        const { data, error: profileErr } = await supabase
          .from('users')
          .select('role')
          .eq('id', user.id)
          .single();
        if (profileErr) throw profileErr;
        role = data.role;
      }

      /* ③ 把 role 存到 localStorage，供前端使用 */
      localStorage.setItem('user_role', role);

      /* ④ 根据 role 重定向 */
      switch (role) {
        case 'maker':
          navigate('/maker', { replace: true });
          break;
        case 'agent':
          navigate('/agent', { replace: true });
          break;
        default:
          navigate('/', { replace: true }); // customer
      }
    } catch (err) {
      console.error('Login Error:', err);
      setError(err.message || t('login_failed'));
    }

    setLoading(false);
  };

  return (
    <div
      className="d-flex justify-content-center align-items-center"
      style={{ minHeight: '80vh' }}
    >
      <Card style={{ width: '400px' }}>
        <Card.Body>
          <h2 className="text-center mb-4">{t('login')}</h2>
          {error && <Alert variant="danger">{error}</Alert>}
          <Form onSubmit={handleSubmit}>
            <Form.Group id="email" className="mb-3">
              <Form.Label>{t('email_address')}</Form.Label>
              <Form.Control
                type="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </Form.Group>

            <Form.Group id="password" className="mb-3">
              <Form.Label>{t('password')}</Form.Label>
              <Form.Control
                type="password"
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </Form.Group>

            <Button disabled={loading} className="w-100" type="submit">
              {loading ? t('logging_in') : t('login')}
            </Button>
          </Form>
        </Card.Body>
      </Card>
    </div>
  );
};

export default LoginPage;