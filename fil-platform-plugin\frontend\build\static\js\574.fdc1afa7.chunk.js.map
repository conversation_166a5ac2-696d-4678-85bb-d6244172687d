{"version": 3, "file": "static/js/574.fdc1afa7.chunk.js", "mappings": "wKAMA,MAAMA,EAAmBC,EAAAA,WAAiB,CAAAC,EAMvCC,KAAQ,IANgC,SACzCC,EAAQ,UACRC,EAEAC,GAAIC,EAAY,SACbC,GACJN,EACC,MAAMO,GAAoBC,EAAAA,EAAAA,IAAmBN,EAAU,OACjDO,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChBC,EAAa,GAAGN,SAChBO,EAAU,GAehB,OAdAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAIE,SADGZ,EAAMU,GAEI,MAAbC,GAA0C,kBAAdA,IAE5BC,QACED,GAEJC,EAAOD,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GAChD,MAARE,GAAcJ,EAAQM,KAAK,GAAGP,IAAaM,KAASD,QAEtCG,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWI,KAAsBO,OAG3DhB,EAAIyB,YAAc,MAClB,S,4IClCA,MAuGA,EAvGuBC,KACnB,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAAWC,IAAgBC,EAAAA,EAAAA,UAAS,KACpCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,GA+CvC,OA7CAG,EAAAA,EAAAA,WAAU,KACiBC,WACnB,MAAMC,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EAAU,OAEfH,GAAW,GACX,MAAQK,MAAM,KAAEC,UAAiBH,EAASI,KAAKC,UAE/C,IAAKF,EAED,YADAN,GAAW,GAKf,MAAM,KAAEK,EAAI,MAAEI,SAAgBN,EACzBO,KAAK,yBACLC,OAAO,8eAgBPC,MAAM,gBAAiB,CAAEC,WAAW,IAErCJ,EACAK,QAAQL,MAAM,kCAAmCA,GAEjDZ,EAAaQ,GAEjBL,GAAW,IAGfe,IACD,IAEChB,GACOT,EAAAA,EAAAA,KAAA,OAAA0B,SAAMtB,EAAE,wBAIfuB,EAAAA,EAAAA,MAACC,EAAAA,EAAS,CAAAF,SAAA,EACN1B,EAAAA,EAAAA,KAAA,MAAIlB,UAAU,OAAM4C,SAAEtB,EAAE,sBACxBJ,EAAAA,EAAAA,KAACvB,EAAAA,EAAG,CAAAiD,UACA1B,EAAAA,EAAAA,KAAC6B,EAAAA,EAAG,CAAAH,UACA1B,EAAAA,EAAAA,KAAC8B,EAAAA,EAAI,CAAAJ,UACD1B,EAAAA,EAAAA,KAAC8B,EAAAA,EAAKC,KAAI,CAAAL,UACNC,EAAAA,EAAAA,MAACK,EAAAA,EAAK,CAACC,SAAO,EAACC,UAAQ,EAACC,OAAK,EAACC,YAAU,EAAAV,SAAA,EACpC1B,EAAAA,EAAAA,KAAA,SAAA0B,UACIC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACI1B,EAAAA,EAAAA,KAAA,MAAA0B,SAAKtB,EAAE,eACPJ,EAAAA,EAAAA,KAAA,MAAA0B,SAAKtB,EAAE,eACPJ,EAAAA,EAAAA,KAAA,MAAA0B,SAAKtB,EAAE,oBACPJ,EAAAA,EAAAA,KAAA,MAAA0B,SAAKtB,EAAE,wBACPJ,EAAAA,EAAAA,KAAA,MAAA0B,SAAKtB,EAAE,YACPJ,EAAAA,EAAAA,KAAA,MAAA0B,SAAKtB,EAAE,wBACPJ,EAAAA,EAAAA,KAAA,MAAA0B,SAAKtB,EAAE,oBACPJ,EAAAA,EAAAA,KAAA,MAAA0B,SAAKtB,EAAE,mBAGfJ,EAAAA,EAAAA,KAAA,SAAA0B,SAC0B,IAArBpB,EAAU+B,QACPrC,EAAAA,EAAAA,KAAA,MAAA0B,UACI1B,EAAAA,EAAAA,KAAA,MAAIsC,QAAQ,IAAIxD,UAAU,cAAa4C,SAAEtB,EAAE,8BAG/CE,EAAUiC,IAAI,CAACC,EAAUC,KAAK,IAAAC,EAAAC,EAAAC,EAAA,OAC1BjB,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACI1B,EAAAA,EAAAA,KAAA,MAAA0B,UAAoB,QAAfgB,EAAAF,EAASK,cAAM,IAAAH,OAAA,EAAfA,EAAiBI,oBAAqB,OAC3C9C,EAAAA,EAAAA,KAAA,MAAA0B,UAAoB,QAAfiB,EAAAH,EAASK,cAAM,IAAAF,GAAY,QAAZC,EAAfD,EAAiBI,kBAAU,IAAAH,OAAZ,EAAfA,EAA6BI,OAAQ,OAC1ChD,EAAAA,EAAAA,KAAA,MAAA0B,SAAK,IAAIuB,KAAKT,EAASU,eAAeC,wBACtCnD,EAAAA,EAAAA,KAAA,MAAA0B,SAAKc,EAASY,mBAAqB,OACnCzB,EAAAA,EAAAA,MAAA,MAAAD,SAAA,CAAKc,EAASa,MAAQC,OAAOd,EAASa,OAAOE,QAAQ,GAAK,IAAI,WAC9D5B,EAAAA,EAAAA,MAAA,MAAAD,SAAA,CAAKc,EAASgB,kBAAoBF,OAAOd,EAASgB,mBAAmBD,QAAQ,GAAK,IAAI,WACtF5B,EAAAA,EAAAA,MAAA,MAAAD,SAAA,CAAKc,EAASiB,cAAgBH,OAAOd,EAASiB,eAAeF,QAAQ,GAAK,IAAI,WAC9E5B,EAAAA,EAAAA,MAAA,MAAAD,SAAA,CAAKc,EAASkB,QAAUJ,OAAOd,EAASkB,SAASH,QAAQ,GAAK,IAAI,YAR7D,GAAGf,EAASmB,YAAYnB,EAASU,mC,sFChFtF,MAAMlB,EAAqBtD,EAAAA,WAAiB,CAAAC,EAWzCC,KAAQ,IAXkC,SAC3CC,EAAQ,UACRC,EAAS,QACTmD,EAAO,SACPC,EAAQ,WACR0B,EAAU,MACVzB,EAAK,KACL0B,EAAI,QACJC,EAAO,WACP1B,KACGnD,GACJN,EACC,MAAMO,GAAoBC,EAAAA,EAAAA,IAAmBN,EAAU,SACjDY,EAAUQ,IAAWnB,EAAWI,EAAmB4E,GAAW,GAAG5E,KAAqB4E,IAAWD,GAAQ,GAAG3E,KAAqB2E,IAAQ5B,GAAW,GAAG/C,KAAwC,kBAAZ+C,EAAuB,WAAWA,IAAY,YAAaC,GAAY,GAAGhD,aAA8B0E,GAAc,GAAG1E,eAAgCiD,GAAS,GAAGjD,WACxV6E,GAAqB/D,EAAAA,EAAAA,KAAK,QAAS,IACpCf,EACHH,UAAWW,EACXb,IAAKA,IAEP,GAAIwD,EAAY,CACd,IAAI4B,EAAkB,GAAG9E,eAIzB,MAH0B,kBAAfkD,IACT4B,EAAkB,GAAGA,KAAmB5B,MAEtBpC,EAAAA,EAAAA,KAAK,MAAO,CAC9BlB,UAAWkF,EACXtC,SAAUqC,GAEd,CACA,OAAOA,IAET/B,EAAM9B,YAAc,QACpB,S,sFCQA,MAAM2B,EAAmBnD,EAAAA,WAEzB,CAACO,EAAOL,KACN,OAAO,UACLE,KACGmF,IAEHlF,GAAIC,EAAY,MAAK,SACrBH,EAAQ,MACRqF,IAjDG,SAAevF,GAKnB,IALoB,GACrBI,EAAE,SACFF,EAAQ,UACRC,KACGG,GACJN,EACCE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,OACxC,MAAMO,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChB2E,EAAQ,GACRzE,EAAU,GAqBhB,OApBAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAIwE,EACAC,EACA9C,SAHGrC,EAAMU,GAIY,kBAAdC,GAAuC,MAAbA,IAEjCuE,OACAC,SACA9C,SACE1B,GAEJuE,EAAOvE,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GACxDwE,GAAMD,EAAMnE,MAAc,IAAToE,EAAgB,GAAGtF,IAAWiB,IAAU,GAAGjB,IAAWiB,KAASqE,KACvE,MAAT7C,GAAe7B,EAAQM,KAAK,QAAQD,KAASwB,KACnC,MAAV8C,GAAgB3E,EAAQM,KAAK,SAASD,KAASsE,OAE9C,CAAC,IACHnF,EACHH,UAAWmB,IAAWnB,KAAcoF,KAAUzE,IAC7C,CACDV,KACAF,WACAqF,SAEJ,CAWOG,CAAOpF,GACZ,OAAoBe,EAAAA,EAAAA,KAAKhB,EAAW,IAC/BiF,EACHrF,IAAKA,EACLE,UAAWmB,IAAWnB,GAAYoF,EAAM7B,QAAUxD,OAGtDgD,EAAI3B,YAAc,MAClB,S,sFC1DA,MAAMoE,EAAwB5F,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPqF,EAASpE,YAAc,WACvB,UCdMqE,EAA0B7F,EAAAA,WAAiB,CAAAC,EAK9CC,KAAQ,IALuC,UAChDE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,gBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPsF,EAAWrE,YAAc,aACzB,U,cCZA,MAAMsE,EAA0B9F,EAAAA,WAAiB,CAAAC,EAM9CC,KAAQ,IANuC,SAChDC,EAAQ,UACRC,EAEAC,GAAIC,EAAY,SACbC,GACJN,EACC,MAAM8F,GAAStF,EAAAA,EAAAA,IAAmBN,EAAU,eACtC6F,GAAeC,EAAAA,EAAAA,SAAQ,KAAM,CACjCC,mBAAoBH,IAClB,CAACA,IACL,OAAoBzE,EAAAA,EAAAA,KAAK6E,EAAAA,EAAkBC,SAAU,CACnDC,MAAOL,EACPhD,UAAuB1B,EAAAA,EAAAA,KAAKhB,EAAW,CACrCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAW2F,SAIvCD,EAAWtE,YAAc,aACzB,UCvBM8E,EAAuBtG,EAAAA,WAE7B,CAAAC,EAMGC,KAAQ,IANV,SACCC,EAAQ,UACRC,EAAS,QACTgF,EACA/E,GAAIC,EAAY,SACbC,GACJN,EACC,MAAM8F,GAAStF,EAAAA,EAAAA,IAAmBN,EAAU,YAC5C,OAAoBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAW6D,EAAU,GAAGW,KAAUX,IAAYW,EAAQ3F,MAC9DG,MAGP+F,EAAQ9E,YAAc,UACtB,UCjBM+E,EAA8BvG,EAAAA,WAAiB,CAAAC,EAKlDC,KAAQ,IAL2C,UACpDE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,qBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPgG,EAAe/E,YAAc,iBAC7B,UCdMgF,EAAwBxG,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,OACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPiG,EAAShF,YAAc,WACvB,U,cCbA,MAAMiF,GAAgBC,EAAAA,EAAAA,GAAiB,MACjCC,EAA4B3G,EAAAA,WAAiB,CAAAC,EAKhDC,KAAQ,IALyC,UAClDE,EAAS,SACTD,EACAE,GAAIC,EAAYmG,KACblG,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,kBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPoG,EAAanF,YAAc,eAC3B,UChBMoF,EAAwB5G,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,OACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPqG,EAASpF,YAAc,WACvB,UCbMqF,GAAgBH,EAAAA,EAAAA,GAAiB,MACjCI,EAAyB9G,EAAAA,WAAiB,CAAAC,EAK7CC,KAAQ,IALsC,UAC/CE,EAAS,SACTD,EACAE,GAAIC,EAAYuG,KACbtG,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,eACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPuG,EAAUtF,YAAc,YACxB,UCPM4B,EAAoBpD,EAAAA,WAAiB,CAAAC,EAWxCC,KAAQ,IAXiC,SAC1CC,EAAQ,UACRC,EAAS,GACT2G,EAAE,KACFC,EAAI,OACJC,EAAM,KACNC,GAAO,EAAK,SACZlE,EAEA3C,GAAIC,EAAY,SACbC,GACJN,EACC,MAAM8F,GAAStF,EAAAA,EAAAA,IAAmBN,EAAU,QAC5C,OAAoBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAW2F,EAAQgB,GAAM,MAAMA,IAAMC,GAAQ,QAAQA,IAAQC,GAAU,UAAUA,KACvGjE,SAAUkE,GAAoB5F,EAAAA,EAAAA,KAAKsE,EAAU,CAC3C5C,SAAUA,IACPA,MAGTI,EAAK5B,YAAc,OACnB,QAAe2F,OAAOC,OAAOhE,EAAM,CACjCiE,IAAKf,EACLgB,MAAOR,EACPS,SAAUZ,EACVtD,KAAMuC,EACN4B,KAAMhB,EACNiB,KAAMb,EACNc,OAAQ5B,EACR6B,OAAQ9B,EACR+B,WAAYrB,G", "sources": ["../node_modules/react-bootstrap/esm/Row.js", "pages/maker/MinerSnapshots.js", "../node_modules/react-bootstrap/esm/Table.js", "../node_modules/react-bootstrap/esm/Col.js", "../node_modules/react-bootstrap/esm/CardBody.js", "../node_modules/react-bootstrap/esm/CardFooter.js", "../node_modules/react-bootstrap/esm/CardHeader.js", "../node_modules/react-bootstrap/esm/CardImg.js", "../node_modules/react-bootstrap/esm/CardImgOverlay.js", "../node_modules/react-bootstrap/esm/CardLink.js", "../node_modules/react-bootstrap/esm/CardSubtitle.js", "../node_modules/react-bootstrap/esm/CardText.js", "../node_modules/react-bootstrap/esm/CardTitle.js", "../node_modules/react-bootstrap/esm/Card.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Row = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'row');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const sizePrefix = `${decoratedBsPrefix}-cols`;\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let cols;\n    if (propValue != null && typeof propValue === 'object') {\n      ({\n        cols\n      } = propValue);\n    } else {\n      cols = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (cols != null) classes.push(`${sizePrefix}${infix}-${cols}`);\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, decoratedBsPrefix, ...classes)\n  });\n});\nRow.displayName = 'Row';\nexport default Row;", "import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst MinerSnapshots = () => {\n    const { t } = useTranslation();\n    const [snapshots, setSnapshots] = useState([]);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        const fetchSnapshots = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setLoading(false);\n                return; // User not logged in\n            }\n\n            // Fetch miner daily snapshots with miner information\n            const { data, error } = await supabase\n                .from('miner_daily_snapshots')\n                .select(`\n                    miner_id,\n                    snapshot_date,\n                    blockchain_height,\n                    power,\n                    available_balance,\n                    pledge_locked,\n                    balance,\n                    miners (\n                        filecoin_miner_id,\n                        category,\n                        facilities (\n                            name\n                        )\n                    )\n                `)\n                .order('snapshot_date', { ascending: false });\n\n            if (error) {\n                console.error('Error fetching miner snapshots:', error);\n            } else {\n                setSnapshots(data);\n            }\n            setLoading(false);\n        };\n\n        fetchSnapshots();\n    }, []);\n\n    if (loading) {\n        return <div>{t('loading_snapshots')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('miner_snapshots')}</h2>\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('miner_id')}</th>\n                                        <th>{t('facility')}</th>\n                                        <th>{t('snapshot_date')}</th>\n                                        <th>{t('blockchain_height')}</th>\n                                        <th>{t('power')}</th>\n                                        <th>{t('available_balance')}</th>\n                                        <th>{t('pledge_locked')}</th>\n                                        <th>{t('balance')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {snapshots.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"8\" className=\"text-center\">{t('no_snapshots_available')}</td>\n                                        </tr>\n                                    ) : (\n                                        snapshots.map((snapshot, index) => (\n                                            <tr key={`${snapshot.miner_id}-${snapshot.snapshot_date}`}>\n                                                <td>{snapshot.miners?.filecoin_miner_id || '-'}</td>\n                                                <td>{snapshot.miners?.facilities?.name || '-'}</td>\n                                                <td>{new Date(snapshot.snapshot_date).toLocaleDateString()}</td>\n                                                <td>{snapshot.blockchain_height || '-'}</td>\n                                                <td>{snapshot.power ? Number(snapshot.power).toFixed(2) : '0'} TiB</td>\n                                                <td>{snapshot.available_balance ? Number(snapshot.available_balance).toFixed(6) : '0'} FIL</td>\n                                                <td>{snapshot.pledge_locked ? Number(snapshot.pledge_locked).toFixed(6) : '0'} FIL</td>\n                                                <td>{snapshot.balance ? Number(snapshot.balance).toFixed(6) : '0'} FIL</td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default MinerSnapshots;\n", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Table = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  striped,\n  bordered,\n  borderless,\n  hover,\n  size,\n  variant,\n  responsive,\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'table');\n  const classes = classNames(className, decoratedBsPrefix, variant && `${decoratedBsPrefix}-${variant}`, size && `${decoratedBsPrefix}-${size}`, striped && `${decoratedBsPrefix}-${typeof striped === 'string' ? `striped-${striped}` : 'striped'}`, bordered && `${decoratedBsPrefix}-bordered`, borderless && `${decoratedBsPrefix}-borderless`, hover && `${decoratedBsPrefix}-hover`);\n  const table = /*#__PURE__*/_jsx(\"table\", {\n    ...props,\n    className: classes,\n    ref: ref\n  });\n  if (responsive) {\n    let responsiveClass = `${decoratedBsPrefix}-responsive`;\n    if (typeof responsive === 'string') {\n      responsiveClass = `${responsiveClass}-${responsive}`;\n    }\n    return /*#__PURE__*/_jsx(\"div\", {\n      className: responsiveClass,\n      children: table\n    });\n  }\n  return table;\n});\nTable.displayName = 'Table';\nexport default Table;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function useCol({\n  as,\n  bsPrefix,\n  className,\n  ...props\n}) {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'col');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const spans = [];\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let span;\n    let offset;\n    let order;\n    if (typeof propValue === 'object' && propValue != null) {\n      ({\n        span,\n        offset,\n        order\n      } = propValue);\n    } else {\n      span = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (span) spans.push(span === true ? `${bsPrefix}${infix}` : `${bsPrefix}${infix}-${span}`);\n    if (order != null) classes.push(`order${infix}-${order}`);\n    if (offset != null) classes.push(`offset${infix}-${offset}`);\n  });\n  return [{\n    ...props,\n    className: classNames(className, ...spans, ...classes)\n  }, {\n    as,\n    bsPrefix,\n    spans\n  }];\n}\nconst Col = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n(props, ref) => {\n  const [{\n    className,\n    ...colProps\n  }, {\n    as: Component = 'div',\n    bsPrefix,\n    spans\n  }] = useCol(props);\n  return /*#__PURE__*/_jsx(Component, {\n    ...colProps,\n    ref: ref,\n    className: classNames(className, !spans.length && bsPrefix)\n  });\n});\nCol.displayName = 'Col';\nexport default Col;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});"], "names": ["Row", "React", "_ref", "ref", "bsPrefix", "className", "as", "Component", "props", "decoratedBsPrefix", "useBootstrapPrefix", "breakpoints", "useBootstrapBreakpoints", "minBreakpoint", "useBootstrapMinBreakpoint", "sizePrefix", "classes", "for<PERSON>ach", "brkPoint", "propValue", "cols", "infix", "push", "_jsx", "classNames", "displayName", "MinerSnapshots", "t", "useTranslation", "snapshots", "setSnapshots", "useState", "loading", "setLoading", "useEffect", "async", "supabase", "getSupabase", "data", "user", "auth", "getUser", "error", "from", "select", "order", "ascending", "console", "fetchSnapshots", "children", "_jsxs", "Container", "Col", "Card", "Body", "Table", "striped", "bordered", "hover", "responsive", "length", "colSpan", "map", "snapshot", "index", "_snapshot$miners", "_snapshot$miners2", "_snapshot$miners2$fac", "miners", "filecoin_miner_id", "facilities", "name", "Date", "snapshot_date", "toLocaleDateString", "blockchain_height", "power", "Number", "toFixed", "available_balance", "pledge_locked", "balance", "miner_id", "borderless", "size", "variant", "table", "responsiveClass", "colProps", "spans", "span", "offset", "useCol", "CardBody", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "prefix", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "value", "CardImg", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "bg", "text", "border", "body", "Object", "assign", "Img", "Title", "Subtitle", "Link", "Text", "Header", "Footer", "ImgOverlay"], "sourceRoot": ""}