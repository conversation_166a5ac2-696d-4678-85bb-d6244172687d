# Filfox.info Data Scraper

这个脚本每天凌晨2点（东京时间）从 https://filfox.info/en 爬取区块高度和24小时平均挖矿奖励数据，并存储到数据库的 `network_stats` 表中。

## 功能特性

- 🕐 **定时执行**: 每天凌晨2点（JST）自动运行
- 🌐 **数据爬取**: 从filfox.info获取最新的区块链数据
- 💾 **数据存储**: 自动存储到Supabase数据库
- 🐳 **Docker支持**: 支持Docker容器化部署
- 📝 **日志记录**: 详细的执行日志
- 🧪 **测试功能**: 包含测试脚本验证功能

## 安装步骤

### 1. 安装依赖

```bash
cd scripts
npm install
```

### 2. 配置环境变量

复制环境变量模板并填入实际配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件：

```env
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_KEY=your_supabase_service_role_key
TZ=Asia/Tokyo
```

**重要**: 请使用 Supabase 的 **service_role** 密钥，而不是 anon 密钥，因为服务端操作需要更高权限。

### 3. 测试爬虫功能

运行测试脚本验证爬虫是否正常工作：

```bash
npm test
# 或者
node test-scraper.js
```

### 4. 手动运行一次

测试完整的数据爬取和存储流程：

```bash
npm start
# 或者
node filfox-scraper.js
```

## 使用方法

### 方法1: 直接运行调度器

```bash
node scheduler.js
```

这将启动定时任务，每天凌晨2点自动执行爬虫。

### 方法2: 使用Docker

构建并运行Docker容器：

```bash
# 构建镜像
docker build -t filfox-scraper .

# 运行容器
docker run -d --name filfox-scraper --env-file .env filfox-scraper
```

### 方法3: 使用Docker Compose

```bash
# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 脚本说明

### filfox-scraper.js
主要的爬虫脚本，负责：
- 使用Puppeteer访问filfox.info
- 提取区块高度和24小时平均挖矿奖励
- 将数据存储到network_stats表

### scheduler.js
定时任务调度器，负责：
- 设置东京时间为时区
- 每天凌晨2点执行爬虫
- 记录执行日志

### test-scraper.js
测试脚本，用于验证爬虫功能是否正常。

### monitor.js
监控脚本，用于检查系统健康状态：
- 数据库连接性检查
- 最近数据检查
- 数据质量验证

## 数据库表结构

脚本会向 `network_stats` 表插入数据：

```sql
CREATE TABLE "network_stats" (
  "stat_date" date PRIMARY KEY,
  "blockchain_height" bigint,
  "fil_per_tib" numeric
);
```

- `stat_date`: 统计日期 (YYYY-MM-DD)
- `blockchain_height`: 区块高度
- `fil_per_tib`: 24小时平均挖矿奖励 (FIL per TiB)

## 故障排除

### 1. 爬虫无法获取数据

- 检查网络连接
- 验证filfox.info网站是否可访问
- 网站结构可能发生变化，需要更新选择器

### 2. 数据库连接失败

- 检查Supabase URL和密钥是否正确
- 确认使用的是service_role密钥
- 检查网络连接到Supabase

### 3. Docker容器问题

- 检查环境变量是否正确设置
- 查看容器日志: `docker logs filfox-scraper`
- 确认Docker有足够权限运行Puppeteer

## 监控和维护

### 查看日志

```bash
# 直接运行时的日志
tail -f logs/scraper.log

# Docker容器日志
docker logs -f filfox-scraper

# Docker Compose日志
docker-compose logs -f
```

### 手动触发执行

如果需要立即执行一次爬虫：

```bash
# 直接运行爬虫
node filfox-scraper.js

# 或者运行调度器并立即执行
node scheduler.js --run-now
```

### 健康检查

运行监控脚本检查系统状态：

```bash
# 运行健康检查
npm run monitor
# 或者
node monitor.js
```

## 注意事项

1. **时区设置**: 脚本使用东京时间 (JST)，确保服务器时区设置正确
2. **网站变化**: filfox.info的页面结构可能会变化，需要定期检查和更新爬虫逻辑
3. **频率限制**: 避免过于频繁地访问目标网站，当前设置为每天一次
4. **错误处理**: 脚本包含错误处理，但建议设置监控告警
5. **数据验证**: 建议定期检查存储的数据是否合理

## 许可证

MIT License
