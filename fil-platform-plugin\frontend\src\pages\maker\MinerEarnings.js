import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const MinerEarnings = () => {
    const { t } = useTranslation();
    const [earnings, setEarnings] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchEarnings = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // Fetch miner daily earnings with miner information
            const { data, error } = await supabase
                .from('miner_daily_earnings')
                .select(`
                    miner_id,
                    earn_date,
                    cumulative_reward,
                    daily_reward,
                    blocks_won,
                    created_at,
                    miners (
                        filecoin_miner_id,
                        category,
                        facilities (
                            name
                        )
                    )
                `)
                .order('earn_date', { ascending: false });

            if (error) {
                console.error('Error fetching miner earnings:', error);
            } else {
                setEarnings(data);
            }
            setLoading(false);
        };

        fetchEarnings();
    }, []);

    if (loading) {
        return <div>{t('loading_earnings')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('miner_earnings')}</h2>
            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('miner_id')}</th>
                                        <th>{t('facility')}</th>
                                        <th>{t('earn_date')}</th>
                                        <th>{t('daily_reward')}</th>
                                        <th>{t('cumulative_reward')}</th>
                                        <th>{t('blocks_won')}</th>
                                        <th>{t('created_at')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {earnings.length === 0 ? (
                                        <tr>
                                            <td colSpan="7" className="text-center">{t('no_earnings_available')}</td>
                                        </tr>
                                    ) : (
                                        earnings.map((earning, index) => (
                                            <tr key={`${earning.miner_id}-${earning.earn_date}`}>
                                                <td>{earning.miners?.filecoin_miner_id || '-'}</td>
                                                <td>{earning.miners?.facilities?.name || '-'}</td>
                                                <td>{new Date(earning.earn_date).toLocaleDateString()}</td>
                                                <td>{earning.daily_reward ? Number(earning.daily_reward).toFixed(6) : '0'} FIL</td>
                                                <td>{earning.cumulative_reward ? Number(earning.cumulative_reward).toFixed(6) : '0'} FIL</td>
                                                <td>{earning.blocks_won || 0}</td>
                                                <td>{new Date(earning.created_at).toLocaleString()}</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default MinerEarnings;
