"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[924],{1072:(e,s,r)=>{r.d(s,{A:()=>c});var t=r(8139),n=r.n(t),i=r(5043),a=r(7852),l=r(579);const o=i.forwardRef((e,s)=>{let{bsPrefix:r,className:t,as:i="div",...o}=e;const c=(0,a.oU)(r,"row"),d=(0,a.gy)(),u=(0,a.Jm)(),m=`${c}-cols`,f=[];return d.forEach(e=>{const s=o[e];let r;delete o[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const t=e!==u?`-${e}`:"";null!=r&&f.push(`${m}${t}-${r}`)}),(0,l.jsx)(i,{ref:s,...o,className:n()(t,c,...f)})});o.displayName="Row";const c=o},1719:(e,s,r)=>{r.d(s,{A:()=>b});var t=r(8139),n=r.n(t),i=r(5043),a=r(1969),l=r(6618),o=r(7852),c=r(4488),d=r(579);const u=(0,c.A)("h4");u.displayName="DivStyledAsH4";const m=i.forwardRef((e,s)=>{let{className:r,bsPrefix:t,as:i=u,...a}=e;return t=(0,o.oU)(t,"alert-heading"),(0,d.jsx)(i,{ref:s,className:n()(r,t),...a})});m.displayName="AlertHeading";const f=m;var h=r(7071);const x=i.forwardRef((e,s)=>{let{className:r,bsPrefix:t,as:i=h.A,...a}=e;return t=(0,o.oU)(t,"alert-link"),(0,d.jsx)(i,{ref:s,className:n()(r,t),...a})});x.displayName="AlertLink";const j=x;var p=r(8072),y=r(5632);const g=i.forwardRef((e,s)=>{const{bsPrefix:r,show:t=!0,closeLabel:i="Close alert",closeVariant:c,className:u,children:m,variant:f="primary",onClose:h,dismissible:x,transition:j=p.A,...g}=(0,a.Zw)(e,{show:"onClose"}),b=(0,o.oU)(r,"alert"),v=(0,l.A)(e=>{h&&h(!1,e)}),_=!0===j?p.A:j,A=(0,d.jsxs)("div",{role:"alert",..._?void 0:g,ref:s,className:n()(u,b,f&&`${b}-${f}`,x&&`${b}-dismissible`),children:[x&&(0,d.jsx)(y.A,{onClick:v,"aria-label":i,variant:c}),m]});return _?(0,d.jsx)(_,{unmountOnExit:!0,...g,ref:void 0,in:t,children:A}):t?A:null});g.displayName="Alert";const b=Object.assign(g,{Link:j,Heading:f})},2924:(e,s,r)=>{r.r(s),r.d(s,{default:()=>j});var t=r(5043),n=r(3519),i=r(1719),a=r(1072),l=r(8602),o=r(8628),c=r(4196),d=r(4282),u=r(3083),m=r(3722),f=r(4312),h=r(4117),x=r(579);const j=()=>{const{t:e}=(0,h.Bd)(),[s,r]=(0,t.useState)([]),[j,p]=(0,t.useState)(!0),[y,g]=(0,t.useState)(!1),[b,v]=(0,t.useState)(null),[_,A]=(0,t.useState)({category:"",filecoin_miner_id:"",sector_size:"",effective_until:""}),[N,w]=(0,t.useState)(!1),[$,C]=(0,t.useState)(null),[S,k]=(0,t.useState)({show:!1,type:"",message:""});(0,t.useEffect)(()=>{(async()=>{const e=(0,f.b)();if(!e)return;p(!0);const{data:{user:s}}=await e.auth.getUser();if(!s)return void p(!1);const{data:t,error:n}=await e.from("miners").select("\n                    id,\n                    category,\n                    filecoin_miner_id,\n                    sector_size,\n                    effective_until,\n                    created_at,\n                    updated_at,\n                    facilities (\n                        name\n                    )\n                ").order("created_at",{ascending:!1});n?console.error("Error fetching miners:",n):r(t),p(!1)})()},[]);const R=e=>{const{name:s,value:r}=e.target;A(e=>({...e,[s]:r}))};return j?(0,x.jsx)("div",{children:e("loading_miners")}):(0,x.jsxs)(n.A,{children:[S.show&&(0,x.jsx)(i.A,{variant:S.type,dismissible:!0,onClose:()=>{k({show:!1,type:"",message:""})},className:"mb-4",children:S.message}),(0,x.jsx)("h2",{className:"mb-4",children:e("all_miners")}),(0,x.jsx)(a.A,{children:(0,x.jsx)(l.A,{children:(0,x.jsx)(o.A,{children:(0,x.jsx)(o.A.Body,{children:(0,x.jsxs)(c.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,x.jsx)("thead",{children:(0,x.jsxs)("tr",{children:[(0,x.jsx)("th",{children:"ID"}),(0,x.jsx)("th",{children:e("category")}),(0,x.jsx)("th",{children:e("facility")}),(0,x.jsx)("th",{children:e("miner_id")}),(0,x.jsx)("th",{children:e("effective_until")}),(0,x.jsx)("th",{children:e("created_at")}),(0,x.jsx)("th",{children:e("updated_at")}),(0,x.jsx)("th",{children:e("actions")})]})}),(0,x.jsx)("tbody",{children:0===s.length?(0,x.jsx)("tr",{children:(0,x.jsx)("td",{colSpan:"9",className:"text-center",children:e("no_miners_available")})}):s.map(s=>{var r;return(0,x.jsxs)("tr",{children:[(0,x.jsxs)("td",{children:[s.id.substring(0,8),"..."]}),(0,x.jsx)("td",{children:s.category}),(0,x.jsx)("td",{children:(null===(r=s.facilities)||void 0===r?void 0:r.name)||"-"}),(0,x.jsx)("td",{children:s.filecoin_miner_id}),(0,x.jsx)("td",{children:s.sector_size}),(0,x.jsx)("td",{children:s.effective_until}),(0,x.jsx)("td",{children:new Date(s.created_at).toLocaleString()}),(0,x.jsx)("td",{children:new Date(s.updated_at).toLocaleString()}),(0,x.jsxs)("td",{children:[(0,x.jsx)(d.A,{variant:"info",size:"sm",className:"me-2",onClick:()=>(e=>{v(e),A({category:e.category||"",filecoin_miner_id:e.filecoin_miner_id||"",sector_size:e.sector_size||"",effective_until:e.effective_until||""}),g(!0)})(s),children:e("edit")}),(0,x.jsx)(d.A,{variant:"danger",size:"sm",onClick:()=>(e=>{C(e),w(!0)})(s),children:e("delete")})]})]},s.id)})})]})})})})}),(0,x.jsxs)(u.A,{show:y,onHide:()=>g(!1),size:"lg",children:[(0,x.jsx)(u.A.Header,{closeButton:!0,children:(0,x.jsx)(u.A.Title,{children:e("edit_miner")})}),(0,x.jsx)(u.A.Body,{children:(0,x.jsxs)(m.A,{onSubmit:async t=>{t.preventDefault();const n=(0,f.b)();if(n&&b)try{const{error:t}=await n.from("miners").update({category:_.category,filecoin_miner_id:_.filecoin_miner_id,sector_size:_.sector_size,effective_until:_.effective_until,updated_at:(new Date).toISOString()}).eq("id",b.id);if(t)throw t;r(s.map(e=>e.id===b.id?{...e,..._,updated_at:(new Date).toISOString()}:e)),k({show:!0,type:"success",message:e("item_updated_successfully")}),g(!1),v(null)}catch(i){console.error("Error updating miner:",i),k({show:!0,type:"danger",message:e("failed_to_update_item")+": "+i.message})}},children:[(0,x.jsxs)(m.A.Group,{className:"mb-3",children:[(0,x.jsx)(m.A.Label,{children:e("category")}),(0,x.jsx)(m.A.Control,{type:"text",name:"category",value:_.category,onChange:R,required:!0})]}),(0,x.jsxs)(m.A.Group,{className:"mb-3",children:[(0,x.jsx)(m.A.Label,{children:e("miner_id")}),(0,x.jsx)(m.A.Control,{type:"text",name:"filecoin_miner_id",value:_.filecoin_miner_id,onChange:R,required:!0})]}),(0,x.jsxs)(m.A.Group,{className:"mb-3",children:[(0,x.jsx)(m.A.Label,{children:e("sector_size")}),(0,x.jsx)(m.A.Control,{type:"text",name:"sector_size",value:_.sector_size,onChange:R,required:!0})]}),(0,x.jsxs)(m.A.Group,{className:"mb-3",children:[(0,x.jsx)(m.A.Label,{children:e("effective_until")}),(0,x.jsx)(m.A.Control,{type:"date",name:"effective_until",value:_.effective_until,onChange:R,required:!0})]}),(0,x.jsxs)("div",{className:"d-flex justify-content-end",children:[(0,x.jsx)(d.A,{variant:"secondary",className:"me-2",onClick:()=>g(!1),children:e("cancel")}),(0,x.jsx)(d.A,{variant:"primary",type:"submit",children:e("save_changes")})]})]})})]}),(0,x.jsxs)(u.A,{show:N,onHide:()=>w(!1),children:[(0,x.jsx)(u.A.Header,{closeButton:!0,children:(0,x.jsx)(u.A.Title,{children:e("confirm_delete")})}),(0,x.jsxs)(u.A.Body,{children:[(0,x.jsx)("p",{children:e("delete_confirmation")}),$&&(0,x.jsx)("p",{children:(0,x.jsxs)("strong",{children:[e("miner_id"),": ",$.filecoin_miner_id]})})]}),(0,x.jsxs)(u.A.Footer,{children:[(0,x.jsx)(d.A,{variant:"secondary",onClick:()=>w(!1),children:e("cancel")}),(0,x.jsx)(d.A,{variant:"danger",onClick:async()=>{const t=(0,f.b)();if(t&&$)try{const{error:n}=await t.from("miners").delete().eq("id",$.id);if(n)throw n;r(s.filter(e=>e.id!==$.id)),k({show:!0,type:"success",message:e("item_deleted_successfully")}),w(!1),C(null)}catch(n){console.error("Error deleting miner:",n),k({show:!0,type:"danger",message:e("failed_to_delete_item")+": "+n.message})}},children:e("confirm")})]})]})]})}},3083:(e,s,r)=>{r.d(s,{A:()=>L});var t,n=r(8139),i=r.n(n),a=r(3043),l=r(8279),o=r(182),c=r(8260);function d(e){if((!t&&0!==t||e)&&l.A){var s=document.createElement("div");s.style.position="absolute",s.style.top="-9999px",s.style.width="50px",s.style.height="50px",s.style.overflow="scroll",document.body.appendChild(s),t=s.offsetWidth-s.clientWidth,document.body.removeChild(s)}return t}var u=r(5043);var m=r(6618),f=r(8293);function h(e){const s=function(e){const s=(0,u.useRef)(e);return s.current=e,s}(e);(0,u.useEffect)(()=>()=>s.current(),[])}var x=r(4232),j=r(3655),p=r(5675),y=r(8072),g=r(7852),b=r(579);const v=u.forwardRef((e,s)=>{let{className:r,bsPrefix:t,as:n="div",...a}=e;return t=(0,g.oU)(t,"modal-body"),(0,b.jsx)(n,{ref:s,className:i()(r,t),...a})});v.displayName="ModalBody";const _=v;var A=r(1602);const N=u.forwardRef((e,s)=>{let{bsPrefix:r,className:t,contentClassName:n,centered:a,size:l,fullscreen:o,children:c,scrollable:d,...u}=e;r=(0,g.oU)(r,"modal");const m=`${r}-dialog`,f="string"===typeof o?`${r}-fullscreen-${o}`:`${r}-fullscreen`;return(0,b.jsx)("div",{...u,ref:s,className:i()(m,t,l&&`${r}-${l}`,a&&`${m}-centered`,d&&`${m}-scrollable`,o&&f),children:(0,b.jsx)("div",{className:i()(`${r}-content`,n),children:c})})});N.displayName="ModalDialog";const w=N,$=u.forwardRef((e,s)=>{let{className:r,bsPrefix:t,as:n="div",...a}=e;return t=(0,g.oU)(t,"modal-footer"),(0,b.jsx)(n,{ref:s,className:i()(r,t),...a})});$.displayName="ModalFooter";const C=$;var S=r(2258);const k=u.forwardRef((e,s)=>{let{bsPrefix:r,className:t,closeLabel:n="Close",closeButton:a=!1,...l}=e;return r=(0,g.oU)(r,"modal-header"),(0,b.jsx)(S.A,{ref:s,...l,className:i()(t,r),closeLabel:n,closeButton:a})});k.displayName="ModalHeader";const R=k;const E=(0,r(4488).A)("h4"),z=u.forwardRef((e,s)=>{let{className:r,bsPrefix:t,as:n=E,...a}=e;return t=(0,g.oU)(t,"modal-title"),(0,b.jsx)(n,{ref:s,className:i()(r,t),...a})});z.displayName="ModalTitle";const D=z;function T(e){return(0,b.jsx)(y.A,{...e,timeout:null})}function U(e){return(0,b.jsx)(y.A,{...e,timeout:null})}const H=u.forwardRef((e,s)=>{let{bsPrefix:r,className:t,style:n,dialogClassName:y,contentClassName:v,children:_,dialogAs:N=w,"data-bs-theme":$,"aria-labelledby":C,"aria-describedby":S,"aria-label":k,show:R=!1,animation:E=!0,backdrop:z=!0,keyboard:D=!0,onEscapeKeyDown:H,onShow:L,onHide:P,container:B,autoFocus:O=!0,enforceFocus:F=!0,restoreFocus:I=!0,restoreFocusOptions:M,onEntered:q,onExit:G,onExiting:W,onEnter:K,onEntering:J,onExited:V,backdropClassName:Z,manager:Q,...X}=e;const[Y,ee]=(0,u.useState)({}),[se,re]=(0,u.useState)(!1),te=(0,u.useRef)(!1),ne=(0,u.useRef)(!1),ie=(0,u.useRef)(null),[ae,le]=(0,u.useState)(null),oe=(0,f.A)(s,le),ce=(0,m.A)(P),de=(0,g.Wz)();r=(0,g.oU)(r,"modal");const ue=(0,u.useMemo)(()=>({onHide:ce}),[ce]);function me(){return Q||(0,p.R)({isRTL:de})}function fe(e){if(!l.A)return;const s=me().getScrollbarWidth()>0,r=e.scrollHeight>(0,o.A)(e).documentElement.clientHeight;ee({paddingRight:s&&!r?d():void 0,paddingLeft:!s&&r?d():void 0})}const he=(0,m.A)(()=>{ae&&fe(ae.dialog)});h(()=>{(0,c.A)(window,"resize",he),null==ie.current||ie.current()});const xe=()=>{te.current=!0},je=e=>{te.current&&ae&&e.target===ae.dialog&&(ne.current=!0),te.current=!1},pe=()=>{re(!0),ie.current=(0,x.A)(ae.dialog,()=>{re(!1)})},ye=e=>{"static"!==z?ne.current||e.target!==e.currentTarget?ne.current=!1:null==P||P():(e=>{e.target===e.currentTarget&&pe()})(e)},ge=(0,u.useCallback)(e=>(0,b.jsx)("div",{...e,className:i()(`${r}-backdrop`,Z,!E&&"show")}),[E,Z,r]),be={...n,...Y};be.display="block";return(0,b.jsx)(A.A.Provider,{value:ue,children:(0,b.jsx)(j.A,{show:R,ref:oe,backdrop:z,container:B,keyboard:!0,autoFocus:O,enforceFocus:F,restoreFocus:I,restoreFocusOptions:M,onEscapeKeyDown:e=>{D?null==H||H(e):(e.preventDefault(),"static"===z&&pe())},onShow:L,onHide:P,onEnter:(e,s)=>{e&&fe(e),null==K||K(e,s)},onEntering:(e,s)=>{null==J||J(e,s),(0,a.Ay)(window,"resize",he)},onEntered:q,onExit:e=>{null==ie.current||ie.current(),null==G||G(e)},onExiting:W,onExited:e=>{e&&(e.style.display=""),null==V||V(e),(0,c.A)(window,"resize",he)},manager:me(),transition:E?T:void 0,backdropTransition:E?U:void 0,renderBackdrop:ge,renderDialog:e=>(0,b.jsx)("div",{role:"dialog",...e,style:be,className:i()(t,r,se&&`${r}-static`,!E&&"show"),onClick:z?ye:void 0,onMouseUp:je,"data-bs-theme":$,"aria-label":k,"aria-labelledby":C,"aria-describedby":S,children:(0,b.jsx)(N,{...X,onMouseDown:xe,className:y,contentClassName:v,children:_})})})})});H.displayName="Modal";const L=Object.assign(H,{Body:_,Header:R,Title:D,Footer:C,Dialog:w,TRANSITION_DURATION:300,BACKDROP_TRANSITION_DURATION:150})},4196:(e,s,r)=>{r.d(s,{A:()=>c});var t=r(8139),n=r.n(t),i=r(5043),a=r(7852),l=r(579);const o=i.forwardRef((e,s)=>{let{bsPrefix:r,className:t,striped:i,bordered:o,borderless:c,hover:d,size:u,variant:m,responsive:f,...h}=e;const x=(0,a.oU)(r,"table"),j=n()(t,x,m&&`${x}-${m}`,u&&`${x}-${u}`,i&&`${x}-${"string"===typeof i?`striped-${i}`:"striped"}`,o&&`${x}-bordered`,c&&`${x}-borderless`,d&&`${x}-hover`),p=(0,l.jsx)("table",{...h,className:j,ref:s});if(f){let e=`${x}-responsive`;return"string"===typeof f&&(e=`${e}-${f}`),(0,l.jsx)("div",{className:e,children:p})}return p});o.displayName="Table";const c=o}}]);
//# sourceMappingURL=924.b3b24eb1.chunk.js.map