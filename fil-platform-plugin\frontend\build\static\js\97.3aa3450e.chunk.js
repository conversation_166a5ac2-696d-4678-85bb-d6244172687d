"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[97],{1072:(e,s,t)=>{t.d(s,{A:()=>c});var a=t(8139),r=t.n(a),n=t(5043),l=t(7852),o=t(579);const i=n.forwardRef((e,s)=>{let{bsPrefix:t,className:a,as:n="div",...i}=e;const c=(0,l.oU)(t,"row"),d=(0,l.gy)(),u=(0,l.Jm)(),m=`${c}-cols`,f=[];return d.forEach(e=>{const s=i[e];let t;delete i[e],null!=s&&"object"===typeof s?({cols:t}=s):t=s;const a=e!==u?`-${e}`:"";null!=t&&f.push(`${m}${a}-${t}`)}),(0,o.jsx)(n,{ref:s,...i,className:r()(a,c,...f)})});i.displayName="Row";const c=i},1719:(e,s,t)=>{t.d(s,{A:()=>g});var a=t(8139),r=t.n(a),n=t(5043),l=t(1969),o=t(6618),i=t(7852),c=t(4488),d=t(579);const u=(0,c.A)("h4");u.displayName="DivStyledAsH4";const m=n.forwardRef((e,s)=>{let{className:t,bsPrefix:a,as:n=u,...l}=e;return a=(0,i.oU)(a,"alert-heading"),(0,d.jsx)(n,{ref:s,className:r()(t,a),...l})});m.displayName="AlertHeading";const f=m;var h=t(7071);const x=n.forwardRef((e,s)=>{let{className:t,bsPrefix:a,as:n=h.A,...l}=e;return a=(0,i.oU)(a,"alert-link"),(0,d.jsx)(n,{ref:s,className:r()(t,a),...l})});x.displayName="AlertLink";const p=x;var j=t(8072),b=t(5632);const y=n.forwardRef((e,s)=>{const{bsPrefix:t,show:a=!0,closeLabel:n="Close alert",closeVariant:c,className:u,children:m,variant:f="primary",onClose:h,dismissible:x,transition:p=j.A,...y}=(0,l.Zw)(e,{show:"onClose"}),g=(0,i.oU)(t,"alert"),A=(0,o.A)(e=>{h&&h(!1,e)}),v=!0===p?j.A:p,N=(0,d.jsxs)("div",{role:"alert",...v?void 0:y,ref:s,className:r()(u,g,f&&`${g}-${f}`,x&&`${g}-dismissible`),children:[x&&(0,d.jsx)(b.A,{onClick:A,"aria-label":n,variant:c}),m]});return v?(0,d.jsx)(v,{unmountOnExit:!0,...y,ref:void 0,in:a,children:N}):a?N:null});y.displayName="Alert";const g=Object.assign(y,{Link:p,Heading:f})},3083:(e,s,t)=>{t.d(s,{A:()=>B});var a,r=t(8139),n=t.n(r),l=t(3043),o=t(8279),i=t(182),c=t(8260);function d(e){if((!a&&0!==a||e)&&o.A){var s=document.createElement("div");s.style.position="absolute",s.style.top="-9999px",s.style.width="50px",s.style.height="50px",s.style.overflow="scroll",document.body.appendChild(s),a=s.offsetWidth-s.clientWidth,document.body.removeChild(s)}return a}var u=t(5043);var m=t(6618),f=t(8293);function h(e){const s=function(e){const s=(0,u.useRef)(e);return s.current=e,s}(e);(0,u.useEffect)(()=>()=>s.current(),[])}var x=t(4232),p=t(3655),j=t(5675),b=t(8072),y=t(7852),g=t(579);const A=u.forwardRef((e,s)=>{let{className:t,bsPrefix:a,as:r="div",...l}=e;return a=(0,y.oU)(a,"modal-body"),(0,g.jsx)(r,{ref:s,className:n()(t,a),...l})});A.displayName="ModalBody";const v=A;var N=t(1602);const w=u.forwardRef((e,s)=>{let{bsPrefix:t,className:a,contentClassName:r,centered:l,size:o,fullscreen:i,children:c,scrollable:d,...u}=e;t=(0,y.oU)(t,"modal");const m=`${t}-dialog`,f="string"===typeof i?`${t}-fullscreen-${i}`:`${t}-fullscreen`;return(0,g.jsx)("div",{...u,ref:s,className:n()(m,a,o&&`${t}-${o}`,l&&`${m}-centered`,d&&`${m}-scrollable`,i&&f),children:(0,g.jsx)("div",{className:n()(`${t}-content`,r),children:c})})});w.displayName="ModalDialog";const $=w,_=u.forwardRef((e,s)=>{let{className:t,bsPrefix:a,as:r="div",...l}=e;return a=(0,y.oU)(a,"modal-footer"),(0,g.jsx)(r,{ref:s,className:n()(t,a),...l})});_.displayName="ModalFooter";const C=_;var S=t(2258);const k=u.forwardRef((e,s)=>{let{bsPrefix:t,className:a,closeLabel:r="Close",closeButton:l=!1,...o}=e;return t=(0,y.oU)(t,"modal-header"),(0,g.jsx)(S.A,{ref:s,...o,className:n()(a,t),closeLabel:r,closeButton:l})});k.displayName="ModalHeader";const R=k;const E=(0,t(4488).A)("h4"),D=u.forwardRef((e,s)=>{let{className:t,bsPrefix:a,as:r=E,...l}=e;return a=(0,y.oU)(a,"modal-title"),(0,g.jsx)(r,{ref:s,className:n()(t,a),...l})});D.displayName="ModalTitle";const T=D;function U(e){return(0,g.jsx)(b.A,{...e,timeout:null})}function H(e){return(0,g.jsx)(b.A,{...e,timeout:null})}const P=u.forwardRef((e,s)=>{let{bsPrefix:t,className:a,style:r,dialogClassName:b,contentClassName:A,children:v,dialogAs:w=$,"data-bs-theme":_,"aria-labelledby":C,"aria-describedby":S,"aria-label":k,show:R=!1,animation:E=!0,backdrop:D=!0,keyboard:T=!0,onEscapeKeyDown:P,onShow:B,onHide:O,container:F,autoFocus:L=!0,enforceFocus:z=!0,restoreFocus:I=!0,restoreFocusOptions:M,onEntered:W,onExit:q,onExiting:K,onEnter:G,onEntering:J,onExited:V,backdropClassName:Z,manager:Q,...X}=e;const[Y,ee]=(0,u.useState)({}),[se,te]=(0,u.useState)(!1),ae=(0,u.useRef)(!1),re=(0,u.useRef)(!1),ne=(0,u.useRef)(null),[le,oe]=(0,u.useState)(null),ie=(0,f.A)(s,oe),ce=(0,m.A)(O),de=(0,y.Wz)();t=(0,y.oU)(t,"modal");const ue=(0,u.useMemo)(()=>({onHide:ce}),[ce]);function me(){return Q||(0,j.R)({isRTL:de})}function fe(e){if(!o.A)return;const s=me().getScrollbarWidth()>0,t=e.scrollHeight>(0,i.A)(e).documentElement.clientHeight;ee({paddingRight:s&&!t?d():void 0,paddingLeft:!s&&t?d():void 0})}const he=(0,m.A)(()=>{le&&fe(le.dialog)});h(()=>{(0,c.A)(window,"resize",he),null==ne.current||ne.current()});const xe=()=>{ae.current=!0},pe=e=>{ae.current&&le&&e.target===le.dialog&&(re.current=!0),ae.current=!1},je=()=>{te(!0),ne.current=(0,x.A)(le.dialog,()=>{te(!1)})},be=e=>{"static"!==D?re.current||e.target!==e.currentTarget?re.current=!1:null==O||O():(e=>{e.target===e.currentTarget&&je()})(e)},ye=(0,u.useCallback)(e=>(0,g.jsx)("div",{...e,className:n()(`${t}-backdrop`,Z,!E&&"show")}),[E,Z,t]),ge={...r,...Y};ge.display="block";return(0,g.jsx)(N.A.Provider,{value:ue,children:(0,g.jsx)(p.A,{show:R,ref:ie,backdrop:D,container:F,keyboard:!0,autoFocus:L,enforceFocus:z,restoreFocus:I,restoreFocusOptions:M,onEscapeKeyDown:e=>{T?null==P||P(e):(e.preventDefault(),"static"===D&&je())},onShow:B,onHide:O,onEnter:(e,s)=>{e&&fe(e),null==G||G(e,s)},onEntering:(e,s)=>{null==J||J(e,s),(0,l.Ay)(window,"resize",he)},onEntered:W,onExit:e=>{null==ne.current||ne.current(),null==q||q(e)},onExiting:K,onExited:e=>{e&&(e.style.display=""),null==V||V(e),(0,c.A)(window,"resize",he)},manager:me(),transition:E?U:void 0,backdropTransition:E?H:void 0,renderBackdrop:ye,renderDialog:e=>(0,g.jsx)("div",{role:"dialog",...e,style:ge,className:n()(a,t,se&&`${t}-static`,!E&&"show"),onClick:D?be:void 0,onMouseUp:pe,"data-bs-theme":_,"aria-label":k,"aria-labelledby":C,"aria-describedby":S,children:(0,g.jsx)(w,{...X,onMouseDown:xe,className:b,contentClassName:A,children:v})})})})});P.displayName="Modal";const B=Object.assign(P,{Body:v,Header:R,Title:T,Footer:C,Dialog:$,TRANSITION_DURATION:300,BACKDROP_TRANSITION_DURATION:150})},3097:(e,s,t)=>{t.r(s),t.d(s,{default:()=>p});var a=t(5043),r=t(3519),n=t(1719),l=t(1072),o=t(8602),i=t(8628),c=t(4196),d=t(4282),u=t(3083),m=t(3722),f=t(4312),h=t(4117),x=t(579);const p=()=>{const{t:e}=(0,h.Bd)(),[s,t]=(0,a.useState)([]),[p,j]=(0,a.useState)(!0),[b,y]=(0,a.useState)(!1),[g,A]=(0,a.useState)(null),[v,N]=(0,a.useState)({name:""}),[w,$]=(0,a.useState)(!1),[_,C]=(0,a.useState)(null),[S,k]=(0,a.useState)({show:!1,type:"",message:""});(0,a.useEffect)(()=>{(async()=>{const e=(0,f.b)();if(!e)return;j(!0);const{data:{user:s}}=await e.auth.getUser();if(!s)return void j(!1);const{data:a,error:r}=await e.from("facilities").select("\n                    id,\n                    name,\n                    created_at,\n                    updated_at\n                ").order("created_at",{ascending:!1});r?console.error("Error fetching facilities:",r):t(a),j(!1)})()},[]);return p?(0,x.jsx)("div",{children:e("loading_facilities")}):(0,x.jsxs)(r.A,{children:[S.show&&(0,x.jsx)(n.A,{variant:S.type,dismissible:!0,onClose:()=>{k({show:!1,type:"",message:""})},className:"mb-4",children:S.message}),(0,x.jsx)("h2",{className:"mb-4",children:e("all_facilities")}),(0,x.jsx)(l.A,{children:(0,x.jsx)(o.A,{children:(0,x.jsx)(i.A,{children:(0,x.jsx)(i.A.Body,{children:(0,x.jsxs)(c.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,x.jsx)("thead",{children:(0,x.jsxs)("tr",{children:[(0,x.jsx)("th",{children:"ID"}),(0,x.jsx)("th",{children:e("name")}),(0,x.jsx)("th",{children:e("created_at")}),(0,x.jsx)("th",{children:e("agent")}),(0,x.jsx)("th",{children:e("actions")})]})}),(0,x.jsx)("tbody",{children:0===s.length?(0,x.jsx)("tr",{children:(0,x.jsx)("td",{colSpan:"9",className:"text-center",children:e("no_facilities_available")})}):s.map(s=>(0,x.jsxs)("tr",{children:[(0,x.jsx)("td",{children:s.id}),(0,x.jsx)("td",{children:s.name}),(0,x.jsx)("td",{children:new Date(s.created_at).toLocaleString()}),(0,x.jsx)("td",{children:new Date(s.updated_at).toLocaleString()}),(0,x.jsxs)("td",{children:[(0,x.jsx)(d.A,{variant:"info",size:"sm",className:"me-2",onClick:()=>(e=>{A(e),N({name:e.name||""}),y(!0)})(s),children:e("edit")}),(0,x.jsx)(d.A,{variant:"danger",size:"sm",onClick:()=>(e=>{C(e),$(!0)})(s),children:e("delete")})]})]},s.id))})]})})})})}),(0,x.jsxs)(u.A,{show:b,onHide:()=>y(!1),size:"lg",children:[(0,x.jsx)(u.A.Header,{closeButton:!0,children:(0,x.jsx)(u.A.Title,{children:e("edit_facility")})}),(0,x.jsx)(u.A.Body,{children:(0,x.jsxs)(m.A,{onSubmit:async a=>{a.preventDefault();const r=(0,f.b)();if(r&&g)try{const{error:a}=await r.from("facilities").update({name:v.name,updated_at:(new Date).toISOString()}).eq("id",g.id);if(a)throw a;t(s.map(e=>e.id===g.id?{...e,...v,updated_at:(new Date).toISOString()}:e)),k({show:!0,type:"success",message:e("item_updated_successfully")}),y(!1),A(null)}catch(n){console.error("Error updating facility:",n),k({show:!0,type:"danger",message:e("failed_to_update_item")+": "+n.message})}},children:[(0,x.jsxs)(m.A.Group,{className:"mb-3",children:[(0,x.jsx)(m.A.Label,{children:e("name")}),(0,x.jsx)(m.A.Control,{type:"text",name:"name",value:v.name,onChange:e=>{const{name:s,value:t}=e.target;N(e=>({...e,[s]:t}))},required:!0})]}),(0,x.jsxs)("div",{className:"d-flex justify-content-end",children:[(0,x.jsx)(d.A,{variant:"secondary",className:"me-2",onClick:()=>y(!1),children:e("cancel")}),(0,x.jsx)(d.A,{variant:"primary",type:"submit",children:e("save_changes")})]})]})})]}),(0,x.jsxs)(u.A,{show:w,onHide:()=>$(!1),children:[(0,x.jsx)(u.A.Header,{closeButton:!0,children:(0,x.jsx)(u.A.Title,{children:e("confirm_delete")})}),(0,x.jsxs)(u.A.Body,{children:[(0,x.jsx)("p",{children:e("delete_confirmation")}),_&&(0,x.jsx)("p",{children:(0,x.jsxs)("strong",{children:[e("name"),": ",_.name]})})]}),(0,x.jsxs)(u.A.Footer,{children:[(0,x.jsx)(d.A,{variant:"secondary",onClick:()=>$(!1),children:e("cancel")}),(0,x.jsx)(d.A,{variant:"danger",onClick:async()=>{const a=(0,f.b)();if(a&&_)try{const{error:r}=await a.from("facilities").delete().eq("id",_.id);if(r)throw r;t(s.filter(e=>e.id!==_.id)),k({show:!0,type:"success",message:e("item_deleted_successfully")}),$(!1),C(null)}catch(r){console.error("Error deleting facility:",r),k({show:!0,type:"danger",message:e("failed_to_delete_item")+": "+r.message})}},children:e("confirm")})]})]})]})}},4196:(e,s,t)=>{t.d(s,{A:()=>c});var a=t(8139),r=t.n(a),n=t(5043),l=t(7852),o=t(579);const i=n.forwardRef((e,s)=>{let{bsPrefix:t,className:a,striped:n,bordered:i,borderless:c,hover:d,size:u,variant:m,responsive:f,...h}=e;const x=(0,l.oU)(t,"table"),p=r()(a,x,m&&`${x}-${m}`,u&&`${x}-${u}`,n&&`${x}-${"string"===typeof n?`striped-${n}`:"striped"}`,i&&`${x}-bordered`,c&&`${x}-borderless`,d&&`${x}-hover`),j=(0,o.jsx)("table",{...h,className:p,ref:s});if(f){let e=`${x}-responsive`;return"string"===typeof f&&(e=`${e}-${f}`),(0,o.jsx)("div",{className:e,children:j})}return j});i.displayName="Table";const c=i}}]);
//# sourceMappingURL=97.3aa3450e.chunk.js.map