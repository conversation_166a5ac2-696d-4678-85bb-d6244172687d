import React, { useState, useEffect } from 'react';
import { Contain<PERSON>, <PERSON>, Col, Card, Button, Alert } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { getSupabase } from '../../supabaseClient';

const DebugAgent = () => {
    const { t } = useTranslation();
    const [debugInfo, setDebugInfo] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchDebugData = async () => {
            const supabase = getSupabase();
            if (!supabase) {
                setDebugInfo({ error: 'Supabase not initialized' });
                setLoading(false);
                return;
            }

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setDebugInfo({ error: 'User not logged in' });
                setLoading(false);
                return;
            }

            const info = {
                userId: user.id,
                userEmail: user.email,
                roleFromLocalStorage: localStorage.getItem('user_role'),
                userMetadata: user.user_metadata,
            };

            // Check users table
            try {
                const { data: userData, error: userError } = await supabase
                    .from('users')
                    .select('*')
                    .eq('id', user.id)
                    .single();
                
                info.usersTableData = userData;
                info.usersTableError = userError;
            } catch (err) {
                info.usersTableError = err;
            }

            // Check agent_profiles table
            try {
                const { data: agentData, error: agentError } = await supabase
                    .from('agent_profiles')
                    .select('*')
                    .eq('user_id', user.id)
                    .single();
                
                info.agentProfileData = agentData;
                info.agentProfileError = agentError;
            } catch (err) {
                info.agentProfileError = err;
            }

            // Check maker_profiles table (in case user is also a maker)
            try {
                const { data: makerData, error: makerError } = await supabase
                    .from('maker_profiles')
                    .select('*')
                    .eq('user_id', user.id)
                    .single();
                
                info.makerProfileData = makerData;
                info.makerProfileError = makerError;
            } catch (err) {
                info.makerProfileError = err;
            }

            setDebugInfo(info);
            setLoading(false);
        };

        fetchDebugData();
    }, []);

    const createAgentProfile = async () => {
        const supabase = getSupabase();
        if (!supabase || !debugInfo) return;

        try {
            const { data, error } = await supabase
                .from('agent_profiles')
                .insert([
                    {
                        user_id: debugInfo.userId,
                        brand_name: 'Default Agent',
                        commission_pct: 0.05,
                        kyc_status: 'pending'
                    }
                ])
                .select()
                .single();

            if (error) {
                alert('Error creating agent profile: ' + error.message);
            } else {
                alert('Agent profile created successfully!');
                window.location.reload();
            }
        } catch (err) {
            alert('Error: ' + err.message);
        }
    };

    if (loading) {
        return <div>Loading debug information...</div>;
    }

    return (
        <Container fluid>
            <Row className="mb-3">
                <Col>
                    <h2>Agent Debug Information</h2>
                </Col>
            </Row>

            {debugInfo && (
                <Row>
                    <Col>
                        <Card className="mb-3">
                            <Card.Header>User Information</Card.Header>
                            <Card.Body>
                                <p><strong>User ID:</strong> {debugInfo.userId}</p>
                                <p><strong>Email:</strong> {debugInfo.userEmail}</p>
                                <p><strong>Role from localStorage:</strong> {debugInfo.roleFromLocalStorage}</p>
                                <p><strong>User Metadata:</strong> {JSON.stringify(debugInfo.userMetadata, null, 2)}</p>
                            </Card.Body>
                        </Card>

                        <Card className="mb-3">
                            <Card.Header>Users Table Data</Card.Header>
                            <Card.Body>
                                {debugInfo.usersTableError ? (
                                    <Alert variant="danger">
                                        Error: {JSON.stringify(debugInfo.usersTableError, null, 2)}
                                    </Alert>
                                ) : (
                                    <pre>{JSON.stringify(debugInfo.usersTableData, null, 2)}</pre>
                                )}
                            </Card.Body>
                        </Card>

                        <Card className="mb-3">
                            <Card.Header>Agent Profile Data</Card.Header>
                            <Card.Body>
                                {debugInfo.agentProfileError ? (
                                    <Alert variant="danger">
                                        Error: {JSON.stringify(debugInfo.agentProfileError, null, 2)}
                                        {debugInfo.agentProfileError.code === 'PGRST116' && (
                                            <div className="mt-2">
                                                <p>No agent profile found. This is likely the cause of the issue.</p>
                                                <Button onClick={createAgentProfile} variant="primary">
                                                    Create Agent Profile
                                                </Button>
                                            </div>
                                        )}
                                    </Alert>
                                ) : (
                                    <pre>{JSON.stringify(debugInfo.agentProfileData, null, 2)}</pre>
                                )}
                            </Card.Body>
                        </Card>

                        <Card className="mb-3">
                            <Card.Header>Maker Profile Data (for reference)</Card.Header>
                            <Card.Body>
                                {debugInfo.makerProfileError ? (
                                    <Alert variant="warning">
                                        Error: {JSON.stringify(debugInfo.makerProfileError, null, 2)}
                                    </Alert>
                                ) : (
                                    <pre>{JSON.stringify(debugInfo.makerProfileData, null, 2)}</pre>
                                )}
                            </Card.Body>
                        </Card>
                    </Col>
                </Row>
            )}
        </Container>
    );
};

export default DebugAgent;
