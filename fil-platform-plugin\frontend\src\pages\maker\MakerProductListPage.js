
import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Button, Badge } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const MakerProductListPage = () => {
    const { t } = useTranslation();
    const [products, setProducts] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchProducts = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            const { data, error } = await supabase
                .from('products')
                .select(`
                    id,
                    name,
                    category,
                    price,
                    total_shares,
                    sold_shares,
                    is_disabled,
                    review_status,
                    created_at
                `)
                .eq('maker_id', user.id)
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Error fetching products:', error);
            } else {
                setProducts(data);
            }
            setLoading(false);
        };

        fetchProducts();
    }, []);

    if (loading) {
        return <div>{t('loading_products')}</div>;
    }

    return (
        <Container>
                <h2 className="mb-4">{t('my_products')}</h2>
                <Row>
                    <Col>
                        <Card>
                            <Card.Body>
                                <Button variant="success" className="mb-3">{t('add_new_product')}</Button>
                                <Table striped bordered hover responsive>
                                    <thead>
                                        <tr>
                                            <th>{t('product_id')}</th>
                                            <th>{t('product_name_header')}</th>
                                            <th>{t('category')}</th>
                                            <th>{t('price_per_share')}</th>
                                            <th>{t('total_shares')}</th>
                                            <th>{t('sold_shares')}</th>
                                            <th>{t('status')}</th>
                                            <th>{t('review_status')}</th>
                                            <th>{t('created_at')}</th>
                                            <th>{t('actions')}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {products.length === 0 ? (
                                            <tr>
                                                <td colSpan="10" className="text-center">{t('no_products_available')}</td>
                                            </tr>
                                        ) : (
                                            products.map(product => (
                                                <tr key={product.id}>
                                                    <td>{product.id.substring(0, 8)}...</td>
                                                    <td>{product.name}</td>
                                                    <td><Badge bg={product.category === 'spot' ? 'success' : 'primary'}>{product.category === 'spot' ? t('spot_category') : t('futures_category')}</Badge></td>
                                                    <td>{product.price}</td>
                                                    <td>{product.total_shares}</td>
                                                    <td>{product.sold_shares}</td>
                                                    <td><Badge bg={product.is_disabled ? 'danger' : 'success'}>{product.is_disabled ? t('disabled') : t('enabled')}</Badge></td>
                                                    <td><Badge bg={product.review_status === 'approved' ? 'success' : 'warning'}>{product.review_status}</Badge></td>
                                                    <td>{new Date(product.created_at).toLocaleString()}</td>
                                                    <td>
                                                        <Button variant="info" size="sm" className="me-2">{t('edit')}</Button>
                                                        <Button variant="danger" size="sm">{t('delete')}</Button>
                                                    </td>
                                                </tr>
                                            ))
                                        )}
                                    </tbody>
                                </Table>
                            </Card.Body>
                        </Card>
                    </Col>
                </Row>     
        </Container>
    );
};

export default MakerProductListPage;
