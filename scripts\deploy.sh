#!/bin/bash

# Filfox Scraper Deployment Script
# This script helps deploy the filfox scraper on a Linux server

set -e

echo "=== Filfox Scraper Deployment Script ==="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root for security reasons"
   exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 16+ first."
    print_status "You can install Node.js using:"
    echo "curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -"
    echo "sudo apt-get install -y nodejs"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    print_error "Node.js version 16+ is required. Current version: $(node -v)"
    exit 1
fi

print_status "Node.js version: $(node -v) ✓"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed"
    exit 1
fi

print_status "npm version: $(npm -v) ✓"

# Install dependencies
print_status "Installing Node.js dependencies..."
npm install

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_warning ".env file not found. Creating from template..."
    cp .env.example .env
    print_warning "Please edit .env file with your Supabase credentials:"
    echo "  - SUPABASE_URL"
    echo "  - SUPABASE_SERVICE_KEY"
    echo ""
    read -p "Press Enter to continue after editing .env file..."
fi

# Validate environment variables
print_status "Validating environment variables..."
source .env

if [ -z "$SUPABASE_URL" ] || [ "$SUPABASE_URL" = "your_supabase_project_url" ]; then
    print_error "SUPABASE_URL is not set in .env file"
    exit 1
fi

if [ -z "$SUPABASE_SERVICE_KEY" ] || [ "$SUPABASE_SERVICE_KEY" = "your_supabase_service_role_key" ]; then
    print_error "SUPABASE_SERVICE_KEY is not set in .env file"
    exit 1
fi

print_status "Environment variables validated ✓"

# Test the scraper
print_status "Testing scraper functionality..."
if node test-scraper.js; then
    print_status "Scraper test passed ✓"
else
    print_warning "Scraper test failed. The scraper might still work, but please check the logs."
fi

# Ask user for deployment method
echo ""
echo "Choose deployment method:"
echo "1) Run as systemd service (recommended for production)"
echo "2) Run with Docker"
echo "3) Run manually"
read -p "Enter your choice (1-3): " choice

case $choice in
    1)
        print_status "Setting up systemd service..."
        
        # Create service file
        SERVICE_FILE="/etc/systemd/system/filfox-scraper.service"
        CURRENT_DIR=$(pwd)
        CURRENT_USER=$(whoami)
        
        sudo tee $SERVICE_FILE > /dev/null <<EOF
[Unit]
Description=Filfox Data Scraper Service
Documentation=https://github.com/your-repo/filfox-scraper
After=network.target
Wants=network.target

[Service]
Type=simple
User=$CURRENT_USER
Group=$CURRENT_USER
WorkingDirectory=$CURRENT_DIR
ExecStart=/usr/bin/node scheduler.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=TZ=Asia/Tokyo
EnvironmentFile=$CURRENT_DIR/.env

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=filfox-scraper

# Security settings
NoNewPrivileges=true
PrivateTmp=true

[Install]
WantedBy=multi-user.target
EOF

        # Reload systemd and start service
        sudo systemctl daemon-reload
        sudo systemctl enable filfox-scraper
        sudo systemctl start filfox-scraper
        
        print_status "Systemd service created and started ✓"
        print_status "Service status:"
        sudo systemctl status filfox-scraper --no-pager
        
        echo ""
        print_status "Useful commands:"
        echo "  - Check status: sudo systemctl status filfox-scraper"
        echo "  - View logs: sudo journalctl -u filfox-scraper -f"
        echo "  - Stop service: sudo systemctl stop filfox-scraper"
        echo "  - Restart service: sudo systemctl restart filfox-scraper"
        ;;
        
    2)
        print_status "Setting up Docker deployment..."
        
        if ! command -v docker &> /dev/null; then
            print_error "Docker is not installed. Please install Docker first."
            exit 1
        fi
        
        # Build and run with Docker Compose
        if command -v docker-compose &> /dev/null; then
            print_status "Using Docker Compose..."
            docker-compose up -d
            print_status "Docker container started ✓"
            print_status "View logs: docker-compose logs -f"
        else
            print_status "Using Docker directly..."
            docker build -t filfox-scraper .
            docker run -d --name filfox-scraper --env-file .env --restart unless-stopped filfox-scraper
            print_status "Docker container started ✓"
            print_status "View logs: docker logs -f filfox-scraper"
        fi
        ;;
        
    3)
        print_status "Manual deployment selected"
        print_status "You can run the scraper manually with:"
        echo "  - Test: node test-scraper.js"
        echo "  - Single run: node filfox-scraper.js"
        echo "  - Scheduled: node scheduler.js"
        ;;
        
    *)
        print_error "Invalid choice"
        exit 1
        ;;
esac

echo ""
print_status "=== Deployment completed successfully! ==="
print_status "The scraper is scheduled to run daily at 2:00 AM JST"
print_status "Monitor the logs to ensure it's working correctly"
