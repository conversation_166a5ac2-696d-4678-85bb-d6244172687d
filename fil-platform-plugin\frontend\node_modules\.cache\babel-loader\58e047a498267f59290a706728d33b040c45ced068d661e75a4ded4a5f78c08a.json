{"ast": null, "code": "import React,{Suspense,useEffect,useState}from'react';import{initSupabase}from'./supabaseClient';import{HashRouter,Routes,Route,Link,Navigate,useLocation}from'react-router-dom';import{Container,Navbar,Nav,NavDropdown}from'react-bootstrap';import{useTranslation}from'react-i18next';import{FaTachometerAlt,FaHardHat,FaGlobe,FaCoins,FaChartBar,FaFileInvoiceDollar,FaUsers,FaShoppingBag,FaYenSign}from'react-icons/fa';// Lazy load components for better performance\nimport{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const LoginPage=/*#__PURE__*/React.lazy(()=>import('./pages/LoginPage'));const CustomerDashboard=/*#__PURE__*/React.lazy(()=>import('./pages/customer/Dashboard'));const ProductListPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/ProductListPage'));const OrderListPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/OrderListPage'));const WalletPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/WalletPage'));const MyAccountPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/MyAccountPage'));const MyGainsPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/MyGainsPage'));const KycPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/KycPage'));const RecommendPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/RecommendPage'));const AgentDashboard=/*#__PURE__*/React.lazy(()=>import('./pages/agent/Dashboard'));const Members=/*#__PURE__*/React.lazy(()=>import('./pages/agent/Members'));const Recommend=/*#__PURE__*/React.lazy(()=>import('./pages/agent/Recommend'));const AgentProductListPage=/*#__PURE__*/React.lazy(()=>import('./pages/agent/AgentProductListPage'));const DebugAgent=/*#__PURE__*/React.lazy(()=>import('./pages/agent/DebugAgent'));const MakerDashboard=/*#__PURE__*/React.lazy(()=>import('./pages/maker/Dashboard'));const MakerProductListPage=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MakerProductListPage'));const MakerOrderListPage=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MakerOrderListPage'));const MakerFacilityListPage=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MakerFacilities'));const MakerMinerListPage=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MakerMiners'));const MinerEarnings=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MinerEarnings'));const MinerSnapshots=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MinerSnapshots'));const Transactions=/*#__PURE__*/React.lazy(()=>import('./pages/maker/Transactions'));const CoinBatches=/*#__PURE__*/React.lazy(()=>import('./pages/maker/CoinBatches'));const NetworkStats=/*#__PURE__*/React.lazy(()=>import('./pages/maker/NetworkStats'));const CustomerAssets=/*#__PURE__*/React.lazy(()=>import('./pages/maker/CustomerAssets'));const OrderReports=/*#__PURE__*/React.lazy(()=>import('./pages/maker/OrderReports'));const OrderDistributions=/*#__PURE__*/React.lazy(()=>import('./pages/maker/OrderDistributions'));const CapacityRequest=/*#__PURE__*/React.lazy(()=>import('./pages/maker/CapacityRequest'));const ManualDeposits=/*#__PURE__*/React.lazy(()=>import('./pages/maker/ManualDeposits'));const AgentCapacityRequest=/*#__PURE__*/React.lazy(()=>import('./pages/agent/CapacityRequest'));const AgentNetworkStats=/*#__PURE__*/React.lazy(()=>import('./pages/agent/NetworkStats'));function App(){const{t,i18n}=useTranslation();const[supabase,setSupabase]=useState(null);const[session,setSession]=useState(null);const[loading,setLoading]=useState(true);const role=localStorage.getItem('user_role');// 从 localStorage 读取用户角色\nuseEffect(()=>{const initialize=async()=>{const supa=await initSupabase();setSupabase(supa);const{data:{session}}=await supa.auth.getSession();setSession(session);supa.auth.onAuthStateChange((_event,newSession)=>{setSession(newSession);if(!newSession){localStorage.removeItem('user_role');}});setLoading(false);};initialize();},[]);const changeLanguage=lng=>{i18n.changeLanguage(lng);};// Debug: Log current URL and hash\nReact.useEffect(()=>{console.log('App mounted. Current URL:',window.location.href);console.log('Hash:',window.location.hash);},[]);// Require login to access protected pages\nconst RequireAuth=_ref=>{let{children}=_ref;const location=useLocation();if(!session){return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",state:{from:location},replace:true});}return children;};// Auto redirect from \"/\" based on role\nconst RoleRedirect=()=>{const role=localStorage.getItem('user_role');if(role==='maker')return/*#__PURE__*/_jsx(Navigate,{to:\"/maker\",replace:true});if(role==='agent')return/*#__PURE__*/_jsx(Navigate,{to:\"/agent\",replace:true});if(role==='customer')return/*#__PURE__*/_jsx(Navigate,{to:\"/customer\",replace:true});return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true});// default to login page\n};return/*#__PURE__*/_jsx(HashRouter,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Navbar,{bg:\"dark\",variant:\"dark\",expand:\"lg\",children:/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(Navbar.Toggle,{\"aria-controls\":\"basic-navbar-nav\"}),/*#__PURE__*/_jsxs(Navbar.Collapse,{id:\"basic-navbar-nav\",children:[/*#__PURE__*/_jsxs(Nav,{className:\"me-auto\",children:[role==='maker'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaHardHat,{className:\"me-1\"}),t('miner_management')]}),id:\"maker-miner-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/miners\",children:t('miner_list')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/facilities\",children:t('facility_list')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/earnings\",children:t('earnings_list')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/transfers\",children:t('transfer_list')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/snapshots\",children:t('daily_snapshot')})]}),/*#__PURE__*/_jsxs(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaGlobe,{className:\"me-1\"}),t('operations_management')]}),id:\"maker-operations-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/capacity\",children:t('capacity_expansion_request')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/orders\",children:t('maker_orders')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/manual-deposits\",children:t('manual_deposit')})]}),/*#__PURE__*/_jsxs(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaCoins,{className:\"me-1\"}),t('coin_management')]}),id:\"maker-coin-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/coin-batches\",children:t('coin_batches')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/network-stats\",children:t('network_stats')})]}),/*#__PURE__*/_jsxs(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaChartBar,{className:\"me-1\"}),t('report_management')]}),id:\"maker-report-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/customer-assets\",children:t('customer_assets')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/order-reports\",children:t('order_reports')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/order-distributions\",children:t('order_distributions')})]})]}),role==='agent'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaGlobe,{className:\"me-1\"}),t('operational_settings')]}),id:\"agent-operational-dropdown\",children:/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/agent/power\",children:t('power_records')})}),/*#__PURE__*/_jsx(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaFileInvoiceDollar,{className:\"me-1\"}),t('profit_management')]}),id:\"agent-profit-dropdown\",children:/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/agent/profit\",children:t('profit_records')})}),/*#__PURE__*/_jsxs(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaUsers,{className:\"me-1\"}),t('member_management')]}),id:\"agent-member-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/agent/member-list\",children:t('member_list')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/agent/recommendation\",children:t('recommendation')})]}),/*#__PURE__*/_jsxs(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaShoppingBag,{className:\"me-1\"}),t('product_management')]}),id:\"agent-product-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/agent/product-list\",children:t('product_list')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/agent/onsale-list\",children:t('onsale_list')})]}),/*#__PURE__*/_jsxs(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaYenSign,{className:\"me-1\"}),t('finance_management')]}),id:\"agent-finance-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/agent/wallet-flow-list\",children:t('wallet_flow')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/agent/withdraw-list\",children:t('withdraw_list')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/agent/order-list\",children:t('order_list')})]})]}),/*#__PURE__*/_jsxs(Nav.Link,{href:\"#/\",children:[/*#__PURE__*/_jsx(FaTachometerAlt,{className:\"me-1\"}),t('dashboard')]})]}),/*#__PURE__*/_jsx(Nav,{children:/*#__PURE__*/_jsxs(NavDropdown,{title:t('language'),id:\"basic-nav-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{onClick:()=>changeLanguage('ja'),children:\"\\u65E5\\u672C\\u8A9E\"}),/*#__PURE__*/_jsx(NavDropdown.Item,{onClick:()=>changeLanguage('zh'),children:\"\\u4E2D\\u6587\"}),/*#__PURE__*/_jsx(NavDropdown.Item,{onClick:()=>changeLanguage('en'),children:\"English\"})]})})]})]})}),/*#__PURE__*/_jsx(Container,{className:\"mt-4\",children:/*#__PURE__*/_jsx(Suspense,{fallback:/*#__PURE__*/_jsx(\"div\",{children:t('loading')}),children:loading?/*#__PURE__*/_jsx(\"div\",{children:t('initializing_platform')}):!supabase?/*#__PURE__*/_jsx(\"div\",{className:\"alert alert-danger\",children:t('backend_connection_failed')}):/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(LoginPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(RoleRedirect,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/customer\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(CustomerDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/products\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(ProductListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/orders\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(OrderListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/wallet\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(WalletPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/my\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MyAccountPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/my-gains\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MyGainsPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/my/kyc\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(KycPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/my/recommend\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(RecommendPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(AgentDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/member-list\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(Members,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/recommendation\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(Recommend,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/products\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(AgentProductListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/debug\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(DebugAgent,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/power\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(AgentCapacityRequest,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/profit\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(AgentNetworkStats,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MakerDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/products\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MakerProductListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/orders\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MakerOrderListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/facilities\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MakerFacilityListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/miners\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MakerMinerListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/earnings\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MinerEarnings,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/transfers\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(Transactions,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/snapshots\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MinerSnapshots,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/coin-batches\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(CoinBatches,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/network-stats\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(NetworkStats,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/customer-assets\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(CustomerAssets,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/order-reports\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(OrderReports,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/order-distributions\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(OrderDistributions,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/capacity\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(CapacityRequest,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/manual-deposits\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(ManualDeposits,{})})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/\",replace:true})})]})})})]})});}export default App;", "map": {"version": 3, "names": ["React", "Suspense", "useEffect", "useState", "initSupabase", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "Link", "Navigate", "useLocation", "Container", "<PERSON><PERSON><PERSON>", "Nav", "NavDropdown", "useTranslation", "FaTachometerAlt", "FaHardHat", "FaGlobe", "FaCoins", "FaChartBar", "FaFileInvoiceDollar", "FaUsers", "FaShoppingBag", "FaYenSign", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "LoginPage", "lazy", "CustomerDashboard", "ProductListPage", "OrderListPage", "WalletPage", "MyAccountPage", "MyGainsPage", "KycPage", "RecommendPage", "AgentDashboard", "Members", "Recommend", "AgentProductListPage", "DebugAgent", "MakerDashboard", "MakerProductListPage", "MakerOrderListPage", "MakerFacilityListPage", "MakerMinerListPage", "<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "MinerSnapshots", "Transactions", "CoinBatches", "NetworkStats", "CustomerAssets", "OrderReports", "OrderDistributions", "CapacityRequest", "ManualDeposits", "AgentCapacityRequest", "AgentNetworkStats", "App", "t", "i18n", "supabase", "set<PERSON><PERSON><PERSON><PERSON>", "session", "setSession", "loading", "setLoading", "role", "localStorage", "getItem", "initialize", "supa", "data", "auth", "getSession", "onAuthStateChange", "_event", "newSession", "removeItem", "changeLanguage", "lng", "console", "log", "window", "location", "href", "hash", "RequireAuth", "_ref", "children", "to", "state", "from", "replace", "RoleRedirect", "bg", "variant", "expand", "Toggle", "Collapse", "id", "className", "title", "<PERSON><PERSON>", "as", "onClick", "fallback", "path", "element"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/App.js"], "sourcesContent": ["import React, { Suspense, useEffect, useState } from 'react';\nimport { initSupabase } from './supabaseClient';\nimport {\n  HashRouter,\n  Routes,\n  Route,\n  Link,\n  Navigate,\n  useLocation,\n} from 'react-router-dom';\nimport { Container, Navbar, Nav, NavDropdown } from 'react-bootstrap';\nimport { useTranslation } from 'react-i18next';\nimport { FaTachometerAlt, FaHardHat, FaGlobe, FaCoins, FaChartBar, FaFileInvoiceDollar, FaUsers, FaShoppingBag, FaYenSign } from 'react-icons/fa';\n\n// Lazy load components for better performance\nconst LoginPage = React.lazy(() => import('./pages/LoginPage'));\nconst CustomerDashboard = React.lazy(() => import('./pages/customer/Dashboard'));\nconst ProductListPage = React.lazy(() => import('./pages/customer/ProductListPage'));\nconst OrderListPage = React.lazy(() => import('./pages/customer/OrderListPage'));\nconst WalletPage = React.lazy(() => import('./pages/customer/WalletPage'));\nconst MyAccountPage = React.lazy(() => import('./pages/customer/MyAccountPage'));\nconst MyGainsPage = React.lazy(() => import('./pages/customer/MyGainsPage'));\nconst KycPage = React.lazy(() => import('./pages/customer/KycPage'));\nconst RecommendPage = React.lazy(() => import('./pages/customer/RecommendPage'));\nconst AgentDashboard = React.lazy(() => import('./pages/agent/Dashboard'));\nconst Members = React.lazy(() => import('./pages/agent/Members'));\nconst Recommend = React.lazy(() => import('./pages/agent/Recommend'));\nconst AgentProductListPage = React.lazy(() => import('./pages/agent/AgentProductListPage'));\nconst DebugAgent = React.lazy(() => import('./pages/agent/DebugAgent'));\nconst MakerDashboard = React.lazy(() => import('./pages/maker/Dashboard'));\nconst MakerProductListPage = React.lazy(() => import('./pages/maker/MakerProductListPage'));\nconst MakerOrderListPage = React.lazy(() => import('./pages/maker/MakerOrderListPage'));\nconst MakerFacilityListPage = React.lazy(() => import('./pages/maker/MakerFacilities'));\nconst MakerMinerListPage = React.lazy(() => import('./pages/maker/MakerMiners'));\nconst MinerEarnings = React.lazy(() => import('./pages/maker/MinerEarnings'));\nconst MinerSnapshots = React.lazy(() => import('./pages/maker/MinerSnapshots'));\nconst Transactions = React.lazy(() => import('./pages/maker/Transactions'));\nconst CoinBatches = React.lazy(() => import('./pages/maker/CoinBatches'));\nconst NetworkStats = React.lazy(() => import('./pages/maker/NetworkStats'));\nconst CustomerAssets = React.lazy(() => import('./pages/maker/CustomerAssets'));\nconst OrderReports = React.lazy(() => import('./pages/maker/OrderReports'));\nconst OrderDistributions = React.lazy(() => import('./pages/maker/OrderDistributions'));\nconst CapacityRequest = React.lazy(() => import('./pages/maker/CapacityRequest'));\nconst ManualDeposits = React.lazy(() => import('./pages/maker/ManualDeposits'));\nconst AgentCapacityRequest = React.lazy(() => import('./pages/agent/CapacityRequest'));\nconst AgentNetworkStats = React.lazy(() => import('./pages/agent/NetworkStats'));\n\nfunction App() {\n  const { t, i18n } = useTranslation();\n  const [supabase, setSupabase] = useState(null);\n  const [session, setSession] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const role = localStorage.getItem('user_role'); // 从 localStorage 读取用户角色\n\n  useEffect(() => {\n    const initialize = async () => {\n      const supa = await initSupabase();\n      setSupabase(supa);\n\n      const { data: { session } } = await supa.auth.getSession();\n      setSession(session);\n\n      supa.auth.onAuthStateChange((_event, newSession) => {\n        setSession(newSession);\n        if (!newSession) {\n          localStorage.removeItem('user_role');\n        }\n      });\n\n      setLoading(false);\n    };\n    initialize();\n  }, []);\n\n  const changeLanguage = (lng) => {\n    i18n.changeLanguage(lng);\n  };\n\n  // Debug: Log current URL and hash\n  React.useEffect(() => {\n    console.log('App mounted. Current URL:', window.location.href);\n    console.log('Hash:', window.location.hash);\n  }, []);\n\n  // Require login to access protected pages\n  const RequireAuth = ({ children }) => {\n    const location = useLocation();\n    if (!session) {\n      return <Navigate to=\"/login\" state={{ from: location }} replace />;\n    }\n    return children;\n  };\n\n  // Auto redirect from \"/\" based on role\n  const RoleRedirect = () => {\n    const role = localStorage.getItem('user_role');\n    if (role === 'maker') return <Navigate to=\"/maker\" replace />;\n    if (role === 'agent') return <Navigate to=\"/agent\" replace />;\n    if (role === 'customer') return <Navigate to=\"/customer\" replace />;\n    return <Navigate to=\"/login\" replace />; // default to login page\n  };\n\n  return (\n    <HashRouter>\n      <div>\n        <Navbar bg=\"dark\" variant=\"dark\" expand=\"lg\">\n          <Container>\n            <Navbar.Toggle aria-controls=\"basic-navbar-nav\" />\n            <Navbar.Collapse id=\"basic-navbar-nav\">\n              <Nav className=\"me-auto\">\n                {/* ===== ★ Maker 导航开始 ★ ===== */}\n                {role === 'maker' && (\n                  <>\n                    {/* Miner Management 下拉 */}\n                    <NavDropdown\n                      title={\n                        <>\n                          <FaHardHat className=\"me-1\" />\n                          {t('miner_management')}\n                        </>\n                      }\n                      id=\"maker-miner-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/maker/miners\">\n                        {t('miner_list')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/facilities\">\n                        {t('facility_list')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/earnings\">\n                        {t('earnings_list')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/transfers\">\n                        {t('transfer_list')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/snapshots\">\n                        {t('daily_snapshot')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n\n                    <NavDropdown title={\n                        <>\n                          <FaGlobe className=\"me-1\" />\n                          {t('operations_management')}\n                        </>\n                      }\n                      id=\"maker-operations-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/maker/capacity\">\n                        {t('capacity_expansion_request')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/orders\">\n                        {t('maker_orders')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/manual-deposits\">\n                        {t('manual_deposit')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n\n                    <NavDropdown title={\n                        <>\n                          <FaCoins className=\"me-1\" />\n                          {t('coin_management')}\n                        </>\n                      }\n                      id=\"maker-coin-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/maker/coin-batches\">\n                        {t('coin_batches')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/network-stats\">\n                        {t('network_stats')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n\n                    <NavDropdown title={\n                        <>\n                          <FaChartBar className=\"me-1\" />\n                          {t('report_management')}\n                        </>\n                      }\n                      id=\"maker-report-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/maker/customer-assets\">\n                        {t('customer_assets')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/order-reports\">\n                        {t('order_reports')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/order-distributions\">\n                        {t('order_distributions')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n                  </>\n                )}\n                {/* ===== ★ Agent 导航开始 ★ ===== */}\n                {role === 'agent' && (\n                  <>\n                    {/* Agent Management 下拉 */}\n                    <NavDropdown\n                      title={\n                        <>\n                          <FaGlobe className=\"me-1\" />\n                          {t('operational_settings')}\n                        </>\n                      }\n                      id=\"agent-operational-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/agent/power\">\n                        {t('power_records')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n\n                    <NavDropdown title={\n                        <>\n                          <FaFileInvoiceDollar className=\"me-1\" />\n                          {t('profit_management')}\n                        </>\n                      }\n                      id=\"agent-profit-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/agent/profit\">\n                        {t('profit_records')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n\n                    <NavDropdown title={\n                        <>\n                          <FaUsers className=\"me-1\" />\n                          {t('member_management')}\n                        </>\n                      }\n                      id=\"agent-member-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/agent/member-list\">\n                        {t('member_list')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/agent/recommendation\">\n                        {t('recommendation')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n\n                    <NavDropdown title={\n                        <>\n                          <FaShoppingBag className=\"me-1\" />\n                          {t('product_management')}\n                        </>\n                      }\n                      id=\"agent-product-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/agent/product-list\">\n                        {t('product_list')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/agent/onsale-list\">\n                        {t('onsale_list')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n\n                    <NavDropdown title={\n                        <>\n                          <FaYenSign className=\"me-1\" />\n                          {t('finance_management')}\n                        </>\n                      }\n                      id=\"agent-finance-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/agent/wallet-flow-list\">\n                        {t('wallet_flow')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/agent/withdraw-list\">\n                        {t('withdraw_list')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/agent/order-list\">\n                        {t('order_list')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n                  </>\n                )}\n                {/* ===== ★ Agent 导航结束 ★ ===== */}\n                <Nav.Link href=\"#/\">\n                  <FaTachometerAlt className=\"me-1\" />\n                  {t('dashboard')}\n                </Nav.Link>\n                {/* Add other nav links based on role later */}\n              </Nav>\n              <Nav>\n                <NavDropdown title={t('language')} id=\"basic-nav-dropdown\">\n                  <NavDropdown.Item onClick={() => changeLanguage('ja')}>日本語</NavDropdown.Item>\n                  <NavDropdown.Item onClick={() => changeLanguage('zh')}>中文</NavDropdown.Item>\n                  <NavDropdown.Item onClick={() => changeLanguage('en')}>English</NavDropdown.Item>\n                </NavDropdown>\n              </Nav>\n            </Navbar.Collapse>\n          </Container>\n        </Navbar>\n\n        <Container className=\"mt-4\">\n          <Suspense fallback={<div>{t('loading')}</div>}>\n            {loading ? (\n              <div>{t('initializing_platform')}</div>\n            ) : !supabase ? (\n              <div className=\"alert alert-danger\">{t('backend_connection_failed')}</div>\n            ) : (\n              <Routes>\n                {/* Public Route */}\n                <Route path=\"/login\" element={<LoginPage />} />\n\n                {/* Root path → redirect by role */}\n                <Route path=\"/\" element={<RequireAuth><RoleRedirect /></RequireAuth>} />\n\n                {/* Customer Routes */}\n                <Route path=\"/customer\" element={<RequireAuth><CustomerDashboard /></RequireAuth>} />\n                <Route path=\"/products\" element={<RequireAuth><ProductListPage /></RequireAuth>} />\n                <Route path=\"/orders\" element={<RequireAuth><OrderListPage /></RequireAuth>} />\n                <Route path=\"/wallet\" element={<RequireAuth><WalletPage /></RequireAuth>} />\n                <Route path=\"/my\" element={<RequireAuth><MyAccountPage /></RequireAuth>} />\n                <Route path=\"/my-gains\" element={<RequireAuth><MyGainsPage /></RequireAuth>} />\n                <Route path=\"/my/kyc\" element={<RequireAuth><KycPage /></RequireAuth>} />\n                <Route path=\"/my/recommend\" element={<RequireAuth><RecommendPage /></RequireAuth>} />\n\n                {/* Agent Routes */}\n                <Route path=\"/agent\" element={<RequireAuth><AgentDashboard /></RequireAuth>} />\n                <Route path=\"/agent/member-list\" element={<RequireAuth><Members /></RequireAuth>} />\n                <Route path=\"/agent/recommendation\" element={<RequireAuth><Recommend /></RequireAuth>} />\n                <Route path=\"/agent/products\" element={<RequireAuth><AgentProductListPage /></RequireAuth>} />\n                <Route path=\"/agent/debug\" element={<RequireAuth><DebugAgent /></RequireAuth>} />\n                <Route path=\"/agent/power\" element={<RequireAuth><AgentCapacityRequest /></RequireAuth>} />\n                <Route path=\"/agent/profit\" element={<RequireAuth><AgentNetworkStats /></RequireAuth>} />\n\n                {/* Maker Routes */}\n                  <Route path=\"/maker\" element={<RequireAuth><MakerDashboard /></RequireAuth>} />\n                  <Route path=\"/maker/products\" element={<RequireAuth><MakerProductListPage /></RequireAuth>} />\n                  <Route path=\"/maker/orders\" element={<RequireAuth><MakerOrderListPage /></RequireAuth>} />\n                  <Route path=\"/maker/facilities\" element={<RequireAuth><MakerFacilityListPage /></RequireAuth>} />\n                  <Route path=\"/maker/miners\" element={<RequireAuth><MakerMinerListPage /></RequireAuth>} />\n                  <Route path=\"/maker/earnings\" element={<RequireAuth><MinerEarnings /></RequireAuth>} />\n                  <Route path=\"/maker/transfers\" element={<RequireAuth><Transactions /></RequireAuth>} />\n                  <Route path=\"/maker/snapshots\" element={<RequireAuth><MinerSnapshots /></RequireAuth>} />\n                  <Route path=\"/maker/coin-batches\" element={<RequireAuth><CoinBatches /></RequireAuth>} />\n                  <Route path=\"/maker/network-stats\" element={<RequireAuth><NetworkStats /></RequireAuth>} />\n                  <Route path=\"/maker/customer-assets\" element={<RequireAuth><CustomerAssets /></RequireAuth>} />\n                  <Route path=\"/maker/order-reports\" element={<RequireAuth><OrderReports /></RequireAuth>} />\n                  <Route path=\"/maker/order-distributions\" element={<RequireAuth><OrderDistributions /></RequireAuth>} />\n                  <Route path=\"/maker/capacity\" element={<RequireAuth><CapacityRequest /></RequireAuth>} />\n                  <Route path=\"/maker/manual-deposits\" element={<RequireAuth><ManualDeposits /></RequireAuth>} />\n                \n                {/* Fallback */}\n                <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\n              </Routes>\n            )}\n          </Suspense>\n        </Container>\n      </div>\n    </HashRouter>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAC5D,OAASC,YAAY,KAAQ,kBAAkB,CAC/C,OACEC,UAAU,CACVC,MAAM,CACNC,KAAK,CACLC,IAAI,CACJC,QAAQ,CACRC,WAAW,KACN,kBAAkB,CACzB,OAASC,SAAS,CAAEC,MAAM,CAAEC,GAAG,CAAEC,WAAW,KAAQ,iBAAiB,CACrE,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,eAAe,CAAEC,SAAS,CAAEC,OAAO,CAAEC,OAAO,CAAEC,UAAU,CAAEC,mBAAmB,CAAEC,OAAO,CAAEC,aAAa,CAAEC,SAAS,KAAQ,gBAAgB,CAEjJ;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,SAAS,cAAG/B,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAC/D,KAAM,CAAAC,iBAAiB,cAAGjC,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CAChF,KAAM,CAAAE,eAAe,cAAGlC,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,kCAAkC,CAAC,CAAC,CACpF,KAAM,CAAAG,aAAa,cAAGnC,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,gCAAgC,CAAC,CAAC,CAChF,KAAM,CAAAI,UAAU,cAAGpC,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,6BAA6B,CAAC,CAAC,CAC1E,KAAM,CAAAK,aAAa,cAAGrC,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,gCAAgC,CAAC,CAAC,CAChF,KAAM,CAAAM,WAAW,cAAGtC,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC,CAC5E,KAAM,CAAAO,OAAO,cAAGvC,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,0BAA0B,CAAC,CAAC,CACpE,KAAM,CAAAQ,aAAa,cAAGxC,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,gCAAgC,CAAC,CAAC,CAChF,KAAM,CAAAS,cAAc,cAAGzC,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC,CAC1E,KAAM,CAAAU,OAAO,cAAG1C,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,uBAAuB,CAAC,CAAC,CACjE,KAAM,CAAAW,SAAS,cAAG3C,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC,CACrE,KAAM,CAAAY,oBAAoB,cAAG5C,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,oCAAoC,CAAC,CAAC,CAC3F,KAAM,CAAAa,UAAU,cAAG7C,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,0BAA0B,CAAC,CAAC,CACvE,KAAM,CAAAc,cAAc,cAAG9C,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC,CAC1E,KAAM,CAAAe,oBAAoB,cAAG/C,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,oCAAoC,CAAC,CAAC,CAC3F,KAAM,CAAAgB,kBAAkB,cAAGhD,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,kCAAkC,CAAC,CAAC,CACvF,KAAM,CAAAiB,qBAAqB,cAAGjD,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,+BAA+B,CAAC,CAAC,CACvF,KAAM,CAAAkB,kBAAkB,cAAGlD,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,2BAA2B,CAAC,CAAC,CAChF,KAAM,CAAAmB,aAAa,cAAGnD,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,6BAA6B,CAAC,CAAC,CAC7E,KAAM,CAAAoB,cAAc,cAAGpD,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC,CAC/E,KAAM,CAAAqB,YAAY,cAAGrD,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CAC3E,KAAM,CAAAsB,WAAW,cAAGtD,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,2BAA2B,CAAC,CAAC,CACzE,KAAM,CAAAuB,YAAY,cAAGvD,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CAC3E,KAAM,CAAAwB,cAAc,cAAGxD,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC,CAC/E,KAAM,CAAAyB,YAAY,cAAGzD,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CAC3E,KAAM,CAAA0B,kBAAkB,cAAG1D,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,kCAAkC,CAAC,CAAC,CACvF,KAAM,CAAA2B,eAAe,cAAG3D,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,+BAA+B,CAAC,CAAC,CACjF,KAAM,CAAA4B,cAAc,cAAG5D,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC,CAC/E,KAAM,CAAA6B,oBAAoB,cAAG7D,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,+BAA+B,CAAC,CAAC,CACtF,KAAM,CAAA8B,iBAAiB,cAAG9D,KAAK,CAACgC,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CAEhF,QAAS,CAAA+B,GAAGA,CAAA,CAAG,CACb,KAAM,CAAEC,CAAC,CAAEC,IAAK,CAAC,CAAGlD,cAAc,CAAC,CAAC,CACpC,KAAM,CAACmD,QAAQ,CAAEC,WAAW,CAAC,CAAGhE,QAAQ,CAAC,IAAI,CAAC,CAC9C,KAAM,CAACiE,OAAO,CAAEC,UAAU,CAAC,CAAGlE,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACmE,OAAO,CAAEC,UAAU,CAAC,CAAGpE,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAAqE,IAAI,CAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAAE;AAEhDxE,SAAS,CAAC,IAAM,CACd,KAAM,CAAAyE,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAxE,YAAY,CAAC,CAAC,CACjC+D,WAAW,CAACS,IAAI,CAAC,CAEjB,KAAM,CAAEC,IAAI,CAAE,CAAET,OAAQ,CAAE,CAAC,CAAG,KAAM,CAAAQ,IAAI,CAACE,IAAI,CAACC,UAAU,CAAC,CAAC,CAC1DV,UAAU,CAACD,OAAO,CAAC,CAEnBQ,IAAI,CAACE,IAAI,CAACE,iBAAiB,CAAC,CAACC,MAAM,CAAEC,UAAU,GAAK,CAClDb,UAAU,CAACa,UAAU,CAAC,CACtB,GAAI,CAACA,UAAU,CAAE,CACfT,YAAY,CAACU,UAAU,CAAC,WAAW,CAAC,CACtC,CACF,CAAC,CAAC,CAEFZ,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CACDI,UAAU,CAAC,CAAC,CACd,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAS,cAAc,CAAIC,GAAG,EAAK,CAC9BpB,IAAI,CAACmB,cAAc,CAACC,GAAG,CAAC,CAC1B,CAAC,CAED;AACArF,KAAK,CAACE,SAAS,CAAC,IAAM,CACpBoF,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAEC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC,CAC9DJ,OAAO,CAACC,GAAG,CAAC,OAAO,CAAEC,MAAM,CAACC,QAAQ,CAACE,IAAI,CAAC,CAC5C,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAC,WAAW,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CAC/B,KAAM,CAAAJ,QAAQ,CAAG/E,WAAW,CAAC,CAAC,CAC9B,GAAI,CAAC0D,OAAO,CAAE,CACZ,mBAAO1C,IAAA,CAACjB,QAAQ,EAACsF,EAAE,CAAC,QAAQ,CAACC,KAAK,CAAE,CAAEC,IAAI,CAAER,QAAS,CAAE,CAACS,OAAO,MAAE,CAAC,CACpE,CACA,MAAO,CAAAJ,QAAQ,CACjB,CAAC,CAED;AACA,KAAM,CAAAK,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAA3B,IAAI,CAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAC9C,GAAIF,IAAI,GAAK,OAAO,CAAE,mBAAO9C,IAAA,CAACjB,QAAQ,EAACsF,EAAE,CAAC,QAAQ,CAACG,OAAO,MAAE,CAAC,CAC7D,GAAI1B,IAAI,GAAK,OAAO,CAAE,mBAAO9C,IAAA,CAACjB,QAAQ,EAACsF,EAAE,CAAC,QAAQ,CAACG,OAAO,MAAE,CAAC,CAC7D,GAAI1B,IAAI,GAAK,UAAU,CAAE,mBAAO9C,IAAA,CAACjB,QAAQ,EAACsF,EAAE,CAAC,WAAW,CAACG,OAAO,MAAE,CAAC,CACnE,mBAAOxE,IAAA,CAACjB,QAAQ,EAACsF,EAAE,CAAC,QAAQ,CAACG,OAAO,MAAE,CAAC,CAAE;AAC3C,CAAC,CAED,mBACExE,IAAA,CAACrB,UAAU,EAAAyF,QAAA,cACThE,KAAA,QAAAgE,QAAA,eACEpE,IAAA,CAACd,MAAM,EAACwF,EAAE,CAAC,MAAM,CAACC,OAAO,CAAC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAAR,QAAA,cAC1ChE,KAAA,CAACnB,SAAS,EAAAmF,QAAA,eACRpE,IAAA,CAACd,MAAM,CAAC2F,MAAM,EAAC,gBAAc,kBAAkB,CAAE,CAAC,cAClDzE,KAAA,CAAClB,MAAM,CAAC4F,QAAQ,EAACC,EAAE,CAAC,kBAAkB,CAAAX,QAAA,eACpChE,KAAA,CAACjB,GAAG,EAAC6F,SAAS,CAAC,SAAS,CAAAZ,QAAA,EAErBtB,IAAI,GAAK,OAAO,eACf1C,KAAA,CAAAF,SAAA,EAAAkE,QAAA,eAEEhE,KAAA,CAAChB,WAAW,EACV6F,KAAK,cACH7E,KAAA,CAAAF,SAAA,EAAAkE,QAAA,eACEpE,IAAA,CAACT,SAAS,EAACyF,SAAS,CAAC,MAAM,CAAE,CAAC,CAC7B1C,CAAC,CAAC,kBAAkB,CAAC,EACtB,CACH,CACDyC,EAAE,CAAC,sBAAsB,CAAAX,QAAA,eAEzBpE,IAAA,CAACZ,WAAW,CAAC8F,IAAI,EAACC,EAAE,CAAErG,IAAK,CAACuF,EAAE,CAAC,eAAe,CAAAD,QAAA,CAC3C9B,CAAC,CAAC,YAAY,CAAC,CACA,CAAC,cACnBtC,IAAA,CAACZ,WAAW,CAAC8F,IAAI,EAACC,EAAE,CAAErG,IAAK,CAACuF,EAAE,CAAC,mBAAmB,CAAAD,QAAA,CAC/C9B,CAAC,CAAC,eAAe,CAAC,CACH,CAAC,cACnBtC,IAAA,CAACZ,WAAW,CAAC8F,IAAI,EAACC,EAAE,CAAErG,IAAK,CAACuF,EAAE,CAAC,iBAAiB,CAAAD,QAAA,CAC7C9B,CAAC,CAAC,eAAe,CAAC,CACH,CAAC,cACnBtC,IAAA,CAACZ,WAAW,CAAC8F,IAAI,EAACC,EAAE,CAAErG,IAAK,CAACuF,EAAE,CAAC,kBAAkB,CAAAD,QAAA,CAC9C9B,CAAC,CAAC,eAAe,CAAC,CACH,CAAC,cACnBtC,IAAA,CAACZ,WAAW,CAAC8F,IAAI,EAACC,EAAE,CAAErG,IAAK,CAACuF,EAAE,CAAC,kBAAkB,CAAAD,QAAA,CAC9C9B,CAAC,CAAC,gBAAgB,CAAC,CACJ,CAAC,EACR,CAAC,cAEdlC,KAAA,CAAChB,WAAW,EAAC6F,KAAK,cACd7E,KAAA,CAAAF,SAAA,EAAAkE,QAAA,eACEpE,IAAA,CAACR,OAAO,EAACwF,SAAS,CAAC,MAAM,CAAE,CAAC,CAC3B1C,CAAC,CAAC,uBAAuB,CAAC,EAC3B,CACH,CACDyC,EAAE,CAAC,2BAA2B,CAAAX,QAAA,eAE9BpE,IAAA,CAACZ,WAAW,CAAC8F,IAAI,EAACC,EAAE,CAAErG,IAAK,CAACuF,EAAE,CAAC,iBAAiB,CAAAD,QAAA,CAC7C9B,CAAC,CAAC,4BAA4B,CAAC,CAChB,CAAC,cACnBtC,IAAA,CAACZ,WAAW,CAAC8F,IAAI,EAACC,EAAE,CAAErG,IAAK,CAACuF,EAAE,CAAC,eAAe,CAAAD,QAAA,CAC3C9B,CAAC,CAAC,cAAc,CAAC,CACF,CAAC,cACnBtC,IAAA,CAACZ,WAAW,CAAC8F,IAAI,EAACC,EAAE,CAAErG,IAAK,CAACuF,EAAE,CAAC,wBAAwB,CAAAD,QAAA,CACpD9B,CAAC,CAAC,gBAAgB,CAAC,CACJ,CAAC,EACR,CAAC,cAEdlC,KAAA,CAAChB,WAAW,EAAC6F,KAAK,cACd7E,KAAA,CAAAF,SAAA,EAAAkE,QAAA,eACEpE,IAAA,CAACP,OAAO,EAACuF,SAAS,CAAC,MAAM,CAAE,CAAC,CAC3B1C,CAAC,CAAC,iBAAiB,CAAC,EACrB,CACH,CACDyC,EAAE,CAAC,qBAAqB,CAAAX,QAAA,eAExBpE,IAAA,CAACZ,WAAW,CAAC8F,IAAI,EAACC,EAAE,CAAErG,IAAK,CAACuF,EAAE,CAAC,qBAAqB,CAAAD,QAAA,CACjD9B,CAAC,CAAC,cAAc,CAAC,CACF,CAAC,cACnBtC,IAAA,CAACZ,WAAW,CAAC8F,IAAI,EAACC,EAAE,CAAErG,IAAK,CAACuF,EAAE,CAAC,sBAAsB,CAAAD,QAAA,CAClD9B,CAAC,CAAC,eAAe,CAAC,CACH,CAAC,EACR,CAAC,cAEdlC,KAAA,CAAChB,WAAW,EAAC6F,KAAK,cACd7E,KAAA,CAAAF,SAAA,EAAAkE,QAAA,eACEpE,IAAA,CAACN,UAAU,EAACsF,SAAS,CAAC,MAAM,CAAE,CAAC,CAC9B1C,CAAC,CAAC,mBAAmB,CAAC,EACvB,CACH,CACDyC,EAAE,CAAC,uBAAuB,CAAAX,QAAA,eAE1BpE,IAAA,CAACZ,WAAW,CAAC8F,IAAI,EAACC,EAAE,CAAErG,IAAK,CAACuF,EAAE,CAAC,wBAAwB,CAAAD,QAAA,CACpD9B,CAAC,CAAC,iBAAiB,CAAC,CACL,CAAC,cACnBtC,IAAA,CAACZ,WAAW,CAAC8F,IAAI,EAACC,EAAE,CAAErG,IAAK,CAACuF,EAAE,CAAC,sBAAsB,CAAAD,QAAA,CAClD9B,CAAC,CAAC,eAAe,CAAC,CACH,CAAC,cACnBtC,IAAA,CAACZ,WAAW,CAAC8F,IAAI,EAACC,EAAE,CAAErG,IAAK,CAACuF,EAAE,CAAC,4BAA4B,CAAAD,QAAA,CACxD9B,CAAC,CAAC,qBAAqB,CAAC,CACT,CAAC,EACR,CAAC,EACd,CACH,CAEAQ,IAAI,GAAK,OAAO,eACf1C,KAAA,CAAAF,SAAA,EAAAkE,QAAA,eAEEpE,IAAA,CAACZ,WAAW,EACV6F,KAAK,cACH7E,KAAA,CAAAF,SAAA,EAAAkE,QAAA,eACEpE,IAAA,CAACR,OAAO,EAACwF,SAAS,CAAC,MAAM,CAAE,CAAC,CAC3B1C,CAAC,CAAC,sBAAsB,CAAC,EAC1B,CACH,CACDyC,EAAE,CAAC,4BAA4B,CAAAX,QAAA,cAE/BpE,IAAA,CAACZ,WAAW,CAAC8F,IAAI,EAACC,EAAE,CAAErG,IAAK,CAACuF,EAAE,CAAC,cAAc,CAAAD,QAAA,CAC1C9B,CAAC,CAAC,eAAe,CAAC,CACH,CAAC,CACR,CAAC,cAEdtC,IAAA,CAACZ,WAAW,EAAC6F,KAAK,cACd7E,KAAA,CAAAF,SAAA,EAAAkE,QAAA,eACEpE,IAAA,CAACL,mBAAmB,EAACqF,SAAS,CAAC,MAAM,CAAE,CAAC,CACvC1C,CAAC,CAAC,mBAAmB,CAAC,EACvB,CACH,CACDyC,EAAE,CAAC,uBAAuB,CAAAX,QAAA,cAE1BpE,IAAA,CAACZ,WAAW,CAAC8F,IAAI,EAACC,EAAE,CAAErG,IAAK,CAACuF,EAAE,CAAC,eAAe,CAAAD,QAAA,CAC3C9B,CAAC,CAAC,gBAAgB,CAAC,CACJ,CAAC,CACR,CAAC,cAEdlC,KAAA,CAAChB,WAAW,EAAC6F,KAAK,cACd7E,KAAA,CAAAF,SAAA,EAAAkE,QAAA,eACEpE,IAAA,CAACJ,OAAO,EAACoF,SAAS,CAAC,MAAM,CAAE,CAAC,CAC3B1C,CAAC,CAAC,mBAAmB,CAAC,EACvB,CACH,CACDyC,EAAE,CAAC,uBAAuB,CAAAX,QAAA,eAE1BpE,IAAA,CAACZ,WAAW,CAAC8F,IAAI,EAACC,EAAE,CAAErG,IAAK,CAACuF,EAAE,CAAC,oBAAoB,CAAAD,QAAA,CAChD9B,CAAC,CAAC,aAAa,CAAC,CACD,CAAC,cACnBtC,IAAA,CAACZ,WAAW,CAAC8F,IAAI,EAACC,EAAE,CAAErG,IAAK,CAACuF,EAAE,CAAC,uBAAuB,CAAAD,QAAA,CACnD9B,CAAC,CAAC,gBAAgB,CAAC,CACJ,CAAC,EACR,CAAC,cAEdlC,KAAA,CAAChB,WAAW,EAAC6F,KAAK,cACd7E,KAAA,CAAAF,SAAA,EAAAkE,QAAA,eACEpE,IAAA,CAACH,aAAa,EAACmF,SAAS,CAAC,MAAM,CAAE,CAAC,CACjC1C,CAAC,CAAC,oBAAoB,CAAC,EACxB,CACH,CACDyC,EAAE,CAAC,wBAAwB,CAAAX,QAAA,eAE3BpE,IAAA,CAACZ,WAAW,CAAC8F,IAAI,EAACC,EAAE,CAAErG,IAAK,CAACuF,EAAE,CAAC,qBAAqB,CAAAD,QAAA,CACjD9B,CAAC,CAAC,cAAc,CAAC,CACF,CAAC,cACnBtC,IAAA,CAACZ,WAAW,CAAC8F,IAAI,EAACC,EAAE,CAAErG,IAAK,CAACuF,EAAE,CAAC,oBAAoB,CAAAD,QAAA,CAChD9B,CAAC,CAAC,aAAa,CAAC,CACD,CAAC,EACR,CAAC,cAEdlC,KAAA,CAAChB,WAAW,EAAC6F,KAAK,cACd7E,KAAA,CAAAF,SAAA,EAAAkE,QAAA,eACEpE,IAAA,CAACF,SAAS,EAACkF,SAAS,CAAC,MAAM,CAAE,CAAC,CAC7B1C,CAAC,CAAC,oBAAoB,CAAC,EACxB,CACH,CACDyC,EAAE,CAAC,wBAAwB,CAAAX,QAAA,eAE3BpE,IAAA,CAACZ,WAAW,CAAC8F,IAAI,EAACC,EAAE,CAAErG,IAAK,CAACuF,EAAE,CAAC,yBAAyB,CAAAD,QAAA,CACrD9B,CAAC,CAAC,aAAa,CAAC,CACD,CAAC,cACnBtC,IAAA,CAACZ,WAAW,CAAC8F,IAAI,EAACC,EAAE,CAAErG,IAAK,CAACuF,EAAE,CAAC,sBAAsB,CAAAD,QAAA,CAClD9B,CAAC,CAAC,eAAe,CAAC,CACH,CAAC,cACnBtC,IAAA,CAACZ,WAAW,CAAC8F,IAAI,EAACC,EAAE,CAAErG,IAAK,CAACuF,EAAE,CAAC,mBAAmB,CAAAD,QAAA,CAC/C9B,CAAC,CAAC,YAAY,CAAC,CACA,CAAC,EACR,CAAC,EACd,CACH,cAEDlC,KAAA,CAACjB,GAAG,CAACL,IAAI,EAACkF,IAAI,CAAC,IAAI,CAAAI,QAAA,eACjBpE,IAAA,CAACV,eAAe,EAAC0F,SAAS,CAAC,MAAM,CAAE,CAAC,CACnC1C,CAAC,CAAC,WAAW,CAAC,EACP,CAAC,EAER,CAAC,cACNtC,IAAA,CAACb,GAAG,EAAAiF,QAAA,cACFhE,KAAA,CAAChB,WAAW,EAAC6F,KAAK,CAAE3C,CAAC,CAAC,UAAU,CAAE,CAACyC,EAAE,CAAC,oBAAoB,CAAAX,QAAA,eACxDpE,IAAA,CAACZ,WAAW,CAAC8F,IAAI,EAACE,OAAO,CAAEA,CAAA,GAAM1B,cAAc,CAAC,IAAI,CAAE,CAAAU,QAAA,CAAC,oBAAG,CAAkB,CAAC,cAC7EpE,IAAA,CAACZ,WAAW,CAAC8F,IAAI,EAACE,OAAO,CAAEA,CAAA,GAAM1B,cAAc,CAAC,IAAI,CAAE,CAAAU,QAAA,CAAC,cAAE,CAAkB,CAAC,cAC5EpE,IAAA,CAACZ,WAAW,CAAC8F,IAAI,EAACE,OAAO,CAAEA,CAAA,GAAM1B,cAAc,CAAC,IAAI,CAAE,CAAAU,QAAA,CAAC,SAAO,CAAkB,CAAC,EACtE,CAAC,CACX,CAAC,EACS,CAAC,EACT,CAAC,CACN,CAAC,cAETpE,IAAA,CAACf,SAAS,EAAC+F,SAAS,CAAC,MAAM,CAAAZ,QAAA,cACzBpE,IAAA,CAACzB,QAAQ,EAAC8G,QAAQ,cAAErF,IAAA,QAAAoE,QAAA,CAAM9B,CAAC,CAAC,SAAS,CAAC,CAAM,CAAE,CAAA8B,QAAA,CAC3CxB,OAAO,cACN5C,IAAA,QAAAoE,QAAA,CAAM9B,CAAC,CAAC,uBAAuB,CAAC,CAAM,CAAC,CACrC,CAACE,QAAQ,cACXxC,IAAA,QAAKgF,SAAS,CAAC,oBAAoB,CAAAZ,QAAA,CAAE9B,CAAC,CAAC,2BAA2B,CAAC,CAAM,CAAC,cAE1ElC,KAAA,CAACxB,MAAM,EAAAwF,QAAA,eAELpE,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEvF,IAAA,CAACK,SAAS,GAAE,CAAE,CAAE,CAAC,cAG/CL,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAACyE,YAAY,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAGxEzE,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAACO,iBAAiB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACrFP,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAACQ,eAAe,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACnFR,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,SAAS,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAACS,aAAa,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC/ET,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,SAAS,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAACU,UAAU,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC5EV,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,KAAK,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAACW,aAAa,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC3EX,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAACY,WAAW,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC/EZ,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,SAAS,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAACa,OAAO,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACzEb,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,eAAe,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAACc,aAAa,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAGrFd,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAACe,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC/Ef,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,oBAAoB,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAACgB,OAAO,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACpFhB,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,uBAAuB,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAACiB,SAAS,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACzFjB,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAACkB,oBAAoB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC9FlB,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,cAAc,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAACmB,UAAU,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACjFnB,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,cAAc,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAACmC,oBAAoB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC3FnC,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,eAAe,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAACoC,iBAAiB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAGvFpC,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAACoB,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC/EpB,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAACqB,oBAAoB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC9FrB,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,eAAe,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAACsB,kBAAkB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC1FtB,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAACuB,qBAAqB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACjGvB,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,eAAe,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAACwB,kBAAkB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC1FxB,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAACyB,aAAa,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACvFzB,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,kBAAkB,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAAC2B,YAAY,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACvF3B,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,kBAAkB,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAAC0B,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACzF1B,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,qBAAqB,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAAC4B,WAAW,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACzF5B,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,sBAAsB,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAAC6B,YAAY,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC3F7B,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,wBAAwB,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAAC8B,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC/F9B,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,sBAAsB,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAAC+B,YAAY,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC3F/B,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,4BAA4B,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAACgC,kBAAkB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACvGhC,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAACiC,eAAe,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACzFjC,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,wBAAwB,CAACC,OAAO,cAAEvF,IAAA,CAACkE,WAAW,EAAAE,QAAA,cAACpE,IAAA,CAACkC,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAGjGlC,IAAA,CAACnB,KAAK,EAACyG,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEvF,IAAA,CAACjB,QAAQ,EAACsF,EAAE,CAAC,GAAG,CAACG,OAAO,MAAE,CAAE,CAAE,CAAC,EAClD,CACT,CACO,CAAC,CACF,CAAC,EACT,CAAC,CACI,CAAC,CAEjB,CAEA,cAAe,CAAAnC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}