import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const ManualDeposits = () => {
    const { t } = useTranslation();
    const [journals, setJournals] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchJournals = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // Fetch manual journals with maker, customer and currency information
            const { data, error } = await supabase
                .from('manual_journals')
                .select(`
                    id,
                    amount,
                    journal_type,
                    remark,
                    created_at,
                    maker_profiles (
                        domain,
                        users (
                            email
                        )
                    ),
                    customer_profiles (
                        real_name,
                        users (
                            email
                        )
                    ),
                    currencies (
                        code,
                        total_supply,
                        withdrawable
                    )
                `)
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Error fetching manual journals:', error);
            } else {
                setJournals(data);
            }
            setLoading(false);
        };

        fetchJournals();
    }, []);

    const getJournalTypeBadge = (type) => {
        switch (type) {
            case 'deposit':
                return <Badge bg="success">{t('deposit')}</Badge>;
            case 'withdrawal':
                return <Badge bg="danger">{t('withdrawal')}</Badge>;
            case 'adjustment':
                return <Badge bg="warning">{t('adjustment')}</Badge>;
            case 'bonus':
                return <Badge bg="info">{t('bonus')}</Badge>;
            case 'penalty':
                return <Badge bg="dark">{t('penalty')}</Badge>;
            default:
                return <Badge bg="secondary">{type || '-'}</Badge>;
        }
    };

    const formatAmount = (amount) => {
        if (!amount) return '0.000000';
        return parseFloat(amount).toFixed(6);
    };

    if (loading) {
        return <div>{t('loading_manual_deposits')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('manual_deposit')}</h2>
            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('journal_id')}</th>
                                        <th>{t('maker')}</th>
                                        <th>{t('customer')}</th>
                                        <th>{t('currency_code')}</th>
                                        <th>{t('amount')}</th>
                                        <th>{t('journal_type')}</th>
                                        <th>{t('remark')}</th>
                                        <th>{t('created_at')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {journals.length === 0 ? (
                                        <tr>
                                            <td colSpan="8" className="text-center">{t('no_manual_deposits_available')}</td>
                                        </tr>
                                    ) : (
                                        journals.map(journal => (
                                            <tr key={journal.id}>
                                                <td>{journal.id}</td>
                                                <td>
                                                    <div>
                                                        <div>{journal.maker_profiles?.domain || '-'}</div>
                                                        <small className="text-muted">
                                                            {journal.maker_profiles?.users?.email || '-'}
                                                        </small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div>
                                                        <div>{journal.customer_profiles?.real_name || '-'}</div>
                                                        <small className="text-muted">
                                                            {journal.customer_profiles?.users?.email || '-'}
                                                        </small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <Badge bg="primary">
                                                        {journal.currencies?.code || '-'}
                                                    </Badge>
                                                </td>
                                                <td className={journal.amount >= 0 ? 'text-success' : 'text-danger'}>
                                                    {journal.amount >= 0 ? '+' : ''}{formatAmount(journal.amount)}
                                                </td>
                                                <td>{getJournalTypeBadge(journal.journal_type)}</td>
                                                <td>
                                                    <div style={{ maxWidth: '200px', wordWrap: 'break-word' }}>
                                                        {journal.remark || '-'}
                                                    </div>
                                                </td>
                                                <td>{new Date(journal.created_at).toLocaleString()}</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default ManualDeposits;
